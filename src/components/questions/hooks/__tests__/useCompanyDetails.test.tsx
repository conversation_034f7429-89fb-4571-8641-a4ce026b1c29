import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, waitFor } from "@testing-library/react";
import { useCompanyDetails } from "../useCompanyDetails";
import { mockServerCompanyData } from "@/test/mocks/companyData";
import { AllTheProviders } from "@/test/utils";

// Mock the auth context
vi.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({
    user: {
      companyId: "test-company-id",
      id: "test-user-id",
    },
  }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock the company selection context
vi.mock("@/contexts/CompanySelectionContext", () => ({
  CompanySelectionProvider: ({ children }: { children: React.ReactNode }) =>
    children,
}));

// Mock the company info hook
vi.mock("@/integrations/legal-concierge/hooks/useCompanyInfo", () => ({
  useCompanyById: vi.fn(),
}));

import { useCompanyById } from "@/integrations/legal-concierge/hooks/useCompanyInfo";

describe("useCompanyDetails", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("useCompanyDetails", () => {
    it("should transform server data to legacy format", async () => {
      vi.mocked(useCompanyById).mockReturnValue({
        data: mockServerCompanyData,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCompanyDetails(), {
        wrapper: AllTheProviders,
      });

      await waitFor(() => {
        const companyDetails = result.current.companyDetails;
        expect(companyDetails).toBeDefined();

        if (companyDetails) {
          // Check legacy field mappings
          expect(companyDetails.companyName).toBe(mockServerCompanyData.name);
          expect(companyDetails.incorporatingToday).toBe(
            mockServerCompanyData.isIncorporatedToday
          );
          expect(companyDetails.authorizedShares).toBe(
            mockServerCompanyData.numberOfAuthorizedShares
          );
          expect(companyDetails.issuedShares).toBe(
            mockServerCompanyData.numberOfAuthorizedSharesToBeIssuedInitially
          );

          // Check address structure transformation
          expect(companyDetails.companyAddressStructured).toEqual({
            street1: mockServerCompanyData.streetAddress,
            street2: mockServerCompanyData.streetAddress2,
            city: mockServerCompanyData.city,
            state: mockServerCompanyData.state,
            zipCode: mockServerCompanyData.zipCode,
          });

          // Check officers transformation to directors
          expect(companyDetails.directors).toHaveLength(2);
          expect(companyDetails.directors?.[0]?.officerTitles).toContain("CEO");
          expect(companyDetails.directors?.[1]?.officerTitles).toContain(
            "President"
          );
          expect(companyDetails.directors?.[1]?.officerTitles).toContain(
            "Secretary"
          );
        }
      });
    });

    it("should handle vesting schedule transformation", async () => {
      vi.mocked(useCompanyById).mockReturnValue({
        data: mockServerCompanyData,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCompanyDetails(), {
        wrapper: AllTheProviders,
      });

      await waitFor(() => {
        const companyDetails = result.current.companyDetails;
        expect(companyDetails?.vestingInfo).toHaveLength(2);

        // Check vesting schedule transformation
        const vestingInfo = companyDetails?.vestingInfo;
        expect(vestingInfo?.[0]?.vestingSchedule).toBe("Standard"); // FOURYEARSONEYEARCLIFF -> Standard
        expect(vestingInfo?.[1]?.vestingSchedule).toBe("Monthly"); // FOURYEARSNOCLIFF -> Monthly

        // Check acceleration transformation
        expect(vestingInfo?.[0]?.acceleration).toBe("SingleTrigger"); // SINGLETRIGGER -> SingleTrigger
        expect(vestingInfo?.[1]?.acceleration).toBe("DoubleTrigger"); // DOUBLETRIGGER -> DoubleTrigger
      });
    });

    it("should handle tech assignments transformation", async () => {
      vi.mocked(useCompanyById).mockReturnValue({
        data: mockServerCompanyData,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCompanyDetails(), {
        wrapper: AllTheProviders,
      });

      await waitFor(() => {
        const companyDetails = result.current.companyDetails;
        expect(companyDetails?.founderTechAssignments).toHaveLength(1);

        const techAssignment = companyDetails?.founderTechAssignments?.[0];
        expect(techAssignment?.founderId).toBe("officer-1");
        expect(techAssignment?.founderName).toBe("John Doe");
        expect(techAssignment?.techItems).toHaveLength(1);
        expect(techAssignment?.techItems?.[0]?.description).toBe(
          "Core AI Algorithm"
        );
      });
    });

    it("should return null when no company data", async () => {
      vi.mocked(useCompanyById).mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCompanyDetails(), {
        wrapper: AllTheProviders,
      });

      expect(result.current.companyDetails).toBeNull();
      expect(result.current.loading).toBe(false);
    });
  });
});
