import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";

export const useOptionPlanDialog = (
  sharesReservedForIssuance: number,
  issuedAndOutstanding: number,
  authorizedShares: number
) => {
  const { toast } = useToast();

  const [additionalShares, setAdditionalShares] = useState<string>("");
  const [totalOptionShares, setTotalOptionShares] = useState<number>(0);
  const [boardApproved, setBoardApproved] = useState<boolean>(false);
  const [stockholdersApproved, setStockholdersApproved] =
    useState<boolean>(false);
  const [step, setStep] = useState<"initial" | "board" | "stockholders">(
    "initial"
  );
  const [exceedsAuthorized, setExceedsAuthorized] = useState<boolean>(false);
  const [requiredAuthorizedShares, setRequiredAuthorizedShares] =
    useState<number>(0);

  // Calculate total option shares when additional shares change
  useEffect(() => {
    const additionalSharesNum = additionalShares
      ? parseInt(additionalShares.replace(/,/g, ""), 10)
      : 0;
    if (!isNaN(additionalSharesNum)) {
      const newTotal = sharesReservedForIssuance + additionalSharesNum;
      setTotalOptionShares(newTotal);

      // Check if exceeds authorized shares
      const newTotalWithIssued = newTotal + issuedAndOutstanding;
      const exceedsLimit = newTotalWithIssued > authorizedShares;
      setExceedsAuthorized(exceedsLimit);

      if (exceedsLimit) {
        setRequiredAuthorizedShares(newTotalWithIssued);
      }
    } else {
      setTotalOptionShares(sharesReservedForIssuance);
      setExceedsAuthorized(false);
    }
  }, [
    additionalShares,
    sharesReservedForIssuance,
    issuedAndOutstanding,
    authorizedShares,
  ]);

  const handleAdditionalSharesChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    // Remove commas and ensure only numbers are entered
    const value = e.target.value.replace(/[^0-9]/g, "");
    // Format with commas
    const formattedValue = value ? parseInt(value, 10).toLocaleString() : "";
    setAdditionalShares(formattedValue);
  };

  const handleBoardApprovalComplete = () => {
    setBoardApproved(true);
    setStep("initial");
    toast({
      title: "Board Approval Process Initiated",
      description: "Email notifications have been sent to all board members.",
    });
  };

  const handleStockholderApprovalComplete = () => {
    setStockholdersApproved(true);
    setStep("initial");
    toast({
      title: "Stockholder Approval Process Initiated",
      description: "Email notifications have been sent to all stockholders.",
    });
  };

  const resetState = () => {
    setAdditionalShares("");
    setBoardApproved(false);
    setStockholdersApproved(false);
    setStep("initial");
  };

  return {
    additionalShares,
    totalOptionShares,
    boardApproved,
    stockholdersApproved,
    step,
    exceedsAuthorized,
    requiredAuthorizedShares,
    handleAdditionalSharesChange,
    handleBoardApprovalComplete,
    handleStockholderApprovalComplete,
    resetState,
    setStep,
  };
};
