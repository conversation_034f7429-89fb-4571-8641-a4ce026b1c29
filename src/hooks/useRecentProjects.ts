import { useState, useEffect } from "react";
import { useFormData } from "@/components/questions/hooks/formStorage/getFormData";
import { useFormProgress } from "@/components/questions/hooks/useFormProgress";

export interface RecentProject {
  id: string;
  title: string;
  type: "incorporation" | "questions" | "financing" | "maintenance";
  lastUpdated: Date;
  progress: number; // 0-100
  path: string;
}

export const useRecentProjects = () => {
  const [recentProjects, setRecentProjects] = useState<RecentProject[]>([]);
  const [loading, setLoading] = useState(true);

  // Get form data and progress from hooks instead of localStorage
  const { formData } = useFormData();
  const { progressPercentage } = useFormProgress(formData.currentStep);

  useEffect(() => {
    const fetchRecentProjects = async () => {
      setLoading(true);

      // Calculate actual progress based on form completion status
      const actualProgress = formData.isFormConfirmed
        ? 100
        : Math.min(Math.max(progressPercentage, 0), 100);

      // In a real app, this would be an API call to fetch recent projects
      // For demo purposes, we'll simulate with mock data
      const mockProjects: RecentProject[] = [
        {
          id: "1",
          title: "Acme Inc. Formation",
          type: "incorporation",
          lastUpdated: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          progress: 65,
          path: "/incorporation",
        },
        {
          id: "2",
          title: "Company Questionnaire",
          type: "questions",
          lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          progress: actualProgress, // Use the calculated progress
          path: "/questions",
        },
        {
          id: "3",
          title: "Series Seed Financing",
          type: "financing",
          lastUpdated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          progress: 80,
          path: "/term-sheet",
        },
      ];

      // Sort by lastUpdated (most recent first)
      const sortedProjects = mockProjects.sort(
        (a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime()
      );

      setRecentProjects(sortedProjects);
      setLoading(false);
    };

    fetchRecentProjects();
  }, [formData.isFormConfirmed, progressPercentage]);

  return {
    recentProjects,
    mostRecentProject: recentProjects.length > 0 ? recentProjects[0] : null,
    loading,
  };
};
