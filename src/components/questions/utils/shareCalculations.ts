import { Officer } from "../types/Officer";

// Format percentage to ensure it never exceeds 2 decimal places
export const formatPercentage = (value: number): number => {
  return Number(Number(value).toFixed(2));
};

// Calculate total percentage owned by directors
export const calculateTotalDirectorPercentage = (
  directors: Officer[]
): number => {
  if (directors.length === 0) return 0;

  const total = directors.reduce(
    (sum, director) => sum + director.stockOwnership,
    0
  );

  // Ensure we don't return -0 or similar floating point oddities
  return Number(total.toFixed(2));
};

// Calculate remaining percentage available
export const calculateRemainingPercentage = (directors: Officer[]): number => {
  if (directors.length === 0) return 100;

  const totalDirectorPercentage = calculateTotalDirectorPercentage(directors);
  const remaining = 100 - totalDirectorPercentage;

  // Ensure we don't return negative values due to rounding
  return Number(Math.max(0, remaining).toFixed(2));
};

// Calculate share amounts based on percentages and total shares
export const calculateShareAmounts = (
  directors: Officer[],
  issuedShares: number
): Officer[] => {
  if (issuedShares <= 0 || directors.length === 0) return directors;

  return directors.map((director) => {
    // Calculate shares amount exactly as percentage of issued shares
    // Use Math.floor to ensure we don't exceed total shares
    const amountOfShares = Math.floor(
      (director.stockOwnership / 100) * issuedShares
    );
    return {
      ...director,
      amountOfShares,
    };
  });
};

// Calculate stock option plan shares
export const calculateOptionPlanShares = (
  percentage: number,
  issuedShares: number
): number => {
  if (issuedShares <= 0 || percentage <= 0) return 0;

  // Use the same calculation method as for director shares for consistency
  return Math.floor((percentage / 100) * issuedShares);
};

// Normalize percentages to ensure they add up to targetSum
export const normalizePercentages = (
  directors: Officer[],
  targetSum: number
): Officer[] => {
  if (directors.length === 0) return directors;

  const currentSum = calculateTotalDirectorPercentage(directors);

  // If already at target (within rounding error margin), return unchanged
  if (Math.abs(currentSum - targetSum) < 0.01) return directors;

  // Calculate scaling factor to adjust all percentages proportionally
  const scaleFactor = targetSum / currentSum;

  // Adjust all percentages
  const adjusted = directors.map((director) => ({
    ...director,
    stockOwnership: formatPercentage(director.stockOwnership * scaleFactor),
  }));

  // Fix any remaining rounding errors by adjusting the first director
  const newSum = calculateTotalDirectorPercentage(adjusted);
  if (Math.abs(newSum - targetSum) > 0.01 && adjusted.length > 0) {
    const diff = targetSum - newSum;
    adjusted[0].stockOwnership = formatPercentage(
      adjusted[0].stockOwnership + diff
    );
  }

  return adjusted;
};
