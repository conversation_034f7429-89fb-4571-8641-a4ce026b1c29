import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { FileText } from "lucide-react";
import { QuestionnaireFormData } from "../../types";

interface ReviewConfirmationProps {
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  formData: QuestionnaireFormData;
}

const ReviewConfirmation: React.FC<ReviewConfirmationProps> = ({
  checked = false,
  onCheckedChange,
  formData
}) => {
  return (<>{!formData.isFormConfirmed && <div className="mt-8 p-6 border border-legal-200 rounded-lg bg-white">
    <div className="flex items-start mb-4">
      <div className="mr-3 bg-legal-50 p-2 rounded-full">
        <FileText className="h-5 w-5 text-legal-600" />
      </div>
      {formData.registerMode == 'getting-started' && <div>
        <h3 className="text-lg font-medium text-legal-900 mb-1">
          Document Generation
        </h3>
        <p className="text-sm text-legal-600 mb-4">
          After confirming, we'll generate your incorporation documents based
          on the information provided. These documents will include your
          Certificate of Incorporation, Bylaws, and other essential legal
          documents.
        </p>
      </div>}
      {formData.registerMode == 'register-existing' && <div>
        <h3 className="text-lg font-medium text-legal-900 mb-1">
          Document Upload
        </h3>
        <p className="text-sm text-legal-600 mb-4">
          After confirming, we'll proceed towards you existing documents upload. These documents will include your
          Certificate of Incorporation, Bylaws, and other essential legal
          documents.
        </p>
      </div>}
      
    </div>

    <div className="flex items-start space-x-3 border-t border-legal-100 pt-4">
      <Checkbox
        id="confirmation"
        checked={checked}
        onCheckedChange={(checked) => onCheckedChange?.(!!checked)}
        className="mt-1"
      />
      <div>
        <Label
          htmlFor="confirmation"
          className="text-base font-medium leading-normal text-legal-900"
        >
          I confirm that the information provided is accurate and complete.
        </Label>
        <p className="text-sm text-legal-600 mt-1">
          {formData.registerMode == 'geetting-started' 
          ? `By checking this box, you confirm that all information is correct
          and you're ready to generate your legal documents.` 
          :`By checking this box, you confirm that all information is correct
          and you're ready to upload your legal documents.`}
          
        </p>
      </div>
    </div>
  </div>}</>
    
  );
};

export default ReviewConfirmation;
