import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Button from "@/components/common/Button";
import { Search, CheckCircle } from "lucide-react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { FormData } from "../types";

interface CompanyNameStepProps {
  formData: FormData;
  updateFormData: (field: string, value: string) => void;
  nameAvailable: boolean | null;
  checkingName: boolean;
  checkCompanyName: () => void;
}

const CompanyNameStep: React.FC<CompanyNameStepProps> = ({
  formData,
  updateFormData,
  nameAvailable,
  checkingName,
  checkCompanyName,
}) => {
  return (
    <AnimatedTransition>
      <div className="space-y-6">
        <div className="space-y-3">
          <Label htmlFor="companyName">
            What is the preferred name of the Company?
          </Label>
          <div className="relative">
            <Input
              id="companyName"
              placeholder="Enter your company name"
              value={formData.companyName}
              onChange={(e) => updateFormData("companyName", e.target.value)}
              autoFocus
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2">
              <Button
                size="sm"
                variant="ghost"
                onClick={checkCompanyName}
                loading={checkingName}
                icon={<Search size={16} />}
                className="h-8"
              >
                Check Availability
              </Button>
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Ensure your preferred name is available and not already in use. Your
            company will be registered as "
            {formData.companyName || "[Company Name]"}, Inc."
          </p>

          {nameAvailable === true && (
            <div className="flex items-center text-green-600 text-sm mt-2">
              <CheckCircle size={16} className="mr-1" />
              <span>This name is available</span>
            </div>
          )}

          {nameAvailable === false && (
            <div className="text-red-500 text-sm mt-2">
              This name is already taken. Please try another name.
            </div>
          )}
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default CompanyNameStep;
