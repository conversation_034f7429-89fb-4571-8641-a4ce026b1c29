import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import ServiceProviderForm from "./ServiceProviderForm";
import { useServiceProviders } from "@/hooks/serviceProviders";

interface ContractorDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContractorDialog: React.FC<ContractorDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { addServiceProvider, companyEquity } = useServiceProviders();
  const [step, setStep] = useState<"form" | "confirmation">("form");
  const [formData, setFormData] = useState<any>(null);

  const handleSubmit = async (data: any) => {
    setFormData(data);
    setStep("confirmation");
  };

  const handleFinish = async () => {
    const success = await addServiceProvider(formData);
    if (success) {
      toast({
        title: "Contractor Onboarding Initiated",
        description:
          "The contractor will receive an email with onboarding documentation.",
      });
      resetAndClose();
    }
  };

  const resetAndClose = () => {
    setStep("form");
    setFormData(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && resetAndClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {step === "form"
              ? "Onboard Independent Contractor/Consultant"
              : "Confirm Contractor Details"}
          </DialogTitle>
          <DialogDescription>
            {step === "form"
              ? "Add a new contractor or consultant to your company and set up their equity grant."
              : "Review the information before finalizing."}
          </DialogDescription>
        </DialogHeader>

        {step === "form" ? (
          <ServiceProviderForm
            type="Contractor"
            onSubmit={handleSubmit}
            hasStockOptionPlan={!!companyEquity.stockOptionPlan}
          />
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium">Contractor Name</h3>
                <p className="text-sm">{formData.name}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Email</h3>
                <p className="text-sm">{formData.email}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Address</h3>
                <p className="text-sm">{formData.address}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Services</h3>
                <p className="text-sm">{formData.services}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Grant Type</h3>
                <p className="text-sm">{formData.grantType}</p>
              </div>
              {formData.grantType !== "None" && (
                <div>
                  <h3 className="text-sm font-medium">Shares</h3>
                  <p className="text-sm">{formData.shares}</p>
                </div>
              )}
              <div>
                <h3 className="text-sm font-medium">Start Date</h3>
                <p className="text-sm">
                  {formData.startDate.toLocaleDateString()}
                </p>
              </div>
              {formData.grantType !== "None" && (
                <div>
                  <h3 className="text-sm font-medium">Vesting Schedule</h3>
                  <p className="text-sm">
                    {formData.vestingSchedule === "Standard"
                      ? "2 years monthly"
                      : `Custom (${formData.vestingPeriod} months, ${formData.cliff} months cliff)`}
                  </p>
                </div>
              )}
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Compensation</h3>
                <p className="text-sm">
                  ${formData.compensation}{" "}
                  {formData.compensationPeriod.toLowerCase()}
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setStep("form")}>
                Back
              </Button>
              <Button onClick={handleFinish}>Finish</Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ContractorDialog;
