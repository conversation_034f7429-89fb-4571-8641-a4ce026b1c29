import React from "react";
import StateSelector from "./StateSelector";
import { Task, StateLink } from "./types";

interface ForeignQualificationContentProps {
  task: Task;
  selectedStates: StateLink[];
  stateLinks: Record<string, string>;
  onAddState: (state: StateLink) => Promise<void>;
  onRemoveState: (stateName: string) => Promise<void>;
}

const ForeignQualificationContent: React.FC<
  ForeignQualificationContentProps
> = ({ task, selectedStates, stateLinks, onAddState, onRemoveState }) => {
  if (task.id !== "foreignQualification") return null;

  return (
    <StateSelector
      selectedStates={selectedStates}
      stateLinks={stateLinks}
      onAddState={onAddState}
      onRemoveState={onRemoveState}
    />
  );
};

export default ForeignQualificationContent;
