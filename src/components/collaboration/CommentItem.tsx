import React, { useState } from "react";
import { Comment } from "./types";
import CommentHeader from "./comment/CommentHeader";
import CommentContent from "./comment/CommentContent";
import CommentFooter from "./comment/CommentFooter";
import CommentReplyForm from "./comment/CommentReplyForm";

interface CommentItemProps {
  comment: Comment;
  onResolve: (id: string) => void;
  isUserLoggedIn: boolean;
  isDashboard?: boolean;
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  onResolve,
  isUserLoggedIn,
  isDashboard = false,
}) => {
  const [isReplying, setIsReplying] = useState(false);

  return (
    <div
      className={`p-3 rounded-lg ${comment.resolved ? "bg-gray-50" : "bg-blue-50"}`}
    >
      <CommentHeader
        comment={comment}
        onResolve={onResolve}
        isUserLoggedIn={isUserLoggedIn}
        isDashboard={isDashboard}
      />

      <CommentContent content={comment.content} />

      <CommentFooter
        comment={comment}
        isUserLoggedIn={isUserLoggedIn}
        isReplying={isReplying}
        setIsReplying={setIsReplying}
        isDashboard={isDashboard}
      />

      {isReplying && !isDashboard && (
        <CommentReplyForm
          commentId={comment.id}
          position={comment.position}
          onCancel={() => setIsReplying(false)}
        />
      )}
    </div>
  );
};

export default CommentItem;
