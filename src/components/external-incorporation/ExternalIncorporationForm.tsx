import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/common/Card";
import Button from "@/components/common/Button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import {
  ExternalIncorporationFormData,
  ExternalIncorporationStep,
  initialFormData,
} from "./types";
import ProgressIndicator from "./ProgressIndicator";
import { EXTERNAL_INCORPORATION_STEPS } from "./constants";
import ExternalIncorporationStepRenderer from "./ExternalIncorporationStepRenderer";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { externalIncorporationService } from "@/services/externalIncorporationService";
import { supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";

const ExternalIncorporationForm: React.FC = () => {
  const [formData, setFormData] =
    useState<ExternalIncorporationFormData>(initialFormData);
  const { isAuthenticated, user, isMfaVerified } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false);
  const [confirmationChecked, setConfirmationChecked] = useState(false);
  const navigate = useNavigate();

  // useEffect(() => {
  //   // Check if user is authenticated
  //   const checkAuth = async () => {
  //     const {
  //       data: { session },
  //     } = await supabase.auth.getSession();
  //     if (!session) {
  //       toast.error("You must be logged in to proceed");
  //       navigate("/login");
  //     }
  //   };

  //   checkAuth();
  // }, [navigate]);

  const updateFormData = (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNext = () => {
    const currentStepIndex = EXTERNAL_INCORPORATION_STEPS.findIndex(
      (step) => step.id === formData.currentStep
    );

    if (currentStepIndex < EXTERNAL_INCORPORATION_STEPS.length - 1) {
      const nextStep = EXTERNAL_INCORPORATION_STEPS[currentStepIndex + 1].id;
      updateFormData("currentStep", nextStep);
    }
  };

  const handleBack = () => {
    const currentStepIndex = EXTERNAL_INCORPORATION_STEPS.findIndex(
      (step) => step.id === formData.currentStep
    );

    if (currentStepIndex > 0) {
      const prevStep = EXTERNAL_INCORPORATION_STEPS[currentStepIndex - 1].id;
      updateFormData("currentStep", prevStep);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);

    try {
      // Get authenticated user
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("You must be logged in to save this information");
        setLoading(false);
        return;
      }

      // Save the external incorporation
      const incorporation =
        await externalIncorporationService.saveExternalIncorporation({
          company_name: formData.companyName,
          company_address: formData.companyAddress,
          authorized_shares: formData.authorizedShares,
          issued_shares: formData.issuedShares,
          par_value_per_share: formData.parValuePerShare,
          stock_option_plan: formData.hasStockOptionPlan,
          stock_option_plan_percentage: formData.hasStockOptionPlan
            ? formData.stockOptionPlanPercentage
            : null,
          registered_agent_name: formData.registeredAgent.name,
          registered_agent_address: formData.registeredAgent.address,
          registered_agent_email: formData.registeredAgent.email,
          registered_agent_phone: formData.registeredAgent.phone,
          incorporation_date: formData.incorporationDate,
        });

      if (!incorporation) {
        throw new Error("Failed to save incorporation data");
      }

      // Save stakeholders
      for (const stakeholder of formData.stakeholders) {
        await externalIncorporationService.saveStakeholder({
          external_incorporation_id: incorporation.id!,
          name: stakeholder.name,
          is_director: stakeholder.isDirector,
          officer_titles: stakeholder.officerTitles,
          email: stakeholder.emailAddress,
          address: stakeholder.address,
          stock_ownership_percentage: stakeholder.stockOwnershipPercentage,
          shares_amount: stakeholder.sharesAmount,
          has_vesting: stakeholder.isVester,
          vesting_schedule: stakeholder.isVester
            ? stakeholder.vestingSchedule
            : null,
          acceleration: stakeholder.isVester ? stakeholder.acceleration : null,
        });
      }

      // Save tech contributions
      for (const contribution of formData.techContributions) {
        const stakeholder = formData.stakeholders.find(
          (s) => s.id === contribution.stakeholderId
        );

        if (stakeholder) {
          const savedStakeholder =
            await externalIncorporationService.getStakeholdersForIncorporation(
              incorporation.id!
            );
          const matchingStakeholder = savedStakeholder.find(
            (s) => s.name === stakeholder.name
          );

          if (matchingStakeholder) {
            await externalIncorporationService.saveTechContribution({
              external_incorporation_id: incorporation.id!,
              stakeholder_id: matchingStakeholder.id!,
              description: contribution.description,
            });
          }
        }
      }

      // Upload and save documents
      for (const doc of formData.documents) {
        if (doc.file && doc.isUploaded) {
          const fileUrl = await externalIncorporationService.uploadDocument(
            doc.file,
            user.id,
            `${doc.type}_${formData.companyName.replace(/\s+/g, "_")}_${Date.now()}`
          );

          if (fileUrl) {
            await externalIncorporationService.saveDocument({
              external_incorporation_id: incorporation.id!,
              document_type: doc.type,
              document_url: fileUrl,
              document_name: doc.name,
              is_required: doc.isRequired,
            });
          }
        }
      }

      // Mark as completed
      await externalIncorporationService.markIncorporationAsCompleted();

      toast.success("Your company information has been successfully saved!");

      // Redirect to post-incorporation
      navigate("/post-incorporation");
    } catch (error) {
      console.error("Error saving external incorporation data:", error);
      toast.error(
        "There was an error saving your information. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmationChange = (checked: boolean) => {
    setConfirmationChecked(checked);
  };

  const handleFinalSubmit = () => {
    if (formData.currentStep === "review" && !confirmationChecked) {
      toast.error("Please confirm that the information is correct");
      return;
    }

    setShowFinalConfirmation(true);
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <ProgressIndicator
          steps={EXTERNAL_INCORPORATION_STEPS}
          currentStep={formData.currentStep}
        />
        <CardTitle>
          {EXTERNAL_INCORPORATION_STEPS.find(
            (step) => step.id === formData.currentStep
          )?.label || ""}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ExternalIncorporationStepRenderer
          currentStep={formData.currentStep}
          formData={formData}
          updateFormData={updateFormData}
          confirmationChecked={confirmationChecked}
          onConfirmationChange={handleConfirmationChange}
        />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          icon={<ChevronLeft size={16} />}
          disabled={formData.currentStep === EXTERNAL_INCORPORATION_STEPS[0].id}
        >
          Back
        </Button>
        {formData.currentStep === "review" ? (
          <Button
            onClick={handleFinalSubmit}
            loading={loading}
            disabled={!confirmationChecked}
          >
            Complete Registration
          </Button>
        ) : (
          <Button
            onClick={handleNext}
            icon={<ChevronRight size={16} />}
            iconPosition="right"
          >
            Continue
          </Button>
        )}
      </CardFooter>

      {/* Final Confirmation Dialog */}
      <Dialog
        open={showFinalConfirmation}
        onOpenChange={setShowFinalConfirmation}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              Complete External Incorporation Registration
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to complete this process? Once submitted,
              you'll be directed to the post-incorporation checklist.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowFinalConfirmation(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowFinalConfirmation(false);
                handleSubmit();
              }}
            >
              Yes, complete registration
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ExternalIncorporationForm;
