import React, { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  ServiceProviderType,
  GrantType,
  OptionType,
  VestingScheduleType,
} from "@/types/serviceProvider";

interface ServiceProviderFormProps {
  type: ServiceProviderType;
  onSubmit: (data: any) => void;
  hasStockOptionPlan: boolean;
}

const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  address: z.string().min(5, { message: "Please enter a valid address." }),
  services: z.string().optional(),
  grantType: z.enum(["Option", "Restricted Stock", "None"]),
  optionType: z.enum(["Statutory", "Non-Statutory"]).optional(),
  shares: z.number().optional(),
  startDate: z.date(),
  vestingSchedule: z.enum(["Standard", "Custom"]),
  vestingPeriod: z.number().optional(),
  cliff: z.number().optional(),
  compensation: z.number().optional(),
  compensationPeriod: z
    .enum(["Annually", "Monthly", "Weekly", "Hourly"])
    .optional(),
});

const ServiceProviderForm: React.FC<ServiceProviderFormProps> = ({
  type,
  onSubmit,
  hasStockOptionPlan,
}) => {
  const [isCustomVesting, setIsCustomVesting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      address: "",
      services: type !== "Employee" ? "" : undefined,
      grantType: "Option",
      optionType: type !== "Employee" ? "Non-Statutory" : "Statutory",
      shares: undefined,
      startDate: new Date(),
      vestingSchedule: "Standard",
      vestingPeriod: undefined,
      cliff: undefined,
      compensation: undefined,
      compensationPeriod: type === "Employee" ? "Annually" : "Hourly",
    },
  });

  const grantType = form.watch("grantType");
  const vestingSchedule = form.watch("vestingSchedule");

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    // Add the service provider type
    const formData = {
      ...values,
      type,
    };
    onSubmit(formData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{type} Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter full name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter full address" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {type !== "Employee" && (
          <FormField
            control={form.control}
            name="services"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Services to be provided</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the services to be provided"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="grantType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type of Grant</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Option" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Option{" "}
                      {type !== "Employee" &&
                        "(Default to Non-Statutory Option Grant)"}
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Restricted Stock" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Restricted Stock Grant
                      {hasStockOptionPlan && (
                        <span className="text-sm text-gray-500 ml-2">
                          (These shares will be issued from the Stock Option
                          Plan)
                        </span>
                      )}
                    </FormLabel>
                  </FormItem>
                  {type !== "Employee" && (
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="None" />
                      </FormControl>
                      <FormLabel className="font-normal">None</FormLabel>
                    </FormItem>
                  )}
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {grantType !== "None" && (
          <FormField
            control={form.control}
            name="shares"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of Shares</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter number of shares"
                    {...field}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="startDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Start Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date < new Date("1900-01-01") ||
                      date >
                        new Date(
                          new Date().setFullYear(new Date().getFullYear() + 1)
                        )
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {grantType !== "None" && (
          <FormField
            control={form.control}
            name="vestingSchedule"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Vesting Schedule</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      field.onChange(value);
                      setIsCustomVesting(value === "Custom");
                    }}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="Standard" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Standard (
                        {type === "Employee"
                          ? "4 years with 1 year cliff"
                          : "2 years, monthly vesting"}
                        )
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="Custom" />
                      </FormControl>
                      <FormLabel className="font-normal">Custom</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {grantType !== "None" && isCustomVesting && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="vestingPeriod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Vesting Period (months)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter months"
                      {...field}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseInt(e.target.value) : undefined
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cliff"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cliff (months)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter months"
                      {...field}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseInt(e.target.value) : undefined
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <FormField
          control={form.control}
          name="compensation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Compensation</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter amount"
                  {...field}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="compensationPeriod"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Compensation Period</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-wrap gap-4"
                >
                  {(type === "Employee"
                    ? ["Annually"]
                    : ["Hourly", "Weekly", "Monthly"]
                  ).map((period) => (
                    <FormItem
                      key={period}
                      className="flex items-center space-x-2 space-y-0"
                    >
                      <FormControl>
                        <RadioGroupItem value={period} />
                      </FormControl>
                      <FormLabel className="font-normal">{period}</FormLabel>
                    </FormItem>
                  ))}
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="submit">Save</Button>
        </div>
      </form>
    </Form>
  );
};

export default ServiceProviderForm;
