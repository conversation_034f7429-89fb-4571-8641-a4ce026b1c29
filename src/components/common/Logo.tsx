import React from "react";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "icon";
  onClick?: () => void;
}

const Logo: React.FC<LogoProps> = ({
  className,
  size = "md",
  variant = "default",
  onClick,
}) => {
  const sizeClasses = {
    sm: variant === "default" ? "text-xl" : "text-2xl",
    md: variant === "default" ? "text-2xl" : "text-3xl",
    lg: variant === "default" ? "text-3xl" : "text-4xl",
  };

  return (
    <Link
      to="/"
      className={cn(
        "font-display font-bold text-legal-800 flex items-center",
        sizeClasses[size],
        className
      )}
      onClick={onClick}
    >
      <div className="relative mr-2">
        <div className="absolute inset-0 bg-legal-500 rounded-md blur-[2px] opacity-75"></div>
        <div className="relative bg-white text-legal-500 font-semibold rounded-md px-1 py-0.5 border border-legal-200">
          LC
        </div>
      </div>
      {variant === "default" && (
        <span className="text-gray-900">
          Legal<span className="text-legal-500">Concierge</span>
        </span>
      )}
    </Link>
  );
};

export default Logo;
