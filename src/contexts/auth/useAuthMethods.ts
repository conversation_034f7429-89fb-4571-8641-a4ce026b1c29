import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { AuthState, UserProfile } from "./types";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "@/integrations/legal-concierge/client";

export const useAuthMethods = (
  state: AuthState,
  setAuthState: (state: Partial<AuthState>) => void
) => {

  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const login = async (email: string, password: string): Promise<void> => {
    try {
      const { error } = await new APIClient().login({ email, password });

      if (error) {
        toast.error(error);
        return Promise.reject(error);
      }

      // Don't set needsMfaVerification here, let useAuthState handle it
      // Just set isAuthenticated flag
      setAuthState({
        isMfaVerified: false,
      });

      // Don't navigate here, let the Login component handle navigation
      // This ensures the proper auth flow

      toast.success("A code has been sent to your email");
      return Promise.resolve();
    } catch (error) {
      toast.error("Login failed. Please try again.");
      return Promise.reject(error);
    }
  };

  const signUp = async (
    email: string,
    phone: string,
    password: string,
    fullName: string,
    token: string
  ): Promise<void> => {
    try {
      if (!fullName.trim()) {
        toast.error("Full name is required");
        return Promise.reject(new Error("Full name is required"));
      }

      const { data, error } = await new APIClient().register({
        email,
        phoneNumber: phone,
        password,
        fullName,
        token,
      });

      if (error) {
        toast.error(error);
        return Promise.reject(error);
      }

      toast.success("Registration successful! Please login to continue.");

      // Force navigation to login page
      window.location.href = "/login";
      return Promise.resolve();
    } catch (error) {
      toast.error("Registration failed. Please try again.");
      return Promise.reject(error);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      const { error } = await new APIClient().logout();

      if (error) {
        toast.error(error.message);
        return Promise.reject(error);
      }

      queryClient.clear();

      setAuthState({
        isMfaVerified: false,
        userEmail: null,
        user: null,
        isAuthenticated: false,
      });

      toast.success("You have been logged out");
      navigate("/login");
    } catch (error) {
      toast.error("Logout failed. Please try again.");
      return Promise.reject(error);
    }
  };

  // Implement MFA verification with Supabase
  // For this example, we're still simulating since Supabase doesn't have built-in MFA
  const verifyMfa = async (email: string, code: string): Promise<boolean> => {
    // Check that the code is 6 digits
    if (!/^\d{6}$/.test(code)) {
      return false;
    }

    // In a real implementation, we'd verify the code with a proper MFA system
    // For a demo, we'll accept any 6-digit code
    const { data, error } = await new APIClient().verifyOtp({
      email: email,
      otp: code,
    });
    // Set MFA as verified and no longer needed
    setAuthState({
      isMfaVerified: error == null,
      isAuthenticated: error == null,
      loading: false,
    });

    return error == null;
  };

  const updateProfile = async (data: Partial<UserProfile>): Promise<void> => {
    if (!state.user?.id) {
      toast.error("You must be logged in to update your profile");
      return Promise.reject(new Error("Not authenticated"));
    }

    try {
      const { error } = await supabase
        .from("profiles")
        .update(data)
        .eq("id", state.user.id);

      if (error) {
        toast.error(`Error updating profile: ${error.message}`);
        return Promise.reject(error);
      }

      // Update local state with new profile data
      setAuthState({});

      toast.success("Profile updated successfully");
      return Promise.resolve();
    } catch (error) {
      toast.error("Failed to update profile");
      return Promise.reject(error);
    }
  };

  const forgotPassword = async (email: string): Promise<void> => {
    try {
      if (!email.trim()) {
        toast.error("Email is required");
        return Promise.reject(new Error("Email is required"));
      }

      const { error } = await new APIClient().forgotPassword({ email });

      if (error) {
        toast.error(error);
        return Promise.reject(error);
      }

      toast.success("Password reset instructions sent to your email");
      return Promise.resolve();
    } catch (error) {
      toast.error("Failed to send password reset email. Please try again.");
      return Promise.reject(error);
    }
  };

  const resetPassword = async (
    email: string,
    token: string,
    newPassword: string
  ): Promise<void> => {
    try {
      if (!email.trim() || !token.trim() || !newPassword.trim()) {
        toast.error("All fields are required");
        return Promise.reject(new Error("All fields are required"));
      }

      const { error } = await new APIClient().resetPassword({
        email,
        token,
        newPassword,
      });

      if (error) {
        toast.error(error);
        return Promise.reject(error);
      }

      toast.success("Password has been reset successfully. Please login.");
      navigate("/login");
      return Promise.resolve();
    } catch (error) {
      toast.error("Failed to reset password. Please try again.");
      return Promise.reject(error);
    }
  };

  return {
    login,
    signUp,
    logout,
    verifyMfa,
    forgotPassword,
    resetPassword,
    updateProfile,
  };
};
