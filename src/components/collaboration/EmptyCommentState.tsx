import React from "react";
import { MessageSquare } from "lucide-react";

interface EmptyCommentStateProps {
  showResolved: boolean;
}

const EmptyCommentState: React.FC<EmptyCommentStateProps> = ({
  showResolved,
}) => {
  return (
    <div className="text-center py-6 flex-1 flex flex-col justify-center">
      <MessageSquare className="h-8 w-8 text-gray-400 mx-auto" />
      <h3 className="mt-2 text-gray-900 font-medium">No active comments</h3>
      <p className="text-gray-500 text-sm">
        {showResolved
          ? "No comments have been made yet"
          : "No unresolved comments"}
      </p>
    </div>
  );
};

export default EmptyCommentState;
