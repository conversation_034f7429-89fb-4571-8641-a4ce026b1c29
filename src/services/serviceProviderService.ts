import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { ServiceProvider } from "@/types/serviceProvider";

export const serviceProviderService = {
  async getServiceProvidersForCompany(
    companyId: string
  ): Promise<ServiceProvider[]> {
    try {
      const { data, error } = await supabase
        .from("service_providers")
        .select("*")
        .eq("company_id", companyId);

      if (error) {
        console.error("Error fetching service providers:", error);
        return [];
      }

      // Map the DB format to the app's ServiceProvider type
      return data.map((item) => ({
        id: item.id,
        name: item.name,
        email: item.email,
        address: item.address || "",
        services: item.services,
        grantType: item.grant_type as any,
        optionType: item.option_type as any,
        shares: item.shares,
        startDate: new Date(item.start_date),
        vestingSchedule: item.vesting_schedule as any,
        vestingPeriod: item.vesting_period,
        cliff: item.cliff,
        compensation: item.compensation,
        compensationPeriod: item.compensation_period as any,
        type: item.type as any,
        status: "Active", // Default status since DB doesn't track it
        company_id: item.company_id,
      }));
    } catch (error) {
      console.error("Error in getServiceProviders:", error);
      return [];
    }
  },

  async addServiceProvider(
    provider: ServiceProvider
  ): Promise<ServiceProvider | null> {
    try {
      const userId = (await supabase.auth.getUser()).data.user?.id;

      if (!userId) {
        toast.error("You must be logged in to add a service provider");
        return null;
      }

      // Convert from app type to DB format
      const dbProvider = {
        user_id: userId,
        company_id: provider.company_id,
        name: provider.name,
        email: provider.email,
        address: provider.address,
        services: provider.services,
        grant_type: provider.grantType,
        option_type: provider.optionType,
        shares: provider.shares,
        start_date: provider.startDate.toISOString().split("T")[0],
        vesting_schedule: provider.vestingSchedule,
        vesting_period: provider.vestingPeriod,
        cliff: provider.cliff,
        compensation: provider.compensation,
        compensation_period: provider.compensationPeriod,
        type: provider.type,
      };

      const { data, error } = await supabase
        .from("service_providers")
        .insert(dbProvider)
        .select();

      if (error) {
        console.error("Error adding service provider:", error);
        toast.error("Failed to add service provider");
        return null;
      }

      toast.success("Service provider added successfully");

      // Map the response back to app type
      const result = data[0];
      return {
        id: result.id,
        name: result.name,
        email: result.email,
        address: result.address || "",
        services: result.services,
        grantType: result.grant_type as any,
        optionType: result.option_type as any,
        shares: result.shares,
        startDate: new Date(result.start_date),
        vestingSchedule: result.vesting_schedule as any,
        vestingPeriod: result.vesting_period,
        cliff: result.cliff,
        compensation: result.compensation,
        compensationPeriod: result.compensation_period as any,
        type: result.type as any,
        status: "Active",
        company_id: result.company_id,
      };
    } catch (error) {
      console.error("Error in addServiceProvider:", error);
      toast.error("An error occurred while adding the service provider");
      return null;
    }
  },

  async updateServiceProvider(
    id: string,
    updates: Partial<ServiceProvider>
  ): Promise<ServiceProvider | null> {
    try {
      // Convert app updates to DB format
      const dbUpdates: any = {};

      if (updates.name !== undefined) dbUpdates.name = updates.name;
      if (updates.email !== undefined) dbUpdates.email = updates.email;
      if (updates.address !== undefined) dbUpdates.address = updates.address;
      if (updates.services !== undefined) dbUpdates.services = updates.services;
      if (updates.grantType !== undefined)
        dbUpdates.grant_type = updates.grantType;
      if (updates.optionType !== undefined)
        dbUpdates.option_type = updates.optionType;
      if (updates.shares !== undefined) dbUpdates.shares = updates.shares;
      if (updates.startDate !== undefined)
        dbUpdates.start_date = updates.startDate.toISOString().split("T")[0];
      if (updates.vestingSchedule !== undefined)
        dbUpdates.vesting_schedule = updates.vestingSchedule;
      if (updates.vestingPeriod !== undefined)
        dbUpdates.vesting_period = updates.vestingPeriod;
      if (updates.cliff !== undefined) dbUpdates.cliff = updates.cliff;
      if (updates.compensation !== undefined)
        dbUpdates.compensation = updates.compensation;
      if (updates.compensationPeriod !== undefined)
        dbUpdates.compensation_period = updates.compensationPeriod;
      if (updates.type !== undefined) dbUpdates.type = updates.type;
      if (updates.company_id !== undefined)
        dbUpdates.company_id = updates.company_id;

      const { data, error } = await supabase
        .from("service_providers")
        .update(dbUpdates)
        .eq("id", id)
        .select();

      if (error) {
        console.error("Error updating service provider:", error);
        toast.error("Failed to update service provider");
        return null;
      }

      toast.success("Service provider updated successfully");

      // Map the response back to app type
      const result = data[0];
      return {
        id: result.id,
        name: result.name,
        email: result.email,
        address: result.address || "",
        services: result.services,
        grantType: result.grant_type as any,
        optionType: result.option_type as any,
        shares: result.shares,
        startDate: new Date(result.start_date),
        vestingSchedule: result.vesting_schedule as any,
        vestingPeriod: result.vesting_period,
        cliff: result.cliff,
        compensation: result.compensation,
        compensationPeriod: result.compensation_period as any,
        type: result.type as any,
        status: "Active",
        company_id: result.company_id,
      };
    } catch (error) {
      console.error("Error in updateServiceProvider:", error);
      toast.error("An error occurred while updating the service provider");
      return null;
    }
  },

  async deleteServiceProvider(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("service_providers")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting service provider:", error);
        toast.error("Failed to delete service provider");
        return false;
      }

      toast.success("Service provider deleted successfully");
      return true;
    } catch (error) {
      console.error("Error in deleteServiceProvider:", error);
      toast.error("An error occurred while deleting the service provider");
      return false;
    }
  },
};
