import { useState, useEffect } from "react";
import { isAfter, addDays } from "date-fns";

interface FormErrors {
  date?: string;
  time?: string;
  purpose?: string;
  agenda?: string;
}

const useBoardMeetingForm = (isOpen: boolean) => {
  const [meetingDate, setMeetingDate] = useState<Date | undefined>(undefined);
  const [meetingTime, setMeetingTime] = useState<string>("09:00");
  const [meetingTimezone, setMeetingTimezone] =
    useState<string>("America/New_York");
  const [meetingPurpose, setMeetingPurpose] = useState<string>("");
  const [agendaFile, setAgendaFile] = useState<File | null>(null);
  const [boardMaterials, setBoardMaterials] = useState<File[]>([]);
  const [scheduledMeeting, setScheduledMeeting] = useState<Date | null>(null);
  const [formErrors, setFormErrors] = useState<FormErrors>({});

  // Clear form when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  const resetForm = () => {
    setMeetingDate(undefined);
    setMeetingTime("09:00");
    setMeetingTimezone("America/New_York");
    setMeetingPurpose("");
    setAgendaFile(null);
    setBoardMaterials([]);
    setFormErrors({});
  };

  const handleTimeChange = (value: string) => {
    setMeetingTime(value);
  };

  const handleTimezoneChange = (value: string) => {
    setMeetingTimezone(value);
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // Check if meeting date is at least 48 hours in the future
    if (!meetingDate) {
      errors.date = "Meeting date is required";
    } else {
      const now = new Date();
      const minimumDate = addDays(now, 2);

      // Combine date and time for comparison
      const selectedDateTime = new Date(meetingDate);
      const [hours, minutes] = meetingTime.split(":").map(Number);
      selectedDateTime.setHours(hours, minutes, 0, 0);

      if (isAfter(minimumDate, selectedDateTime)) {
        errors.date =
          "The meeting must be scheduled at least 48 hours after today";
      }
    }

    if (!meetingPurpose.trim()) {
      errors.purpose = "Meeting purpose is required";
    }

    if (!agendaFile) {
      errors.agenda = "Agenda document is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAgendaFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setAgendaFile(e.target.files[0]);
      // Clear any existing error
      setFormErrors((prev) => ({ ...prev, agenda: undefined }));
    }
  };

  const handleBoardMaterialsChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files) {
      const fileArray = Array.from(e.target.files);
      setBoardMaterials((prev) => [...prev, ...fileArray]);
    }
  };

  const handleScheduleMeeting = () => {
    if (!validateForm()) return;

    // Combine date and time
    if (meetingDate) {
      const scheduledDateTime = new Date(meetingDate);
      const [hours, minutes] = meetingTime.split(":").map(Number);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      // Set the scheduled meeting date
      setScheduledMeeting(scheduledDateTime);

      // Return the scheduled date for toast notification
      return scheduledDateTime;
    }
    return null;
  };

  const handleCancelMeeting = () => {
    // In a real app, you would also cancel the calendar invite here
    setScheduledMeeting(null);
  };

  return {
    meetingDate,
    setMeetingDate,
    meetingTime,
    meetingTimezone,
    meetingPurpose,
    setMeetingPurpose,
    agendaFile,
    boardMaterials,
    scheduledMeeting,
    formErrors,
    handleTimeChange,
    handleTimezoneChange,
    handleAgendaFileChange,
    handleBoardMaterialsChange,
    handleScheduleMeeting,
    handleCancelMeeting,
  };
};

export default useBoardMeetingForm;
