import React from "react";
import { formatDistanceToNow } from "date-fns";
import { Check } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Comment } from "../types";

interface CommentHeaderProps {
  comment: Comment;
  onResolve: (id: string) => void;
  isUserLoggedIn: boolean;
  isDashboard?: boolean;
}

const CommentHeader: React.FC<CommentHeaderProps> = ({
  comment,
  onResolve,
  isUserLoggedIn,
  isDashboard = false,
}) => {
  const initials = comment.userName
    ? comment.userName
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2) || comment.userName.slice(0, 2).toUpperCase()
    : "AN";

  const formatTimestamp = (timestamp: Date) => {
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return "Just now";
      }
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.error("Error formatting timestamp:", error);
      return "Just now";
    }
  };

  return (
    <div className="flex justify-between items-start">
      <div className="flex items-center gap-2">
        {/* <Avatar className="h-6 w-6">
          <AvatarFallback className="text-xs">{initials}</AvatarFallback>
        </Avatar> */}
        <span className="font-medium text-sm">{comment.userName || "Anonymous"}</span>
        <span className="text-xs text-gray-500">
          {formatTimestamp(comment.timestamp)}
        </span>
      </div>

      {!comment.resolved && isUserLoggedIn && !isDashboard && (
        <Button
          variant="ghost"
          size="sm"
          className="h-6 px-2"
          onClick={() => onResolve(comment.id)}
        >
          <Check className="h-3 w-3 mr-1" />
          <span className="text-xs">Resolve</span>
        </Button>
      )}
    </div>
  );
};

export default CommentHeader;
