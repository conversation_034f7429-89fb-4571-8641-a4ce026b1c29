"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateDocumentComment } from "@/integrations/legal-concierge/hooks/useDocumentComments";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface CommentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (
    commentId: string,
    content: string,
    author: string
  ) => Promise<void>;
  selectedText?: string;
  documentId?: string;
}

export const CommentDialog = ({
  isOpen,
  onClose,
  onSubmit,
  selectedText,
  documentId,
}: CommentDialogProps) => {
  const [content, setContent] = useState("");
  const { user } = useAuth();
  const createCommentMutation = useCreateDocumentComment();

  const isSubmitting = createCommentMutation.isPending;
  console.log({ documentId });
  const handleSubmit = async () => {
    if (!content.trim() || !user?.companyId || !documentId) {
      if (!user?.companyId) toast.error("User company not found");
      if (!documentId) toast.error("Document ID not provided");
      return;
    }

    try {
      // Create comment via React Query mutation
      const response = await createCommentMutation.mutateAsync({
        companyId: user.companyId,
        documentIdentifier: documentId,
        comment: {
          comment: content.trim(),
          resolved: false,
        },
      });
      // Use server-generated ID for the comment
      const serverCommentId = response.id;
      const author = user.fullName || user.email?.split("@")[0] || "You";

      // Call the onSubmit callback with server ID
      await onSubmit(serverCommentId, content.trim(), author);

      setContent("");
      onClose();
      toast.success("Comment added successfully");
    } catch (error) {
      console.error("Error creating comment:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create comment"
      );
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setContent("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Comment</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {selectedText && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium text-gray-700">
                Selected text:
              </Label>
              <p className="text-sm text-gray-600 mt-1 italic">
                "{selectedText}"
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="comment">Comment</Label>
            <Textarea
              id="comment"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Write your comment here..."
              className="w-full min-h-[100px] resize-none"
              autoFocus
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!content.trim() || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Add Comment"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
