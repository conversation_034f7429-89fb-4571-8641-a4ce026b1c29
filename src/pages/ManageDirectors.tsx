import React from "react";
import Layout from "@/components/Layout";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import DirectorsManagementForm from "@/components/directors/DirectorsManagementForm";

const ManageDirectors: React.FC = () => {
  return (
    <Layout>
      <div className="container py-12 px-4 md:px-8">
        <AnimatedTransition className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Manage Board of Directors
            </h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Add, remove, or update your company's board of directors.
            </p>
          </div>

          <DirectorsManagementForm />
        </AnimatedTransition>
      </div>
    </Layout>
  );
};

export default ManageDirectors;
