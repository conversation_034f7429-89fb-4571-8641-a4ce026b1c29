/* Reset the container styling to match original */
#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
  text-align: inherit;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.tiptap {
  /* Default font size in points for print compatibility */
  font-size: 11pt;
  line-height: 1.5;
  font-family: "Times New Roman", serif;
  color: #1a202c;
  background: #ffffff;

  /* Ensure headings use proper pt sizes */
  h1 {
    font-size: 24pt;
  }
  h2 {
    font-size: 18pt;
  }
  h3 {
    font-size: 16pt;
  }
  h4 {
    font-size: 14pt;
  }
  h5 {
    font-size: 12pt;
  }
  h6 {
    font-size: 10pt;
  }

  /* Link styles */
  a {
    @apply text-blue-600;
    cursor: pointer;

    &:hover {
      @apply underline underline-offset-4;
    }
  }

  /* Image specific styling */
  img {
    display: block;
    height: auto;
    margin: 1.5rem 0;
    max-width: 100%;

    &.ProseMirror-selectednode {
      outline: 3px solid var(--primary);
    }
  }

  /* Table-specific styling */
  table {
    border-collapse: collapse;
    margin: 0;
    overflow: hidden;
    table-layout: fixed;
    width: 100%;

    td,
    th {
      border: 1px solid #d1d5db;
      box-sizing: border-box;
      min-width: 1em;
      padding: 8px 12px;
      position: relative;
      vertical-align: top;
      background: #ffffff;

      > * {
        margin-bottom: 0;
      }
    }

    th {
      background-color: #f8fafc;
      font-weight: 600;
      text-align: left;
      color: #374151;
      border-bottom: 2px solid #d1d5db;
    }

    .selectedCell:after {
      background: var(#959596);
      content: "";
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      pointer-events: none;
      position: absolute;
      z-index: 2;
    }

    .column-resize-handle {
      background-color: var(--primary);
      bottom: -2px;
      pointer-events: none;
      position: absolute;
      right: -2px;
      top: 0;
      width: 4px;
    }
  }

  .tableWrapper {
    margin: 1.5rem 0;
    overflow-x: auto;
  }

  &.resize-cursor {
    cursor: ew-resize;
    cursor: col-resize;
  }

  /* Heading styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.1;
    text-wrap: pretty;
  }

  /* List styles */
  ul,
  ol {
    padding: 0 1rem;
    margin: 0.4rem 1rem 0.4rem 0.4rem;
  }

  ul li {
    list-style-type: disc;

    p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  ol li {
    list-style-type: decimal;

    p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  /* Task list specific styles */
  ul[data-type="taskList"] {
    list-style: none;
    margin-left: 0;
    padding: 0;

    li {
      align-items: flex-start;
      display: flex;

      > label {
        flex: 0 0 auto;
        margin-right: 0.5rem;
        user-select: none;
      }

      > div {
        flex: 1 1 auto;
      }
    }

    input[type="checkbox"] {
      cursor: pointer;
    }

    ul[data-type="taskList"] {
      margin: 0;
    }
  }

  /* Paragraph styles - matching export defaults */
  p {
    line-height: 1;
    margin: 0;
  }

  /* Handle TipTap's trailing breaks and empty paragraphs */
  p:empty {
    min-height: 1em;
  }

  br.ProseMirror-trailingBreak {
    display: block;
    content: "";
    margin-bottom: 1em;
  }

  p br.ProseMirror-trailingBreak:only-child {
    display: block;
    height: 1.5em;
  }

  /* Comment highlighting styles */
  .comment-highlight {
    background-color: #fef3c7;
    border-radius: 2px;
    padding: 1px 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
  }

  .comment-highlight:hover {
    background-color: #fde68a;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .comment-highlight.active {
    background-color: #f59e0b;
    color: white;
    box-shadow: 0 2px 6px rgba(245, 158, 11, 0.3);
  }

  /* Comment indicator for better visibility */
  .comment-highlight::after {
    content: "";
    position: absolute;
    top: -2px;
    right: -2px;
    width: 6px;
    height: 6px;
    background-color: #f59e0b;
    border-radius: 50%;
    border: 1px solid white;
    opacity: 0.8;
  }

  .comment-highlight:hover::after {
    opacity: 1;
    transform: scale(1.2);
  }

  .comment-highlight.active::after {
    background-color: white;
  }

  p.is-editor-empty:first-child::before {
    color: lightslategray;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
}

/* Change tracking styles */

/* Insertion mark - green background for additions */
ins {
  background-color: #d4edda !important;
  color: #8ca392 !important;
  text-decoration: none !important;
  padding: 1px 2px;
  border-radius: 2px;
}

/* Deletion mark - red background with strikethrough for deletions */
del {
  background-color: #f8d7da !important;
  color: #721c24 !important;
  text-decoration: line-through !important;
  padding: 1px 2px;
  border-radius: 2px;
}

/* Highlight for specific commits when hovering */
.change-highlight {
  background-color: #96f5b0 !important;
  border: 1px solid #ffeaa7;
  border-radius: 2px;
  padding: 1px;
}

/* Commit diff styles - show only changes between consecutive commits */
.commit-diff-addition {
  background-color: #d4edda !important;
  color: #155724 !important;
  border-left: 3px solid #28a745;
  padding: 1px 4px;
  border-radius: 2px;
  position: relative;
}

.commit-diff-deletion {
  background-color: #f8d7da !important;
  color: #721c24 !important;
  text-decoration: line-through !important;
  border-left: 3px solid #dc3545;
  padding: 1px 4px;
  border-radius: 2px;
  position: relative;
  display: inline-block;
}

/* Hover effects for commit diffs */
.commit-diff-addition:hover {
  background-color: #c3e6cb !important;
  cursor: pointer;
}

.commit-diff-deletion:hover {
  background-color: #f5c6cb !important;
  cursor: pointer;
}

/* Hover effects */
ins:hover {
  background-color: #c3e6cb !important;
  cursor: pointer;
}

del:hover {
  background-color: #f5c6cb !important;
  cursor: pointer;
}

/* Tooltip styles for change information */
.change-tooltip {
  position: absolute;
  background: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
  white-space: nowrap;
}

.change-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}
