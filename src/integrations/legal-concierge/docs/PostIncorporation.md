# PostIncorporation API Integration

This document describes the PostIncorporation APIs that have been added to the legal-concierge client and their corresponding React Query hooks.

## API Endpoints

The following PostIncorporation endpoints have been integrated:

### 1. Complete EIN Task

- **Endpoint**: `PUT /api/companies/{companyId}/completeein`
- **Description**: Marks the EIN (Tax ID) application task as complete
- **Parameters**: `companyId` (UUID)

### 2. Complete Business Account Task

- **Endpoint**: `PUT /api/companies/{companyId}/completebusinessaccount`
- **Description**: Marks the business account setup task as complete
- **Parameters**: `companyId` (UUID)

### 3. Complete Foreign Qualification Task

- **Endpoint**: `PUT /api/companies/{companyId}/completeforeignqualification`
- **Description**: Marks the foreign qualification task as complete
- **Parameters**: `companyId` (UUID)

### 4. Add Foreign Qualification State

- **Endpoint**: `POST /api/companies/{companyId}/foreignqualification/states`
- **Description**: Adds a state to the foreign qualification list
- **Parameters**: `companyId` (UUID)
- **Body**: `string` (state name)

### 5. Remove Foreign Qualification State

- **Endpoint**: `DELETE /api/companies/foreignqualification/states/{id}`
- **Description**: Removes a state from the foreign qualification list
- **Parameters**: `id` (UUID)

### 6. Confirm Post-Incorporation

- **Endpoint**: `PUT /api/companies/{companyId}/postincorporation/confirm`
- **Description**: Marks the overall post-incorporation process as confirmed
- **Parameters**: `companyId` (UUID)

## React Query Hooks

### Import

```typescript
import {
  useCompleteEIN,
  useCompleteBusinessAccount,
  useCompleteForeignQualification,
  useAddForeignQualificationState,
  useRemoveForeignQualificationState,
  useConfirmPostIncorporation,
} from "@/integrations/legal-concierge/hooks";
```

### Hook Usage Examples

#### 1. Complete EIN Task

```typescript
const completeEIN = useCompleteEIN();

const handleCompleteEIN = async () => {
  try {
    await completeEIN.mutateAsync(companyId);
    toast.success("EIN task completed successfully!");
  } catch (error) {
    toast.error("Failed to complete EIN task");
  }
};
```

#### 2. Complete Business Account Task

```typescript
const completeBusinessAccount = useCompleteBusinessAccount();

const handleCompleteBusinessAccount = async () => {
  try {
    await completeBusinessAccount.mutateAsync(companyId);
    toast.success("Business account task completed!");
  } catch (error) {
    toast.error("Failed to complete business account task");
  }
};
```

#### 3. Complete Foreign Qualification Task

```typescript
const completeForeignQualification = useCompleteForeignQualification();

const handleCompleteForeignQualification = async () => {
  try {
    await completeForeignQualification.mutateAsync(companyId);
    toast.success("Foreign qualification task completed!");
  } catch (error) {
    toast.error("Failed to complete foreign qualification task");
  }
};
```

#### 4. Add Foreign Qualification State

```typescript
const addForeignQualificationState = useAddForeignQualificationState();

const handleAddState = async (stateName: string) => {
  try {
    await addForeignQualificationState.mutateAsync({
      companyId,
      state: stateName,
    });
    toast.success(`Added ${stateName} to foreign qualification states`);
  } catch (error) {
    toast.error("Failed to add foreign qualification state");
  }
};
```

#### 5. Remove Foreign Qualification State

```typescript
const removeForeignQualificationState = useRemoveForeignQualificationState();

const handleRemoveState = async (stateId: string) => {
  try {
    await removeForeignQualificationState.mutateAsync({
      id: stateId,
      companyId,
    });
    toast.success("State removed successfully");
  } catch (error) {
    toast.error("Failed to remove foreign qualification state");
  }
};
```

## Types

### PostIncorporationTaskResponse

```typescript
interface PostIncorporationTaskResponse {
  message?: string;
  data?: any;
  error?: string | null;
  validationErrors?: Record<string, unknown> | null;
}
```

### ForeignQualificationState

```typescript
interface ForeignQualificationState {
  id: string;
  state: string;
  companyId: string;
  createdAt?: string;
  updatedAt?: string;
}
```

### ForeignQualificationStateRequest

```typescript
interface ForeignQualificationStateRequest {
  state: string;
}
```

## Error Handling

All hooks follow the same error handling pattern:

- They use the `hasError` type guard to check for API errors
- Errors are thrown and can be caught in try/catch blocks
- The hooks automatically invalidate relevant React Query cache entries on success

## Cache Invalidation

The hooks automatically invalidate the following query keys on success:

- `[QueryKey().company, companyId]` - Company data
- `[QueryKey().postIncorporation, companyId]` - Post-incorporation data
- `[QueryKey().foreignQualificationStates, companyId]` - Foreign qualification states (for state-related operations)

## Example Component

See `src/components/post-incorporation/examples/PostIncorporationAPIExample.tsx` for a complete example of how to use these hooks in a React component.

## Integration with Existing Components

To integrate these hooks with existing post-incorporation components:

1. Replace localStorage-based task completion with API calls
2. Use the hooks' loading states to show appropriate UI feedback
3. Handle errors gracefully with toast notifications
4. Leverage React Query's caching for better performance

## Notes

- All hooks require a valid `companyId` from the authenticated user
- The hooks follow the existing patterns established in the legal-concierge integration
- Error responses are handled consistently across all hooks
- Success callbacks automatically update the React Query cache
