import React, { useState } from "react";
import Layout from "@/components/Layout";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import ForgotPasswordForm from "@/components/auth/ForgotPasswordForm";
import { toast } from "sonner";

const ForgotPassword: React.FC = () => {
  const { isAuthenticated, loading, forgotPassword } = useAuth();
  const location = useLocation();
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  if (loading) {
    return null; // Don't render anything while checking auth status
  }

  // If authenticated, redirect to dashboard
  if (isAuthenticated) {
    const from = (location.state as any)?.from?.pathname || "/dashboard";
    return <Navigate to={from} replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    setIsSubmitting(true);

    try {
      await forgotPassword(email);
      setIsSubmitted(true);
      setIsSubmitting(false);
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-[calc(100vh-16rem)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <AnimatedTransition className="w-full max-w-md">
          <Card variant="glass" className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-xl md:text-2xl">
                Forgot Password
              </CardTitle>
              <CardDescription>
                {isSubmitted
                  ? "Check your email for password reset instructions"
                  : "Enter your email to receive a password reset link"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isSubmitted ? (
                <div className="text-center py-4">
                  <p className="mb-4">
                    We've sent password reset instructions to:
                  </p>
                  <p className="font-medium text-lg">{email}</p>
                  <p className="mt-4 text-sm text-gray-600">
                    Please check your email and follow the instructions to reset
                    your password.
                  </p>
                </div>
              ) : (
                <ForgotPasswordForm
                  email={email}
                  setEmail={setEmail}
                  loading={isSubmitting}
                  onSubmit={handleSubmit}
                />
              )}
            </CardContent>
          </Card>
        </AnimatedTransition>
      </div>
    </Layout>
  );
};

export default ForgotPassword;
