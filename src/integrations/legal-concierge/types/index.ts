export type SendRegisterInviteRequest = { email?: string };
export type RegisterRequest = {
  fullName: string;
  email: string;
  password: string;
  phoneNumber: string;
  token: string;
};
export type LoginRequest = { email?: string; password?: string };
export type VerifyOtpRequest = { otp?: string; email?: string };
export type ForgotPasswordRequest = { email?: string };
export type ResetPasswordRequest = {
  email?: string;
  token?: string;
  newPassword?: string;
};

export * from "./Collaborator";
export * from "./PostIncorporation";

export type Company = {
  companyId: string;
  companyName: string;
  role: string;
};

export type OfficerUpdateRequest = {
  name?: string;
  emailAddress?: string;
  contactAddress?: string;
  stockOwnership?: number;
  amountOfShares?: number;
  isDirector?: boolean;
  isCEO: boolean;
  isPresident: boolean;
  isSecretary: boolean;
  isTreasurer: boolean;
  notApplicable: boolean;
};
