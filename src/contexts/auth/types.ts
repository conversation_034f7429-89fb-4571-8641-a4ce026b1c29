export interface User {
  id: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  avatarUrl: string | null;
  companyId?: string | null;
  roles: string[];
}

export interface AuthState {
  isAuthenticated: boolean;
  isMfaVerified: boolean;
  userEmail: string | null;
  user: User | null;
  loading: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  phone: string;
  full_name: string | null;
  avatar_url: string | null;
  updated_at: string | null;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  signUp: (
    email: string,
    phone: string,
    password: string,
    fullName: string,
    token: string
  ) => Promise<void>;
  logout: () => Promise<void>;
  verifyMfa: (email: string, code: string) => Promise<boolean>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (
    email: string,
    token: string,
    newPassword: string
  ) => Promise<void>;
  updateProfile: (data: Partial<UserProfile>) => Promise<void>;
  handleAuthChange: (silent: boolean) => Promise<void>;
}
