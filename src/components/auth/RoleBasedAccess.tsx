import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  useUserPermissions,
  UserRole,
  USER_ROLES,
} from "@/integrations/legal-concierge/hooks/useUserProfile";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";

interface RoleBasedAccessProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  requiredPermission?: keyof ReturnType<typeof useUserPermissions>;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

/**
 * Component that conditionally renders content based on user roles and permissions
 */
export const RoleBasedAccess: React.FC<RoleBasedAccessProps> = ({
  children,
  allowedRoles,
  requiredPermission,
  fallback = null,
}) => {
  const { user } = useAuth();
  const { isCompanySelected } = useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);

  // Check if user has required role
  const hasRequiredRole = allowedRoles
    ? allowedRoles.some((role) => user?.roles?.includes(role))
    : true;

  // Check if user has required permission
  const hasRequiredPermission = requiredPermission
    ? permissions[requiredPermission]
    : true;

  // Render children if user has access, otherwise render fallback
  if (hasRequiredRole && hasRequiredPermission) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
};

/**
 * Hook to check if user has specific role
 */
export const useHasRole = (role: UserRole): boolean => {
  const { user } = useAuth();
  return user?.roles?.includes(role) ?? false;
};

/**
 * Hook to check if user has any of the specified roles
 */
export const useHasAnyRole = (roles: UserRole[]): boolean => {
  const { user } = useAuth();
  return roles.some((role) => user?.roles?.includes(role)) ?? false;
};

/**
 * Hook to get user's primary role (prioritized by hierarchy)
 */
export const useUserRole = (): UserRole | null => {
  const { user } = useAuth();
  if (!user?.roles?.length) return null;

  // Return roles in priority order: OWNER > COLLABORATOR > SIGNER
  if (user.roles.includes(USER_ROLES.OWNER)) {
    return USER_ROLES.OWNER;
  }

  if (user.roles.includes(USER_ROLES.COLLABORATOR)) {
    return USER_ROLES.COLLABORATOR;
  }

  if (user.roles.includes(USER_ROLES.SIGNER)) {
    return USER_ROLES.SIGNER;
  }

  return user.roles[0] as UserRole;
};

/**
 * Component that shows different content based on user role
 */
interface RoleBasedContentProps {
  ownerContent?: React.ReactNode;
  collaboratorContent?: React.ReactNode;
  signerContent?: React.ReactNode;
  fallbackContent?: React.ReactNode;
}

export const RoleBasedContent: React.FC<RoleBasedContentProps> = ({
  ownerContent,
  collaboratorContent,
  signerContent,
  fallbackContent,
}) => {
  const isOwner = useHasRole(USER_ROLES.OWNER);
  const isCollaborator = useHasRole(USER_ROLES.COLLABORATOR);
  const isSigner = useHasRole(USER_ROLES.SIGNER);

  if (isOwner && ownerContent) {
    return <>{ownerContent}</>;
  }

  if (isCollaborator && collaboratorContent) {
    return <>{collaboratorContent}</>;
  }

  if (isSigner && signerContent) {
    return <>{signerContent}</>;
  }

  return <>{fallbackContent}</>;
};

export default RoleBasedAccess;
