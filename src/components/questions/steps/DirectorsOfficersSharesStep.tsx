import React from "react";
import { QuestionnaireFormData } from "../types";
import DirectorsList from "./directors/DirectorsList";
import ShareSummary from "./directors/ShareSummary";
import { useDirectorsManagement } from "../hooks/useDirectorsManagement";
import { toast } from "sonner";

interface DirectorsOfficersSharesStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: unknown) => void;
}

const DirectorsOfficersSharesStep: React.FC<
  DirectorsOfficersSharesStepProps
> = ({ formData, updateFormData }) => {
  const {
    handleAddDirector,
    handleRemoveDirector,
    updateDirectorField,
    toggleOfficerTitle,
    isTitleTakenByOther,
  } = useDirectorsManagement(formData, updateFormData);

  // Handle navigating back to Authorized Shares step
  const handleEditShares = () => {
    console.log("Navigating to authorized-shares step");
    updateFormData("currentStep", "authorized-shares");
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-base font-medium mb-1">
          Please input information for each director, officer and stockholder of
          the Company.
        </h3>

        <DirectorsList
          directors={formData.directors}
          onAddDirector={handleAddDirector}
          onRemoveDirector={handleRemoveDirector}
          onUpdateDirectorField={updateDirectorField}
          onToggleOfficerTitle={toggleOfficerTitle}
          isTitleTakenByOther={isTitleTakenByOther}
        />
      </div>

      <ShareSummary
        authorizedShares={formData.authorizedShares}
        issuedShares={formData.issuedShares}
        onEditShares={handleEditShares}
      />
    </div>
  );
};

export default DirectorsOfficersSharesStep;

// Export the save function so it can be called from the continue button
export const saveDirectorsOfficersData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  addOfficer: { mutateAsync: (data: unknown) => Promise<unknown> },
  updateOfficer: { mutateAsync: (data: unknown) => Promise<unknown> }
): Promise<boolean> => {
  try {
    console.log("Saving directors/officers data...");
    
    // Process each director in the form data
    for (const director of formData.directors) {
      // Skip directors that don't have required fields
      if (!director.name.trim() || !director.emailAddress.trim() || !director.contactAddress.trim()) {
        console.warn(`Skipping director ${director.name} - missing required fields`);
        continue;
      }

      if (director.isNew) {
        // Create new officer
        console.log(`Creating new officer: ${director.name}`);
        await addOfficer.mutateAsync({
          companyId,
          officerId: null,
          name: director.name,
          emailAddress: director.emailAddress,
          contactAddress: director.contactAddress,
          stockOwnership: director.stockOwnership,
          amountOfShares: director.amountOfShares,
          isDirector: director.isDirector,
          isCEO: director.isCEO,
          isPresident: director.isPresident,
          isSecretary: director.isSecretary,
          isTreasurer: director.isTreasurer,
          notApplicable: director.notApplicable,
        });
      } else {
        // Update existing officer
        console.log(`Updating existing officer: ${director.name}`);
        await updateOfficer.mutateAsync({
          id: director.id,
          companyId,
          name: director.name,
          emailAddress: director.emailAddress,
          contactAddress: director.contactAddress,
          stockOwnership: director.stockOwnership,
          amountOfShares: director.amountOfShares,
          isDirector: director.isDirector,
          isCEO: director.isCEO,
          isPresident: director.isPresident,
          isSecretary: director.isSecretary,
          isTreasurer: director.isTreasurer,
          notApplicable: director.notApplicable,
        });
      }
    }

    console.log("Directors/officers data saved successfully");
    toast.success("Directors and officers information saved successfully");
    return true;
  } catch (error) {
    console.error("Error saving directors/officers data:", error);
    toast.error("Failed to save directors and officers information");
    return false;
  }
};
