import { useEffect } from "react";
import { QuestionnaireFormData } from "../types";

export const useIncorporationDateSync = (
  formData: QuestionnaireFormData,
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void
) => {
  // Ensure incorporation date is set when incorporating today
  useEffect(() => {
    if (formData.incorporatingToday === true && !formData.incorporationDate) {
      updateFormData("incorporationDate", new Date());
    }
    // We no longer need to sync with plannedIncorporationDate as we're only using incorporationDate
  }, [formData.incorporatingToday, updateFormData]);

  return {};
};
