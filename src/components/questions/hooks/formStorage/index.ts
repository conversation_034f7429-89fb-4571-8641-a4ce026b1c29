import { APIClient } from "@/integrations/legal-concierge/client";
import { saveCompanyDetails } from "./saveCompanyDetails";
import { saveStakeholdersData } from "./saveStakeholdersData";
import { saveVestingInfo } from "./saveVestingInfo";
import { saveTechAssignments } from "./saveTechAssignments";
import { getSavedFormData, getSavedStep } from "./getFormData";
import { QuestionnaireFormData, FormStep } from "./types";

// Save only the relevant data for the current step
const saveStepData = async (
  userId: string,
  companyId: string,
  formData: QuestionnaireFormData,
  currentStep: FormStep
): Promise<void> => {
  console.log(`saveStepData called for step: ${currentStep}`);

  try {
    switch (currentStep) {
      case "company-name":
        console.log("Saving company name data...");
        // Save company name and incorporation details
        await saveCompanyDetails(userId, companyId, formData, currentStep);
        console.log("Company name data saved successfully");
        break;

      case "company-address":
        console.log("Saving company address data...");
        // Save company address
        await saveCompanyDetails(userId, companyId, formData, currentStep);
        console.log("Company address data saved successfully");
        break;

      case "authorized-shares":
        console.log("Saving authorized shares data...");
        // Save authorized shares
        await saveCompanyDetails(userId, companyId, formData, currentStep);
        console.log("Authorized shares data saved successfully");
        break;

      case "directors-and-officers":
        console.log("Saving stakeholders/directors data...");
        // Save stakeholders/directors data
        await saveStakeholdersData(userId, companyId, formData);
        console.log("Stakeholders/directors data saved successfully");
        break;

      case "vesting":
        console.log("Saving vesting information...");
        // Save vesting information
        await saveVestingInfo(userId, companyId, formData);
        console.log("Vesting information saved successfully");
        break;

      case "technology":
        console.log("Saving tech assignments...");
        // Save tech assignments
        await saveTechAssignments(userId, companyId, formData);
        console.log("Tech assignments saved successfully");
        break;

      case "stock-plan":
        console.log("Saving stock plan data...");
        // Save stock plan data using saveCompanyDetails
        await saveCompanyDetails(userId, companyId, formData, currentStep);
        console.log("Stock plan data saved successfully");
        break;

      case "registered-agent":
        console.log("Saving registered agent data...");
        if (
          formData.registeredAgent &&
          formData.registeredAgent.id &&
          companyId
        ) {
          try {
            const api = new APIClient();
            await api.updateRegisteredAgent(companyId, {
              agentId: formData.registeredAgent.id,
            });
            console.log("Registered agent data saved successfully");
          } catch (error) {
            console.error("Error saving registered agent data:", error);
            throw error;
          }
        } else {
          console.log(
            "No registered agent selected or no company ID, skipping save"
          );
        }
        break;

      case "review":
        console.log("Saving all data for review step...");
        // Save all data when on review step
        await saveCompanyDetails(userId, companyId, formData, currentStep);
        await saveStakeholdersData(userId, companyId, formData);
        await saveVestingInfo(userId, companyId, formData);
        await saveTechAssignments(userId, companyId, formData);

        // Explicitly save stock plan data
        try {
          console.log("Saving stock plan data for review step...");
          await saveCompanyDetails(userId, companyId, formData, "stock-plan");
          console.log("Stock plan data saved successfully for review step");
        } catch (error) {
          console.error("Error saving stock plan data for review step:", error);
          // Continue with other saves even if this one fails
        }

        // Save registered agent data
        if (formData.registeredAgent && formData.registeredAgent.id) {
          try {
            const api = new APIClient();
            await api.updateRegisteredAgent(companyId, {
              agentId: formData.registeredAgent.id,
            });
            console.log("Registered agent data saved successfully");
          } catch (error) {
            console.error("Error saving registered agent data:", error);
            // Continue with other saves even if this one fails
          }
        }

        console.log("All data saved successfully for review step");
        break;

      default:
        console.log(`Unknown step: ${currentStep}, no data saved`);
    }
  } catch (error) {
    console.error(`Error in saveStepData for step ${currentStep}:`, error);
    throw error;
  }
};

// Main function to save form data to legal-concierge API and localStorage
export const saveFormData = async (
  formData: QuestionnaireFormData,
  currentStep: FormStep
): Promise<boolean> => {
  try {
    console.log(`Starting to save ${currentStep} data...`);

    // First save to localStorage as a fallback
    let dataToSave = {
      ...formData,
      currentStep,
    };

    const syncDirectors = async () => {
      try {
        const savedData = await getSavedFormData();
        if (savedData?.directors) {
          // Only update directors field
          const updatedFormData = {
            ...formData,
            directors: savedData?.directors,
          };
          dataToSave = {
            ...updatedFormData,
            currentStep,
          }
        }
      } catch (error) {
        console.error("Failed to sync directors:", error);
      }
    };
    

    // Get the current user from the auth context
    // Since this is not a React component, we need to get the user differently
    // We'll use the APIClient to get the current user profile
    const api = new APIClient();
    console.log("Fetching user profile...");
    const { data: user, error } = await api.profile();

    if (error) {
      console.error("Error fetching user profile:", error);
      return false;
    }

    if (!user) {
      console.log("User not authenticated, only saving to localStorage");
      return false;
    }

    if (!user.companyId) {
      console.log("No company selected, only saving to localStorage");
      return false;
    }

    console.log(
      `User authenticated with ID: ${user.id}, Company ID: ${user.companyId}`
    );

    // Then try to save to legal-concierge API if company name is available
    if (formData.companyName) {
      console.log(`Saving ${currentStep} data to API...`);
      // Save only the relevant data for the current step
      await saveStepData(user.id, user.companyId, formData, currentStep);
      console.log(
        `Successfully saved ${currentStep} data to legal-concierge API`
      );

      try {
        await syncDirectors();
        console.log("Master setter", dataToSave);
        localStorage.setItem("companyQuestionnaire", JSON.stringify(dataToSave));
      } catch (err) {
        console.error("syncDirectors() failed:", err);
      }
    
    console.log("Saved to localStorage");

      return true;
    } else {
      console.log("Company name not available, skipping API save");
      return false;
    }


    
  } catch (error) {
    console.error("Error saving form data:", error);
    return false;
  }
};

// Store finalized form data for cross-workflow access
export const saveFinalizedFormData = async (
  formData: QuestionnaireFormData
): Promise<boolean> => {
  try {
    console.log("Finalizing company details...");

    // Store in localStorage for fallback
    localStorage.setItem("completedCompanyDetails", JSON.stringify(formData));
    console.log("Saved to localStorage");

    // Get the current user
    const api = new APIClient();
    console.log("Fetching user profile...");
    const { data: user, error } = await api.profile();

    if (error) {
      console.error("Error fetching user profile:", error);
      return false;
    }

    if (!user) {
      console.log("User not authenticated, only saving to localStorage");
      return false;
    }

    if (!user.companyId) {
      console.log("No company selected, only saving to localStorage");
      return false;
    }

    console.log(
      `User authenticated with ID: ${user.id}, Company ID: ${user.companyId}`
    );

    // Save all data when finalizing
    if (formData.companyName) {
      console.log("Saving all company data...");

      try {
        // Save company details
        console.log("Saving company details...");
        await saveCompanyDetails(user.id, user.companyId, formData, "review");
        console.log("Company details saved successfully");

        // Save stakeholders/directors data
        console.log("Saving stakeholders/directors data...");
        await saveStakeholdersData(user.id, user.companyId, formData);
        console.log("Stakeholders/directors data saved successfully");

        // Save vesting information
        console.log("Saving vesting information...");
        await saveVestingInfo(user.id, user.companyId, formData);
        console.log("Vesting information saved successfully");

        // Save tech assignments
        console.log("Saving tech assignments...");
        await saveTechAssignments(user.id, user.companyId, formData);
        console.log("Tech assignments saved successfully");

        // Save stock plan data
        try {
          console.log("Saving stock plan data...");
          await saveCompanyDetails(
            user.id,
            user.companyId,
            formData,
            "stock-plan"
          );
          console.log("Stock plan data saved successfully");
        } catch (error) {
          console.error("Error saving stock plan data:", error);
          // Continue with other saves even if this one fails
        }

        // Save registered agent data
        if (formData.registeredAgent && formData.registeredAgent.id) {
          try {
            console.log("Saving registered agent data...");
            await api.updateRegisteredAgent(user.companyId, {
              agentId: formData.registeredAgent.id,
            });
            console.log("Registered agent data saved successfully");
          } catch (error) {
            console.error("Error saving registered agent data:", error);
            // Continue with other saves even if this one fails
          }
        }

        console.log("All company data saved successfully");
      } catch (saveError) {
        console.error("Error saving company data:", saveError);
        return false;
      }
    } else {
      console.log("Company name not available, skipping API save");
      return false;
    }

    console.log(
      "Company details finalized and stored for cross-workflow access"
    );
    return true;
  } catch (error) {
    console.error("Error storing finalized company details:", error);
    return false;
  }
};

// Export all the necessary functions
export { getSavedFormData, getSavedStep };

// Re-export types
export * from "./types";
