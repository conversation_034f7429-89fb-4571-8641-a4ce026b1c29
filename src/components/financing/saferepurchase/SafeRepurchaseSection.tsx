import React, { useState } from "react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { SafeRepurchase } from "@/types/financing";

// Mock data for demonstration
const mockSafes: SafeRepurchase[] = [
  {
    id: "1",
    selected: false,
    repurchaseAmount: 0,
    boardApproved: false,
    investorName: "Angel Investor A",
    dateIssued: new Date("2023-02-15"),
    purchaseAmount: 250000,
    valuationCap: 8000000,
    discount: 20,
    mfn: true,
  },
  {
    id: "2",
    selected: false,
    repurchaseAmount: 0,
    boardApproved: false,
    investorName: "VC Firm B",
    dateIssued: new Date("2023-03-10"),
    purchaseAmount: 500000,
    valuationCap: 10000000,
    discount: 15,
    mfn: false,
  },
  {
    id: "3",
    selected: false,
    repurchaseAmount: 0,
    boardApproved: false,
    investorName: "Strategic Partner C",
    dateIssued: new Date("2023-04-05"),
    purchaseAmount: 350000,
    valuationCap: 9000000,
    discount: 18,
    mfn: true,
  },
];

const SafeRepurchaseSection: React.FC = () => {
  const { toast } = useToast();
  const [safes, setSafes] = useState<SafeRepurchase[]>(mockSafes);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [boardConsentDialogOpen, setBoardConsentDialogOpen] = useState(false);

  const handleCheckboxChange = (id: string, checked: boolean) => {
    setSafes(
      safes.map((safe) =>
        safe.id === id ? { ...safe, selected: checked } : safe
      )
    );
  };

  const handleRepurchaseAmountChange = (id: string, value: string) => {
    const amount = parseFloat(value) || 0;
    setSafes(
      safes.map((safe) =>
        safe.id === id ? { ...safe, repurchaseAmount: amount } : safe
      )
    );
  };

  const handleRepurchaseClick = () => {
    const selectedSafes = safes.filter((safe) => safe.selected);
    if (selectedSafes.length === 0) {
      toast({
        title: "No SAFEs Selected",
        description: "Please select at least one SAFE to repurchase.",
        variant: "destructive",
      });
      return;
    }

    const invalidSafes = selectedSafes.filter(
      (safe) => safe.repurchaseAmount <= 0
    );
    if (invalidSafes.length > 0) {
      toast({
        title: "Invalid Repurchase Amount",
        description:
          "Please enter a valid repurchase amount for all selected SAFEs.",
        variant: "destructive",
      });
      return;
    }

    setConfirmDialogOpen(true);
  };

  const handleConfirmRepurchase = () => {
    setConfirmDialogOpen(false);
    setBoardConsentDialogOpen(true);
  };

  const handleBoardConsentSign = () => {
    toast({
      title: "Board Consent Submitted",
      description:
        "The board consent has been submitted for approval. You will be notified when all directors have signed.",
    });

    // In a real app, this would trigger emails to directors

    setBoardConsentDialogOpen(false);
  };

  const anySelected = safes.some((safe) => safe.selected);

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">
            SAFE Repurchase and Cancel
          </h3>
          <p className="text-sm text-muted-foreground mb-4">
            Please click the SAFE(s) you would like to repurchase. You should
            only approve a repurchase if, and only if, you have discussed with
            the SAFE Holder. For additional assistance, contact Founders Form
            legal team.
          </p>

          {anySelected && (
            <div className="mb-4">
              <Button onClick={handleRepurchaseClick}>Repurchase SAFE</Button>
            </div>
          )}
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">Select</TableHead>
              <TableHead>Repurchase Amount</TableHead>
              <TableHead>Repurchase Approved by the Board</TableHead>
              <TableHead>Investor Name</TableHead>
              <TableHead>Date Issued</TableHead>
              <TableHead>Purchase Amount</TableHead>
              <TableHead>Valuation Cap</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>MFN</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {safes.map((safe) => (
              <TableRow key={safe.id}>
                <TableCell className="text-center">
                  <Checkbox
                    checked={safe.selected}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(safe.id, checked === true)
                    }
                  />
                </TableCell>
                <TableCell>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      $
                    </span>
                    <Input
                      value={safe.repurchaseAmount || ""}
                      onChange={(e) =>
                        handleRepurchaseAmountChange(safe.id, e.target.value)
                      }
                      type="text"
                      placeholder="0.00"
                      className="pl-8"
                      disabled={!safe.selected}
                    />
                  </div>
                </TableCell>
                <TableCell>{safe.boardApproved ? "Yes" : "No"}</TableCell>
                <TableCell>{safe.investorName}</TableCell>
                <TableCell>
                  {safe.dateIssued ? format(safe.dateIssued, "PP") : "N/A"}
                </TableCell>
                <TableCell>${safe.purchaseAmount.toLocaleString()}</TableCell>
                <TableCell>${safe.valuationCap.toLocaleString()}</TableCell>
                <TableCell>{safe.discount}%</TableCell>
                <TableCell>{safe.mfn ? "Yes" : "No"}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm SAFE Repurchase</DialogTitle>
              <DialogDescription>
                Are you sure you want to repurchase the selected SAFE(s)? This
                action requires board approval.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Investor</TableHead>
                    <TableHead>Purchase Amount</TableHead>
                    <TableHead>Repurchase Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {safes
                    .filter((s) => s.selected)
                    .map((safe) => (
                      <TableRow key={safe.id}>
                        <TableCell>{safe.investorName}</TableCell>
                        <TableCell>
                          ${safe.purchaseAmount.toLocaleString()}
                        </TableCell>
                        <TableCell>
                          ${safe.repurchaseAmount.toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setConfirmDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleConfirmRepurchase}>
                Confirm Repurchase
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog
          open={boardConsentDialogOpen}
          onOpenChange={setBoardConsentDialogOpen}
        >
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Board Consent for SAFE Repurchase</DialogTitle>
              <DialogDescription>
                Review and sign the board consent for SAFE repurchase
              </DialogDescription>
            </DialogHeader>

            <div className="h-96 border rounded-md p-4 overflow-y-auto bg-gray-50 my-4">
              <h3 className="text-lg font-semibold mb-2">Board Consent</h3>
              <p className="text-sm mb-4">
                This is where the board consent document would appear. In a real
                application, this would be a document that can be reviewed.
              </p>

              <h4 className="text-md font-medium mt-6 mb-2">
                Form of Repurchase and Cancellation Agreement
              </h4>
              <p className="text-sm">
                This is where the form of repurchase and cancellation agreement
                would appear.
              </p>
            </div>

            <div className="border p-4 rounded-md">
              <h4 className="font-medium mb-2">Signature</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Please sign this document to submit for board approval
              </p>
              <div className="border h-20 rounded-md mb-4 bg-gray-50">
                {/* Signature pad would go here */}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setBoardConsentDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleBoardConsentSign}>Sign and Submit</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default SafeRepurchaseSection;
