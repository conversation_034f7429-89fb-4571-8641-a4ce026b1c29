import { v4 as uuidv4 } from "uuid";

export type ExternalIncorporationStep =
  | "company-info"
  | "documents"
  | "stakeholders"
  | "stock-options"
  | "tech-contributions"
  | "registered-agent"
  | "review";

export interface ExternalIncorporationFormData {
  companyName: string;
  companyAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  authorizedShares: number;
  issuedShares: number;
  parValuePerShare: number;
  incorporationDate: string;
  hasStockOptionPlan: boolean;
  stockOptionPlanPercentage: number;
  stakeholders: StakeholderData[];
  techContributions: TechContribution[];
  registeredAgent: {
    name: string;
    address: string;
    email: string;
    phone: string;
  };
  documents: DocumentUpload[];
  currentStep: ExternalIncorporationStep;
}

export interface StakeholderData {
  id: string;
  name: string;
  isDirector: boolean;
  officerTitles: string[];
  emailAddress: string;
  address: string;
  stockOwnershipPercentage: number;
  sharesAmount: number;
  isVester: boolean;
  vestingSchedule: string;
  acceleration: string;
}

export interface TechContribution {
  id: string;
  stakeholderId: string;
  description: string;
}

export interface DocumentUpload {
  id: string;
  type: string;
  file: File | null;
  url: string;
  name: string;
  isRequired: boolean;
  isUploaded: boolean;
}

export const requiredDocuments = [
  {
    type: "certificate",
    label: "Certificate of Incorporation",
    isRequired: true,
  },
  {
    type: "bylaws",
    label: "Bylaws",
    isRequired: true,
  },
  {
    type: "rspa",
    label: "Restricted Stock Purchase Agreements",
    isRequired: true,
  },
  {
    type: "sole-incorporator",
    label: "Action by Sole Incorporator",
    isRequired: true,
  },
  {
    type: "board-consent",
    label: "Action by Unanimous Written Consent of the Board of Directors",
    isRequired: true,
  },
  {
    type: "ciia",
    label: "Confidential Information and Invention Assignment Agreements",
    isRequired: true,
  },
  {
    type: "stockholder-consent",
    label: "Action by Written Consent of the Stockholders",
    isRequired: false,
  },
];

export const initialFormData: ExternalIncorporationFormData = {
  companyName: "",
  companyAddress: {
    street: "",
    city: "",
    state: "",
    zipCode: "",
  },
  authorizedShares: 10000000,
  issuedShares: 0,
  parValuePerShare: 0.00001,
  incorporationDate: "",
  hasStockOptionPlan: false,
  stockOptionPlanPercentage: 10,
  stakeholders: [],
  techContributions: [],
  registeredAgent: {
    name: "Cogency Global Inc.",
    address: "",
    email: "",
    phone: "",
  },
  documents: requiredDocuments.map((doc) => ({
    id: uuidv4(),
    type: doc.type,
    file: null,
    url: "",
    name: doc.label,
    isRequired: doc.isRequired,
    isUploaded: false,
  })),
  currentStep: "company-info",
};
