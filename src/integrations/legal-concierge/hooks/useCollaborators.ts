import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { QueryKey } from "../keys";
import {
  Collaborator,
  CollaboratorInviteRequest,
  CollaboratorRole,
} from "../types";

const api = new APIClient();

// Type guard to check if response has an error
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// Get collaborators for a company
export const useCompanyCollaborators = (companyId: string) => {
  return useQuery({
    queryKey: [QueryKey().company, companyId, "collaborators"],
    queryFn: async () => {
      const response = await api.getCompanyCollaborators(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data as Collaborator[];
    },
    enabled: !!companyId,
  });
};

// Send an invite (for registration)
export const useSendInvite = () => {
  return useMutation({
    mutationFn: async ({
      email,
      role,
    }: {
      email: string;
      role: CollaboratorRole;
    }) => {
      const payload = {
        email,
        role,
      };
      const response = await api.sendInvite(payload);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return { data: response.data };
    },
  });
};

// Invite a collaborator
export const useInviteCollaborator = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      companyId,
      email,
      role,
    }: {
      companyId: string;
      email: string;
      role: CollaboratorRole;
    }) => {
      const payload: CollaboratorInviteRequest = {
        email,
        role,
      };
      const response = await api.inviteCollaborator(companyId, payload);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return { companyId, data: response.data };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId, "collaborators"],
      });
    },
  });
};

// Update a collaborator's role
export const useUpdateCollaboratorRole = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      collaboratorId,
      companyId,
      role,
    }: {
      collaboratorId: string;
      companyId: string;
      role: CollaboratorRole;
    }) => {
      const response = await api.updateCollaboratorRole(collaboratorId, {
        role,
      });
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return { companyId, data: response.data };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId, "collaborators"],
      });
    },
  });
};

// Delete a collaborator
export const useDeleteCollaborator = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      collaboratorId,
      companyId,
    }: {
      collaboratorId: string;
      companyId: string;
    }) => {
      const response = await api.deleteCollaborator(collaboratorId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return { companyId, data: response.data };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId, "collaborators"],
      });
    },
  });
};
