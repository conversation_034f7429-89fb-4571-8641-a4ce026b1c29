import React, { useState, useEffect, useCallback } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";  
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { Comment } from "@/components/collaboration/types";
import { supabase } from "@/integrations/supabase/client";
import { formatDistanceToNow } from "date-fns";
import { Check, MessageSquare, Plus, FileText } from "lucide-react";
import { Link } from "react-router-dom";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";
import { useCompanyDocuments } from "@/integrations/legal-concierge/hooks/useDocuments";
import DocumentsList from "@/components/documents/DocumentsList";

// Replace this import to use the new parsePositionData utility
import { parsePositionData } from "@/components/collaboration/utils/commentUtils";
import DocumentModal from "../documents/DocumentModal";
import { Document as FFDocument } from "@/integrations/legal-concierge/types/Document";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

export const DocumentCommentsContext = React.createContext<{
  comments: Comment[];
  addComment: (
    comment: Omit<
      Comment,
      "id" | "userId" | "userName" | "timestamp" | "resolved"
    >
  ) => Promise<void>;
  resolveComment: (id: string) => Promise<void>;
} | null>(null);

const DocumentSigningContainer: React.FC = () => {
  const { documentId } = useParams<{ documentId: string }>();
  const navigate = useNavigate();
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfFile, setPdfFile] = useState<string | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [commentPosition, setCommentPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [showResolved, setShowResolved] = useState(false);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { companyDetails } = useCompanyDetails();
  const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);
  const [modalDocument, setModalDocument] = useState<FFDocument>();
  const [documentMode, setDocumentMode] = useState<'view' | 'edit'>('view');

  // Fetch company documents
  const companyId = user?.companyId || "";
  const {
    data: documents,
    isLoading: isLoadingDocuments,
    error: documentsError,
  } = useCompanyDocuments(companyId);

  const documentUrl = `/documents/contract.pdf`;

  useEffect(() => {
    if (documentId) {
      setPdfFile(documentUrl);
      fetchComments();
    }
  }, [documentId]);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
  }

  const goToPrevPage = () => {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1);
    }
  };

  const goToNextPage = () => {
    if (numPages && pageNumber < numPages) {
      setPageNumber(pageNumber + 1);
    }
  };

  const handlePageClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement;
    const rect = target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    setCommentPosition({ x, y });
  };

  const handleDocumentOpen = (doc: FFDocument, mode: 'view' | 'edit') => {
    setModalDocument(doc);
    setDocumentMode(mode);
    setIsDocumentModalOpen(true);
  };

  const fetchComments = async () => {
    if (!documentId || !user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("document_comments")
        .select("*")
        .eq("document_id", documentId)
        .order("timestamp", { ascending: false });

      if (error) {
        console.error("Error fetching comments:", error);
        toast.error("Failed to load comments");
        return;
      }

      // Transform the data to match our Comment type
      const formattedComments: Comment[] = data.map((comment) => ({
        id: comment.id,
        userId: comment.user_id,
        userName: comment.user_name,
        documentId: comment.document_id,
        content: comment.content,
        timestamp: new Date(comment.timestamp),
        position: parsePositionData(comment.position),
        resolved: comment.resolved,
      }));

      setComments(formattedComments);
    } catch (error) {
      console.error("Error in fetchComments:", error);
      toast.error("Failed to load comments");
    } finally {
      setLoading(false);
    }
  };

  const addComment = async (commentData: {
    content: string;
    documentId: string;
    position?: { x: number; y: number };
  }) => {
    if (!user) return;

    try {
      const defaultPosition = { x: 100, y: 100 };

      const newCommentObj = {
        user_id: user.id,
        user_name: user.email?.split("@")[0] || "Anonymous",
        document_id: commentData.documentId,
        content: commentData.content,
        position: commentData.position || defaultPosition,
        // Fix: Use null for company_id
        company_id: null,
      };

      const { data, error } = await supabase
        .from("document_comments")
        .insert(newCommentObj)
        .select()
        .single();

      if (error) {
        console.error("Error adding comment:", error);
        toast.error("Failed to add comment");
        return;
      }

      // Add the new comment to state
      const formattedComment: Comment = {
        id: data.id,
        userId: data.user_id,
        userName: data.user_name,
        documentId: data.document_id,
        content: data.content,
        timestamp: new Date(data.timestamp),
        position:
          parsePositionData(data.position) ||
          commentData.position ||
          defaultPosition,
        resolved: data.resolved,
      };

      setComments((prevComments) => [formattedComment, ...prevComments]);
      toast.success("Comment added successfully");
    } catch (error) {
      console.error("Error in addComment:", error);
      toast.error("Failed to add comment");
    } finally {
      setNewComment("");
      setCommentPosition(null);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !documentId || !commentPosition) return;
    addComment({
      content: newComment,
      documentId: documentId,
      position: commentPosition,
    });
  };

  const resolveComment = async (id: string) => {
    try {
      const { error } = await supabase
        .from("document_comments")
        .update({ resolved: true })
        .eq("id", id);

      if (error) {
        console.error("Error resolving comment:", error);
        toast.error("Failed to resolve comment");
        return;
      }

      setComments((prevComments) =>
        prevComments.map((comment) =>
          comment.id === id ? { ...comment, resolved: true } : comment
        )
      );
      toast.success("Comment resolved successfully");
    } catch (error) {
      console.error("Error in resolveComment:", error);
      toast.error("Failed to resolve comment");
    }
  };

  const filteredComments = showResolved
    ? comments
    : comments.filter((comment) => !comment.resolved);

  return (
    <DocumentCommentsContext.Provider
      value={{ comments, addComment, resolveComment }}
    >
      <div className="grid grid-cols-1 gap-8">
        {/* Company Documents Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Company Documents
            </CardTitle>
            <CardDescription>
              Your company's legal documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DocumentsList
              documents={documents}
              isLoading={isLoadingDocuments}
              error={documentsError}
              handleDocumentOpen={handleDocumentOpen}
            />
          </CardContent>
          <CardFooter className="flex justify-between">
            {/* <Button
              variant="outline"
              onClick={() => navigate("/company-documents")}
            >
              View All Documents
            </Button>
            <Button onClick={() => navigate("/")}>Go to Dashboard</Button> */}
          </CardFooter>
        </Card>

        <DocumentModal
          isOpen={isDocumentModalOpen}
          onClose={() => setIsDocumentModalOpen(false)}
          ffdocument={modalDocument}
          mode={documentMode}
        />

        {/* Document Signing Section */}
        {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="col-span-1">
            <CardHeader>
              <CardTitle>Document Signing</CardTitle>
            </CardHeader>
            <CardContent>
              {pdfFile ? (
                <div onClick={handlePageClick} style={{ cursor: "pointer" }}>
                  <Document
                    file={pdfFile}
                    onLoadSuccess={onDocumentLoadSuccess}
                    className="max-w-full"
                  >
                    <Page pageNumber={pageNumber} width={500} />
                  </Document>
                  <div className="flex justify-center mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={goToPrevPage}
                      disabled={pageNumber <= 1}
                    >
                      Previous
                    </Button>
                    <span className="mx-4">
                      Page {pageNumber} of {numPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={goToNextPage}
                      disabled={numPages ? pageNumber >= numPages : true}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              ) : (
                <p>No document loaded.</p>
              )}
              {commentPosition && (
                <div className="mt-4">
                  <Textarea
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="w-full mb-2"
                  />
                  <Button onClick={handleAddComment} size="sm">
                    Add Comment at ({commentPosition.x.toFixed(0)},{" "}
                    {commentPosition.y.toFixed(0)})
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="col-span-1 h-full flex flex-col">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Document Comments
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => setShowResolved(!showResolved)}
                  >
                    {showResolved ? "Hide Resolved" : "Show Resolved"}
                  </Badge>
                  <Badge variant="secondary">
                    {comments.filter((c) => !c.resolved).length} Active
                  </Badge>
                </div>
              </div>
            </CardHeader>

            <CardContent className="flex-1 flex flex-col">
              <div className="space-y-4 flex-1 flex flex-col">
                {user ? (
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Add a comment..."
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      className="flex-1"
                    />
                    <Button onClick={handleAddComment} size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                ) : (
                  <div className="text-sm text-gray-500 mb-2">
                    Please log in to add comments
                  </div>
                )}

                <Separator />

                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="text-center">
                      <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
                      <p className="mt-2 text-sm text-gray-500">
                        Loading comments...
                      </p>
                    </div>
                  </div>
                ) : filteredComments.length > 0 ? (
                  <ScrollArea className="flex-1">
                    <div className="space-y-3 pr-2">
                      {filteredComments.map((comment) => (
                        <div
                          key={comment.id}
                          className={`p-3 rounded-lg ${comment.resolved ? "bg-gray-50" : "bg-blue-50"}`}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarFallback className="text-xs">
                                  {comment.userName
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <span className="font-medium text-sm">
                                {comment.userName}
                              </span>
                              <span className="text-xs text-gray-500">
                                {formatDistanceToNow(
                                  new Date(comment.timestamp),
                                  { addSuffix: true }
                                )}
                              </span>
                            </div>

                            {!comment.resolved && user && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2"
                                onClick={() => resolveComment(comment.id)}
                              >
                                <Check className="h-3 w-3 mr-1" />
                                <span className="text-xs">Resolve</span>
                              </Button>
                            )}
                          </div>

                          <p className="text-sm mt-1">{comment.content}</p>

                          <div className="text-xs text-gray-500 mt-1 flex justify-between">
                            <span>on: {comment.documentId}</span>
                            {comment.documentId.startsWith("1") ||
                            comment.documentId.startsWith("2") ? (
                              <Link
                                to="/sign-documents"
                                className="text-blue-500 hover:underline"
                              >
                                View Document
                              </Link>
                            ) : null}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="text-center py-6 flex-1 flex flex-col justify-center">
                    <MessageSquare className="h-8 w-8 text-gray-400 mx-auto" />
                    <h3 className="mt-2 text-gray-900 font-medium">
                      No active comments
                    </h3>
                    <p className="text-gray-500 text-sm">
                      {showResolved
                        ? "No comments have been made yet"
                        : "No unresolved comments"}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div> */}
      </div>
    </DocumentCommentsContext.Provider>
  );
};

export default DocumentSigningContainer;
