import React, { useState } from "react";
import { Navigate, useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import { toast } from "sonner";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import Button from "@/components/common/Button";
import Layout from "@/components/Layout";

const MfaVerification: React.FC = () => {
  const { verifyMfa, isMfaVerified } = useAuth();
  const [code, setCode] = useState("");
  const [verifying, setVerifying] = useState(false);
  const navigate = useNavigate();
  const [params] = useSearchParams();

  // If authenticated and doesn't need MFA verification, redirect to dashboard
  if (isMfaVerified) {
    const from = "/dashboard";
    return <Navigate to={from} replace />;
  }
  const handleVerify = async () => {
    if (code.length !== 6) {
      toast.error("Please enter a 6-digit code");
      return;
    }

    setVerifying(true);
    try {
      const isVerified = await verifyMfa(params.get("email"), code);
      if (isVerified) {
        toast.success("Verification successful");
        const from = "/dashboard";
        navigate(from, { replace: true });
      } else {
        toast.error("Invalid verification code");
      }
    } catch (error) {
      toast.error("Verification failed. Please try again.");
    } finally {
      setVerifying(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-[calc(100vh-16rem)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <AnimatedTransition className="w-full max-w-md">
          <Card variant="glass" className="w-full">
            <CardHeader>
              <CardTitle className="text-xl md:text-2xl">
                Two-Factor Authentication
              </CardTitle>
              <CardDescription>
                Enter the 6-digit code to verify your identity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-center">
                <InputOTP
                  maxLength={6}
                  value={code}
                  onChange={setCode}
                  pattern="^[0-9]+$"
                  containerClassName="gap-2"
                >
                  <InputOTPGroup>
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTPGroup>
                </InputOTP>
              </div>

              <Button
                className="w-full"
                onClick={handleVerify}
                loading={verifying}
              >
                Verify
              </Button>

              <p className="text-center text-sm text-gray-500 mt-4">
                For demo purposes, enter any 6-digit code
              </p>
            </CardContent>
          </Card>
        </AnimatedTransition>
      </div>
    </Layout>
  );
};

export default MfaVerification;
