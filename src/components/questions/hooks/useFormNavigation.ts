import { FormStep } from "../types";
import { FORM_STEPS } from "../constants/formSteps";
import { useNavigate } from "react-router-dom";

export const useFormNavigation = (
  currentStep: FormStep,
  setCurrentStep: (step: FormStep) => void
) => {
  const navigate = useNavigate();

  const handleBack = () => {
    const currentIndex = FORM_STEPS.findIndex(
      (step) => step.id === currentStep
    );
    if (currentIndex > 0) {
      setCurrentStep(FORM_STEPS[currentIndex - 1].id);
    } else {
      navigate("/start");
    }
  };

  const handleNext = () => {
    const currentIndex = FORM_STEPS.findIndex(
      (step) => step.id === currentStep
    );

    if (currentIndex < FORM_STEPS.length - 1) {
      setCurrentStep(FORM_STEPS[currentIndex + 1].id);
    }
  };

  return {
    handleBack,
    handleNext,
  };
};
