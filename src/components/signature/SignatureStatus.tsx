import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/common/Card";
import { Check<PERSON><PERSON><PERSON>, Clock, UserCheck } from "lucide-react";

interface Signer {
  id: string;
  name: string;
  email: string;
  status: "pending" | "signed";
  signedAt?: string;
}

interface DocumentSignatureStatus {
  id: string;
  name: string;
  signers: Signer[];
  completedAt?: string;
  status: "pending" | "partial" | "completed";
}

interface SignatureStatusProps {
  documents: DocumentSignatureStatus[];
}

const SignatureStatus: React.FC<SignatureStatusProps> = ({ documents }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Signature Status</h2>

      {documents.length === 0 ? (
        <p className="text-gray-500">No documents awaiting signatures.</p>
      ) : (
        <div className="grid gap-6">
          {documents.map((doc) => (
            <Card key={doc.id}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{doc.name}</CardTitle>
                  <div
                    className={`px-3 py-1 rounded-full text-sm font-medium ${
                      doc.status === "completed"
                        ? "bg-green-100 text-green-800"
                        : doc.status === "partial"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {doc.status === "completed"
                      ? "All Signed"
                      : doc.status === "partial"
                        ? "Partially Signed"
                        : "Awaiting Signatures"}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {doc.signers.map((signer) => (
                    <div
                      key={signer.id}
                      className="flex items-center justify-between py-2 border-b last:border-0"
                    >
                      <div className="flex items-center">
                        <div
                          className={`h-8 w-8 rounded-full flex items-center justify-center mr-3 ${
                            signer.status === "signed"
                              ? "bg-green-100"
                              : "bg-gray-100"
                          }`}
                        >
                          {signer.status === "signed" ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Clock className="h-4 w-4 text-gray-500" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{signer.name}</p>
                          <p className="text-sm text-gray-500">
                            {signer.email}
                          </p>
                        </div>
                      </div>
                      <div className="text-sm">
                        {signer.status === "signed" ? (
                          <span className="text-green-600">
                            Signed {signer.signedAt}
                          </span>
                        ) : (
                          <span className="text-gray-500">Pending</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {doc.status === "completed" && doc.completedAt && (
                  <div className="mt-4 pt-3 border-t flex items-center">
                    <UserCheck className="h-5 w-5 text-green-600 mr-2" />
                    <span className="text-sm text-green-600">
                      All signatures completed on {doc.completedAt}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default SignatureStatus;
