import React from "react";
import { ExternalIncorporationFormData, DocumentUpload } from "../types";
import { Label } from "@/components/ui/label";
import Button from "@/components/common/Button";
import { Upload, CheckCircle, AlertCircle } from "lucide-react";

interface DocumentsStepProps {
  formData: ExternalIncorporationFormData;
  updateFormData: (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => void;
}

const DocumentsStep: React.FC<DocumentsStepProps> = ({
  formData,
  updateFormData,
}) => {
  const handleDocumentChange = (index: number, file: File | null) => {
    const updatedDocuments = [...formData.documents];

    if (file) {
      updatedDocuments[index] = {
        ...updatedDocuments[index],
        file,
        isUploaded: true,
        name: file.name,
      };
    } else {
      updatedDocuments[index] = {
        ...updatedDocuments[index],
        file: null,
        isUploaded: false,
      };
    }

    updateFormData("documents", updatedDocuments);
  };

  const getRequiredText = (isRequired: boolean) => {
    return isRequired ? (
      <span className="text-red-500 text-sm font-medium">(Required)</span>
    ) : (
      <span className="text-gray-500 text-sm">(Optional)</span>
    );
  };

  const countUploadedRequiredDocuments = () => {
    return formData.documents.filter((doc) => doc.isRequired && doc.isUploaded)
      .length;
  };

  const totalRequiredDocuments = formData.documents.filter(
    (doc) => doc.isRequired
  ).length;

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Please upload your existing corporate documents.
        </p>
      </div>

      <div className="p-3 bg-blue-50 border border-blue-100 rounded-md text-blue-700 text-sm mb-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium">Important:</p>
            <p>
              Upload each document in PDF format. Ensure all signatures are
              included in the documents.
            </p>
          </div>
        </div>
      </div>

      <div className="p-3 bg-gray-100 rounded-md mb-4">
        <div className="flex justify-between items-center">
          <span>Documents Uploaded:</span>
          <span className="font-medium">
            {countUploadedRequiredDocuments()} of {totalRequiredDocuments}{" "}
            required documents
          </span>
        </div>
      </div>

      <div className="space-y-4">
        {formData.documents.map((doc, index) => (
          <div key={doc.id} className="p-4 border rounded-lg bg-gray-50">
            <div className="flex justify-between items-center mb-2">
              <div>
                <Label className="font-medium">
                  {doc.name} {getRequiredText(doc.isRequired)}
                </Label>
              </div>
              {doc.isUploaded && (
                <div className="flex items-center text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  <span className="text-sm">Uploaded</span>
                </div>
              )}
            </div>

            <div className="mt-2">
              <div className="flex items-center">
                <input
                  type="file"
                  id={`document-${doc.id}`}
                  onChange={(e) =>
                    handleDocumentChange(index, e.target.files?.[0] || null)
                  }
                  className="hidden"
                  accept=".pdf,.docx,.doc"
                />
                <Label
                  htmlFor={`document-${doc.id}`}
                  className="cursor-pointer"
                >
                  <Button
                    type="button"
                    variant={doc.isUploaded ? "outline" : "primary"}
                    icon={<Upload size={16} />}
                  >
                    {doc.isUploaded ? "Replace File" : "Upload File"}
                  </Button>
                </Label>
                {doc.isUploaded && doc.file && (
                  <span className="ml-3 text-sm text-gray-600">
                    {doc.file.name} ({Math.round(doc.file.size / 1024)} KB)
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DocumentsStep;
