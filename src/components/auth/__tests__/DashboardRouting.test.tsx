import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { useLocation } from "react-router-dom";
import AuthRedirect from "../AuthRedirect";
import Login from "@/pages/Login";
import { RouterTestWrapper } from "@/test/utils";
import { USER_ROLES } from "@/integrations/legal-concierge/hooks/useUserProfile";

// Mock the hooks
const mockUseAuth = vi.fn();
const mockUseCompanySelection = vi.fn();
const mockUseUserPermissions = vi.fn();

vi.mock("@/contexts/AuthContext", () => ({
  useAuth: () => mockUseAuth(),
}));

vi.mock("@/contexts/CompanySelectionContext", () => ({
  useCompanySelection: () => mockUseCompanySelection(),
}));

vi.mock("@/integrations/legal-concierge/hooks/useUserProfile", () => ({
  useUserPermissions: (user: any, isCompanySelected: boolean) =>
    mockUseUserPermissions(user, isCompanySelected),
  USER_ROLES: {
    OWNER: "OWNER",
    COLLABORATOR: "COLLABORATOR",
    SIGNER: "SIGNER",
    ADMIN: "ADMIN",
  },
}));

// Mock Navigate component to track navigation
const mockNavigate = vi.fn();
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    Navigate: ({ to, replace }: { to: string; replace?: boolean }) => {
      mockNavigate(to, replace);
      return <div data-testid="navigate" data-to={to} data-replace={replace} />;
    },
  };
});

// Mock CompanySelectionGuard
vi.mock("../CompanySelectionGuard", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="company-selection-guard">{children}</div>
  ),
}));

// Mock Spinner
vi.mock("@/components/ui/spinner", () => ({
  Spinner: ({ size }: { size?: string }) => (
    <div data-testid="spinner" data-size={size}>
      Loading...
    </div>
  ),
}));

describe("Dashboard Routing Tests", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe("AuthRedirect Component", () => {
    const defaultAuthState = {
      isAuthenticated: true,
      loading: false,
      user: null,
    };

    const defaultCompanyState = {
      isCompanySelected: true,
      isCompanySelectionLoading: false,
    };

    it("should redirect ADMIN users to regular dashboard", () => {
      const adminUser = {
        id: "admin-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.ADMIN],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: adminUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: false,
        isSigner: false,
        isAdmin: true,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("should redirect OWNER users to regular dashboard", () => {
      const ownerUser = {
        id: "owner-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.OWNER],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: ownerUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: true,
        isCollaborator: false,
        isSigner: false,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("should redirect COLLABORATOR users to regular dashboard", () => {
      const collaboratorUser = {
        id: "collaborator-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.COLLABORATOR],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: collaboratorUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: true,
        isSigner: false,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("should redirect SIGNER users to signer-dashboard", () => {
      const signerUser = {
        id: "signer-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.SIGNER],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: signerUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: false,
        isSigner: true,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/signer-dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/signer-dashboard"
      );
    });

    it("should show loading spinner when auth is loading", () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        loading: true,
        user: null,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(screen.getByTestId("spinner")).toBeInTheDocument();
      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    it("should render children when not authenticated", () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        loading: false,
        user: null,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div data-testid="landing-page">Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(screen.getByTestId("landing-page")).toBeInTheDocument();
      expect(screen.getByText("Landing Page")).toBeInTheDocument();
    });

    it("should show company selection guard when company is not selected", () => {
      const ownerUser = {
        id: "owner-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.OWNER],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: ownerUser,
      });

      mockUseCompanySelection.mockReturnValue({
        isCompanySelected: false,
        isCompanySelectionLoading: false,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(screen.getByTestId("company-selection-guard")).toBeInTheDocument();
    });
  });

  describe("Login Component", () => {
    const defaultAuthState = {
      isAuthenticated: true,
      loading: false,
      user: null,
    };

    const defaultCompanyState = {
      isCompanySelected: true,
      isCompanySelectionLoading: false,
    };

    it("should redirect ADMIN users to regular dashboard from login", () => {
      const adminUser = {
        id: "admin-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.ADMIN],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: adminUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: false,
        isSigner: false,
        isAdmin: true,
      });

      render(
        <RouterTestWrapper>
          <Login />
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("should redirect OWNER users to regular dashboard from login", () => {
      const ownerUser = {
        id: "owner-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.OWNER],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: ownerUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: true,
        isCollaborator: false,
        isSigner: false,
      });

      render(
        <RouterTestWrapper>
          <Login />
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("should redirect COLLABORATOR users to regular dashboard from login", () => {
      const collaboratorUser = {
        id: "collaborator-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.COLLABORATOR],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: collaboratorUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: true,
        isSigner: false,
      });

      render(
        <RouterTestWrapper>
          <Login />
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("should redirect SIGNER users to signer-dashboard from login", () => {
      const signerUser = {
        id: "signer-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.SIGNER],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: signerUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: false,
        isSigner: true,
      });

      render(
        <RouterTestWrapper>
          <Login />
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/signer-dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/signer-dashboard"
      );
    });

    it("should handle users with multiple roles (ADMIN + OWNER) - should go to dashboard", () => {
      const multiRoleUser = {
        id: "multi-role-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.ADMIN, USER_ROLES.OWNER],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: multiRoleUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: true,
        isCollaborator: false,
        isSigner: false,
        isAdmin: true,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("should handle edge case: user with SIGNER + COLLABORATOR roles - should go to dashboard (non-signer takes precedence)", () => {
      const mixedRoleUser = {
        id: "mixed-role-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.SIGNER, USER_ROLES.COLLABORATOR],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: mixedRoleUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      // Based on useUserPermissions logic, if user has COLLABORATOR role, isSigner should be false
      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: true,
        isSigner: false, // This should be false when user has other roles
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });
  });

  describe("Dashboard Routing Summary - Key Requirements", () => {
    const defaultAuthState = {
      isAuthenticated: true,
      loading: false,
      user: null,
    };

    const defaultCompanyState = {
      isCompanySelected: true,
      isCompanySelectionLoading: false,
    };

    it("REQUIREMENT: Admin should land in the regular dashboard page", () => {
      const adminUser = {
        id: "admin-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.ADMIN],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: adminUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: false,
        isSigner: false,
        isAdmin: true,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      // Admin should go to regular dashboard, NOT signer-dashboard
      expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/dashboard"
      );
      expect(screen.getByTestId("navigate")).not.toHaveAttribute(
        "data-to",
        "/signer-dashboard"
      );
    });

    it("REQUIREMENT: Only SIGNER should land in the signer-dashboard", () => {
      const signerUser = {
        id: "signer-user-id",
        email: "<EMAIL>",
        roles: [USER_ROLES.SIGNER],
        companyId: "company-id",
      };

      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        user: signerUser,
      });

      mockUseCompanySelection.mockReturnValue(defaultCompanyState);

      mockUseUserPermissions.mockReturnValue({
        isOwner: false,
        isCollaborator: false,
        isSigner: true,
      });

      render(
        <RouterTestWrapper>
          <AuthRedirect>
            <div>Landing Page</div>
          </AuthRedirect>
        </RouterTestWrapper>
      );

      // Only SIGNER should go to signer-dashboard
      expect(mockNavigate).toHaveBeenCalledWith("/signer-dashboard", true);
      expect(screen.getByTestId("navigate")).toHaveAttribute(
        "data-to",
        "/signer-dashboard"
      );
      expect(screen.getByTestId("navigate")).not.toHaveAttribute(
        "data-to",
        "/dashboard"
      );
    });

    it("REQUIREMENT: All non-SIGNER roles (OWNER, COLLABORATOR, ADMIN) should go to regular dashboard", () => {
      const testCases = [
        {
          role: USER_ROLES.OWNER,
          permissions: {
            isOwner: true,
            isCollaborator: false,
            isSigner: false,
          },
        },
        {
          role: USER_ROLES.COLLABORATOR,
          permissions: {
            isOwner: false,
            isCollaborator: true,
            isSigner: false,
          },
        },
        {
          role: USER_ROLES.ADMIN,
          permissions: {
            isOwner: false,
            isCollaborator: false,
            isSigner: false,
            isAdmin: true,
          },
        },
      ];

      testCases.forEach(({ role, permissions }) => {
        vi.clearAllMocks();
        mockNavigate.mockClear();

        const user = {
          id: `${role.toLowerCase()}-user-id`,
          email: `${role.toLowerCase()}@example.com`,
          roles: [role],
          companyId: "company-id",
        };

        mockUseAuth.mockReturnValue({
          ...defaultAuthState,
          user,
        });

        mockUseCompanySelection.mockReturnValue(defaultCompanyState);
        mockUseUserPermissions.mockReturnValue(permissions);

        const { unmount } = render(
          <RouterTestWrapper>
            <AuthRedirect>
              <div>Landing Page</div>
            </AuthRedirect>
          </RouterTestWrapper>
        );

        // All non-SIGNER roles should go to regular dashboard
        expect(mockNavigate).toHaveBeenCalledWith("/dashboard", true);

        unmount();
      });
    });
  });
});
