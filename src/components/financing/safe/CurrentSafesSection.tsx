import React from "react";
import { AlertCircle, ExternalLink } from "lucide-react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { Safe } from "@/types/financing";
import { Link } from "react-router-dom";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Mock data for demonstration
const mockCurrentSafes: (Safe & { hasMfnTrigger?: boolean })[] = [
  {
    id: "1",
    dateAuthorized: new Date("2023-02-10"),
    authorizedAmount: 1000000,
    outstandingSafeAmount: 800000,
    valuationCap: 8000000,
    discount: 20,
    mfn: true,
    boardApproved: true,
    hasMfnTrigger: true,
  },
  {
    id: "2",
    dateAuthorized: new Date("2023-06-15"),
    authorizedAmount: 1500000,
    outstandingSafeAmount: 1200000,
    valuationCap: 10000000,
    discount: 15,
    mfn: false,
    boardApproved: true,
  },
];

const CurrentSafesSection: React.FC = () => {
  return (
    <Card>
      <CardContent className="pt-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date Authorized</TableHead>
              <TableHead>Authorized SAFE Amount</TableHead>
              <TableHead>Outstanding SAFE Amount</TableHead>
              <TableHead>Valuation Cap</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>MFN</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockCurrentSafes.map((safe) => (
              <TableRow key={safe.id}>
                <TableCell className="relative">
                  {safe.dateAuthorized
                    ? format(safe.dateAuthorized, "PP")
                    : "N/A"}
                  {safe.hasMfnTrigger && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertCircle className="h-4 w-4 text-red-500 ml-2 inline" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            Per the terms of the SAFE, you will need to amend
                            and restate all issued and outstanding SAFEs from
                            this round. Visit the MFN tab for further action.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <div className="mt-1">
                    <Link
                      to="/captable"
                      className="text-xs text-blue-600 flex items-center hover:underline"
                    >
                      Issued and Outstanding SAFEs
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </Link>
                  </div>
                </TableCell>
                <TableCell>${safe.authorizedAmount.toLocaleString()}</TableCell>
                <TableCell>
                  ${safe.outstandingSafeAmount.toLocaleString()}
                </TableCell>
                <TableCell>${safe.valuationCap.toLocaleString()}</TableCell>
                <TableCell>{safe.discount}%</TableCell>
                <TableCell>{safe.mfn ? "Yes" : "No"}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <p className="text-sm text-muted-foreground mt-4">
          Please make sure to update the Cap Table tab for accuracy.
        </p>
      </CardContent>
    </Card>
  );
};

export default CurrentSafesSection;
