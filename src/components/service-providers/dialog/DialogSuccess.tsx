import React from "react";

interface DialogSuccessProps {
  title: string;
  description: string;
  hasEquityGrant: boolean;
}

const DialogSuccess: React.FC<DialogSuccessProps> = ({
  title,
  description,
  hasEquityGrant,
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <div className="flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
        <svg
          className="w-8 h-8 text-green-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 13l4 4L19 7"
          />
        </svg>
      </div>
      <h2 className="text-xl font-semibold text-center">{title}</h2>
      <p className="text-center text-gray-600 mt-2 mb-6">{description}</p>
      {hasEquityGrant && (
        <p className="text-center text-green-600 font-medium">
          The equity grant details have been saved under Promised Grants.
        </p>
      )}
    </div>
  );
};

export default DialogSuccess;
