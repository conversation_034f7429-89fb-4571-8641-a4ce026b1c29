import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { PromisedGrant } from "@/types/serviceProvider";

export const promisedGrantService = {
  async getPromisedGrantsForUser(): Promise<PromisedGrant[]> {
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        console.error("User not authenticated");
        return [];
      }

      // Use RPC call instead of direct table access
      const { data, error } = await supabase
        .from("promised_grants")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching promised grants:", error);
        return [];
      }

      // Map DB data to our application type
      return data.map((item) => ({
        id: item.id,
        serviceProviderId: item.service_provider_id,
        serviceProviderName: item.service_provider_name,
        serviceProviderType: item.service_provider_type as any,
        vestingCommencementDate: new Date(item.vesting_commencement_date),
        grantType: item.grant_type as any,
        optionType: item.option_type as any,
        shares: item.shares,
        vestingSchedule: item.vesting_schedule,
        status: item.status as any,
      }));
    } catch (error) {
      console.error("Error in getPromisedGrantsForUser:", error);
      return [];
    }
  },

  async savePromisedGrant(
    grant: Omit<PromisedGrant, "id">
  ): Promise<PromisedGrant | null> {
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        toast.error("You must be logged in to save promised grants");
        return null;
      }

      // Get company ID - in a real app this would be stored in context or state
      // For simplicity, we'll use the first company associated with the user
      const { data: companyData } = await supabase
        .from("company_details")
        .select("id")
        .limit(1)
        .single();

      if (!companyData) {
        toast.error("No company found. Please set up your company first.");
        return null;
      }

      // Convert app type to DB format
      const dbGrant = {
        user_id: userData.user.id,
        company_id: companyData.id,
        service_provider_id: grant.serviceProviderId,
        service_provider_name: grant.serviceProviderName,
        service_provider_type: grant.serviceProviderType,
        vesting_commencement_date: grant.vestingCommencementDate
          .toISOString()
          .split("T")[0],
        grant_type: grant.grantType,
        option_type: grant.optionType,
        shares: grant.shares,
        vesting_schedule: grant.vestingSchedule,
        status: grant.status || "Promised",
      };

      const { data, error } = await supabase
        .from("promised_grants")
        .insert(dbGrant)
        .select();

      if (error) {
        console.error("Error saving promised grant:", error);
        toast.error("Failed to save promised grant");
        return null;
      }

      toast.success("Promised grant saved successfully");

      // Map the response back to app type
      const result = data[0];
      return {
        id: result.id,
        serviceProviderId: result.service_provider_id,
        serviceProviderName: result.service_provider_name,
        serviceProviderType: result.service_provider_type as any,
        vestingCommencementDate: new Date(result.vesting_commencement_date),
        grantType: result.grant_type as any,
        optionType: result.option_type as any,
        shares: result.shares,
        vestingSchedule: result.vesting_schedule,
        status: result.status as any,
      };
    } catch (error) {
      console.error("Error in savePromisedGrant:", error);
      toast.error("An error occurred while saving the promised grant");
      return null;
    }
  },

  async getGrantsSummary(): Promise<{
    totalShares: number;
    allocatedShares: number;
    remainingShares: number;
  }> {
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        console.error("User not authenticated");
        return {
          totalShares: 0,
          allocatedShares: 0,
          remainingShares: 0,
        };
      }

      // Get company ID
      const { data: companyData } = await supabase
        .from("company_details")
        .select("id")
        .limit(1)
        .single();

      if (!companyData) {
        return {
          totalShares: 0,
          allocatedShares: 0,
          remainingShares: 0,
        };
      }

      // Call our custom RPC function
      const { data, error } = await supabase.rpc(
        "get_promised_grants_summary",
        {
          p_user_id: userData.user.id,
          p_company_id: companyData.id,
        }
      );

      if (error) {
        console.error("Error getting grants summary:", error);
        return {
          totalShares: 0,
          allocatedShares: 0,
          remainingShares: 0,
        };
      }

      // Return the first row of data
      if (data && data.length > 0) {
        return {
          totalShares: data[0].total_shares || 0,
          allocatedShares: data[0].allocated_shares || 0,
          remainingShares: data[0].remaining_shares || 0,
        };
      }

      return {
        totalShares: 0,
        allocatedShares: 0,
        remainingShares: 0,
      };
    } catch (error) {
      console.error("Error in getGrantsSummary:", error);
      return {
        totalShares: 0,
        allocatedShares: 0,
        remainingShares: 0,
      };
    }
  },

  async updateGrantStatus(
    id: string,
    status: PromisedGrant["status"]
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("promised_grants")
        .update({ status, updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) {
        console.error("Error updating grant status:", error);
        toast.error("Failed to update grant status");
        return false;
      }

      toast.success(`Grant status updated to ${status}`);
      return true;
    } catch (error) {
      console.error("Error in updateGrantStatus:", error);
      toast.error("An error occurred while updating the grant status");
      return false;
    }
  },
};
