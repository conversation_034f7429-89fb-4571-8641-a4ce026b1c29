import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CollaboratorRole } from "./types";
import { toast } from "sonner";
import { User } from "@/contexts/auth/types";

interface InviteCollaboratorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInvite: (email: string, role: CollaboratorRole) => Promise<void>;
  userData: User | null;
}

const InviteCollaboratorDialog: React.FC<InviteCollaboratorDialogProps> = ({
  open,
  onOpenChange,
  onInvite,
  userData
}) => {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState<CollaboratorRole>(
    userData?.roles?.includes('ADMIN') ? 'owner' : 'collaborator'
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      toast.error("Please enter an email address");
      return;
    }

    setIsSubmitting(true);

    try {
      await onInvite(email, role);
      setEmail("");
      setRole(role);
    } catch (error) {
      console.error("Error inviting collaborator:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {userData?.roles?.includes('ADMIN') ? "Invite User" : "Invite Collaborator"}
          </DialogTitle>
          <DialogDescription>
            Send an invitation to collaborate on your incorporation documents.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email address</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Permission Level</Label>
            <Select
              value={role}
              onValueChange={(value: CollaboratorRole) => setRole(value)}
            >
              <SelectTrigger id="role">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {userData?.roles?.includes('ADMIN') ? (
                    <>
                      <SelectItem value="owner">
                        Admin (Full editing rights)
                      </SelectItem>
                    </>
                  ) : (
                    <>
                     <SelectItem value="collaborator">
                      Collaborator (Can comment and make suggestions)
                    </SelectItem>
                    <SelectItem value="signer">
                      Signer (Can only view and sign documents)
                    </SelectItem>
                    </>
                  )}
                </SelectGroup>
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              {role === "collaborator" &&
                "Can view details and add comments, but cannot edit directly."}
              {role === "owner" &&
                "Can edit documents and make changes to the incorporation."}
            </p>
          </div>

          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Sending..." : "Send Invitation"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default InviteCollaboratorDialog;
