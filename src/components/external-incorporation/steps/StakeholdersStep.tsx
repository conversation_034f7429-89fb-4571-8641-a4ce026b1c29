import React from "react";
import { ExternalIncorporationFormData, StakeholderData } from "../types";
import Button from "@/components/common/Button";
import { PlusCircle } from "lucide-react";
import StakeholderForm from "../components/StakeholderForm";
import { v4 as uuidv4 } from "uuid";

interface StakeholdersStepProps {
  formData: ExternalIncorporationFormData;
  updateFormData: (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => void;
}

const StakeholdersStep: React.FC<StakeholdersStepProps> = ({
  formData,
  updateFormData,
}) => {
  const addStakeholder = () => {
    const newStakeholder: StakeholderData = {
      id: uuidv4(),
      name: "",
      isDirector: false,
      officerTitles: [],
      emailAddress: "",
      address: "",
      stockOwnershipPercentage: 0,
      sharesAmount: 0,
      isVester: false,
      vestingSchedule: "4-year-1-year-cliff",
      acceleration: "none",
    };

    updateFormData("stakeholders", [...formData.stakeholders, newStakeholder]);
  };

  const removeStakeholder = (id: string) => {
    updateFormData(
      "stakeholders",
      formData.stakeholders.filter((s) => s.id !== id)
    );

    // Also remove any tech contributions for this stakeholder
    updateFormData(
      "techContributions",
      formData.techContributions.filter((c) => c.stakeholderId !== id)
    );
  };

  const updateStakeholderField = (
    id: string,
    field: keyof StakeholderData,
    value: any
  ) => {
    updateFormData(
      "stakeholders",
      formData.stakeholders.map((s) =>
        s.id === id ? { ...s, [field]: value } : s
      )
    );

    // If updating stock percentage, also update shares amount
    if (field === "stockOwnershipPercentage") {
      updateFormData(
        "stakeholders",
        formData.stakeholders.map((s) =>
          s.id === id
            ? {
                ...s,
                [field]: value,
                sharesAmount: Math.round((value / 100) * formData.issuedShares),
              }
            : s
        )
      );
    }
  };

  const toggleOfficerTitle = (id: string, title: string) => {
    updateFormData(
      "stakeholders",
      formData.stakeholders.map((s) => {
        if (s.id === id) {
          const updatedTitles = s.officerTitles.includes(title)
            ? s.officerTitles.filter((t) => t !== title)
            : [...s.officerTitles, title];
          return { ...s, officerTitles: updatedTitles };
        }
        return s;
      })
    );
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Add all directors, officers, and shareholders of your company.
        </p>
      </div>

      <div className="space-y-6">
        {formData.stakeholders.length === 0 ? (
          <div className="text-center p-6 bg-gray-50 rounded-lg">
            <p className="text-gray-500 mb-4">
              No directors/officers/shareholders added yet
            </p>
            <Button onClick={addStakeholder} icon={<PlusCircle size={16} />}>
              Add Person
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {formData.stakeholders.map((stakeholder, index) => (
              <StakeholderForm
                key={stakeholder.id}
                stakeholder={stakeholder}
                index={index}
                totalShares={formData.issuedShares}
                onRemove={removeStakeholder}
                onUpdate={updateStakeholderField}
                onToggleOfficerTitle={toggleOfficerTitle}
              />
            ))}

            <Button
              onClick={addStakeholder}
              icon={<PlusCircle size={16} />}
              variant="outline"
              className="mt-4"
            >
              Add Another Person
            </Button>
          </div>
        )}
      </div>

      <div className="mt-4 p-3 bg-gray-100 rounded-md">
        <div className="flex justify-between mb-2">
          <span>Total Ownership:</span>
          <span>
            {formData.stakeholders
              .reduce((sum, s) => sum + s.stockOwnershipPercentage, 0)
              .toFixed(2)}
            %
          </span>
        </div>
        <div className="flex justify-between font-medium">
          <span>Total Shares:</span>
          <span>{formData.issuedShares.toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
};

export default StakeholdersStep;
