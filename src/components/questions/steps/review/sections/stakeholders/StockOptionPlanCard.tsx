import React from "react";

interface StockOptionPlanCardProps {
  percentage: number;
  issuedShares: number;
}

const StockOptionPlanCard: React.FC<StockOptionPlanCardProps> = ({
  percentage,
  issuedShares,
}) => {
  const sharesAmount = Math.round(
    (percentage / 100) * issuedShares
  ).toLocaleString();

  return (
    <div className="p-3 border rounded-md bg-gray-50">
      <h4 className="font-medium mb-2">Stock Option Plan</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <p className="text-sm text-gray-500">Percentage</p>
          <p>{percentage}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Shares</p>
          <p>{sharesAmount}</p>
        </div>
      </div>
    </div>
  );
};

export default StockOptionPlanCard;
