import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import {
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface NoStockOptionPlanStateProps {
  handleClose: () => void;
}

const NoStockOptionPlanState: React.FC<NoStockOptionPlanStateProps> = ({
  handleClose,
}) => {
  return (
    <>
      <DialogHeader>
        <DialogTitle>Create Stock Option Plan</DialogTitle>
        <DialogDescription>
          You don't have a stock option plan set up yet
        </DialogDescription>
      </DialogHeader>

      <div className="py-6 text-center">
        <p className="mb-4">
          To increase your stock option plan, you first need to create one.
          Would you like to set up a stock option plan now?
        </p>
        <Button asChild>
          <Link to="/questions">Create Stock Option Plan</Link>
        </Button>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={handleClose}>
          Cancel
        </Button>
      </DialogFooter>
    </>
  );
};

export default NoStockOptionPlanState;
