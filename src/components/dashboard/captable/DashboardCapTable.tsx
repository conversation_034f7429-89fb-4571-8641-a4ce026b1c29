import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useCapTableData } from "@/hooks/useCapTableData";
import {
  <PERSON><PERSON>ie,
  CircleDollarSign,
  Users,
  FileBar<PERSON>hart,
  ArrowRight,
} from "lucide-react";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { Pie<PERSON><PERSON>, Pie, Cell } from "recharts";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
  "#b19cd9",
];

const DashboardCapTable: React.FC = () => {
  const { loading, currentCapTable } = useCapTableData();

  if (loading || !currentCapTable) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-lg">Cap Table Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-52">
            <p className="text-muted-foreground">Loading cap table data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Prepare data for the pie chart - top 5 shareholders + "Others"
  const shareholders = [...currentCapTable.shareholders].sort(
    (a, b) => b.percentage - a.percentage
  );

  // Calculate shares remaining in option pool if present
  const optionPlan = currentCapTable.stockOptionPlan;
  const optionPoolTotal = optionPlan?.totalShares || 0;

  // Calculate allocated option shares (if any grants exist)
  const allocatedOptions =
    optionPlan?.grants?.reduce((sum, grant) => sum + grant.optionsGranted, 0) ||
    0;

  // Calculate remaining option shares
  const remainingOptions = optionPoolTotal - allocatedOptions;

  let chartData;
  if (shareholders.length <= 4) {
    // Reducing to 4 to make room for option pool
    chartData = shareholders.map((s, i) => ({
      name: s.name,
      value: s.totalShares,
      color: COLORS[i % COLORS.length],
    }));
  } else {
    // Take top 4 and group others (reduced from 5 to make room for option pool)
    const top4 = shareholders.slice(0, 4);
    const others = shareholders.slice(4);
    const othersShares = others.reduce((sum, s) => sum + s.totalShares, 0);

    chartData = [
      ...top4.map((s, i) => ({
        name: s.name,
        value: s.totalShares,
        color: COLORS[i % COLORS.length],
      })),
      {
        name: "Others",
        value: othersShares,
        color: "#A9A9A9",
      },
    ];
  }

  // Add remaining options to chart data
  if (remainingOptions > 0) {
    chartData.push({
      name: "Shares Remaining (Options)",
      value: remainingOptions,
      color: COLORS[6], // Using the last color in our palette
    });
  }

  return (
    <Card className="w-full mb-4">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Cap Table Summary</CardTitle>
          <Button variant="ghost" size="sm" asChild>
            <Link to="/cap-table" className="flex items-center gap-1">
              View Full Cap Table <ArrowRight size={16} />
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
            <Users className="text-blue-500" />
            <div>
              <p className="text-sm text-gray-500">Shareholders</p>
              <p className="font-semibold">
                {currentCapTable.shareholders.length}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
            <FileBarChart className="text-indigo-500" />
            <div>
              <p className="text-sm text-gray-500">Share Classes</p>
              <p className="font-semibold">
                {currentCapTable.shareClasses.length}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
            <ChartPie className="text-green-500" />
            <div>
              <p className="text-sm text-gray-500">Total Shares</p>
              <p className="font-semibold">
                {currentCapTable.totalShares.toLocaleString()}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
            <CircleDollarSign className="text-purple-500" />
            <div>
              <p className="text-sm text-gray-500">Fully Diluted</p>
              <p className="font-semibold">
                {currentCapTable.totalFullyDiluted.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-center mt-4 h-[180px]">
          <ChartContainer
            config={chartData.reduce(
              (acc, item) => ({
                ...acc,
                [item.name]: {
                  color: item.color,
                  label: item.name,
                },
              }),
              {}
            )}
          >
            <PieChart>
              <Pie
                data={chartData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={70}
                fill="#8884d8"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
            </PieChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardCapTable;
