import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Info } from "lucide-react";
import {
  Too<PERSON>ip,
  Tooltip<PERSON>ontent,
  Tooltip<PERSON>rigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import {
  CapTableSummary,
  ProFormaVoting as ProFormaVotingType,
} from "@/types/capTable";

interface ProFormaVotingProps {
  votingData?: ProFormaVotingType;
  capTableData: CapTableSummary;
}

const ProFormaVoting: React.FC<ProFormaVotingProps> = ({
  votingData,
  capTableData,
}) => {
  if (!votingData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Voting Rights</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">
            No voting rights data available. Please configure voting power to
            view this analysis.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Helper function to format numbers
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // Helper to get badge color based on voting power
  const getVotingPowerBadge = (power: "High" | "Medium" | "Low") => {
    switch (power) {
      case "High":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            High
          </Badge>
        );
      case "Medium":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Medium
          </Badge>
        );
      case "Low":
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            Low
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Voting Rights Analysis</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="text-gray-400 hover:text-gray-600">
                  <Info size={16} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  This analysis shows the voting power distribution among
                  shareholders, including key thresholds for corporate
                  decisions.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
        <CardDescription>
          Voting thresholds: Majority ({votingData.threshold.majority}%),
          Supermajority ({votingData.threshold.supermajority}%)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Shareholder</TableHead>
                <TableHead>Voting Shares</TableHead>
                <TableHead>Voting %</TableHead>
                <TableHead>Voting Power</TableHead>
                <TableHead>Can Block Majority?</TableHead>
                <TableHead>Can Block Supermajority?</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {votingData.votingRights.map((right, index) => {
                const canBlockMajority =
                  right.votingPercentage > 100 - votingData.threshold.majority;
                const canBlockSupermajority =
                  right.votingPercentage >
                  100 - votingData.threshold.supermajority;

                return (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {right.shareholder}
                    </TableCell>
                    <TableCell>{formatNumber(right.votingShares)}</TableCell>
                    <TableCell>{right.votingPercentage.toFixed(2)}%</TableCell>
                    <TableCell>
                      {getVotingPowerBadge(right.votingPower)}
                    </TableCell>
                    <TableCell>
                      {canBlockMajority ? (
                        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                          Yes
                        </Badge>
                      ) : (
                        "No"
                      )}
                    </TableCell>
                    <TableCell>
                      {canBlockSupermajority ? (
                        <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">
                          Yes
                        </Badge>
                      ) : (
                        "No"
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        <div className="mt-6">
          <h3 className="text-base font-medium mb-3">Voting Analysis</h3>
          <div className="space-y-2">
            <p className="text-sm">
              <span className="font-medium">Majority Vote Threshold: </span>
              {votingData.threshold.majority}% required for standard decisions
              like electing directors.
            </p>
            <p className="text-sm">
              <span className="font-medium">
                Supermajority Vote Threshold:{" "}
              </span>
              {votingData.threshold.supermajority}% required for major decisions
              like selling the company.
            </p>

            {votingData.votingRights.some(
              (r) => r.votingPercentage > votingData.threshold.majority
            ) && (
              <p className="text-sm text-amber-600">
                <span className="font-medium">⚠️ Majority Control: </span>
                {
                  votingData.votingRights.find(
                    (r) => r.votingPercentage > votingData.threshold.majority
                  )?.shareholder
                }{" "}
                has majority control.
              </p>
            )}

            {votingData.votingRights.some(
              (r) =>
                r.votingPercentage > 100 - votingData.threshold.supermajority
            ) && (
              <p className="text-sm text-amber-600">
                <span className="font-medium">⚠️ Blocking Rights: </span>
                {votingData.votingRights
                  .filter(
                    (r) =>
                      r.votingPercentage >
                      100 - votingData.threshold.supermajority
                  )
                  .map((r) => r.shareholder)
                  .join(", ")}{" "}
                can block supermajority decisions.
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProFormaVoting;
