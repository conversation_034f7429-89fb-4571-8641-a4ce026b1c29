import React, { useEffect, useRef, useCallback } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Input } from "@/components/ui/input";
import {
  ChevronDown,
  ChevronUp,
  Plus,
  Trash,
  Edit,
  Loader2,
} from "lucide-react";
import { QuestionnaireFormData } from "../types";
import { toast } from "sonner";
import {
  useUpdateTechnology,
  useAddOfficerIP,
  useUpdateOfficerIP,
  useDeleteOfficerIP,
} from "@/integrations/legal-concierge/hooks";
import { useAuth } from "@/contexts/AuthContext";
import { Checkbox } from "@/components/ui/checkbox";
import clsx from "clsx";

interface TechItem {
  id: string;
  description: string;
  isExcluded: boolean;
  isSaving?: boolean;
  isDeleting?: boolean;
}

interface TechStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: unknown) => void;
}

const TechStep: React.FC<TechStepProps> = ({ formData, updateFormData }) => {
  const [openSections, setOpenSections] = React.useState<string[]>([]);
  const [editingTechItem, setEditingTechItem] = React.useState<{
    founderId: string;
    techItemId: string;
  } | null>(null);
  const [newTechItems, setNewTechItems] = React.useState<
    Record<
      string,
      {
        description: string;
        isExcluded: boolean;
      }
    >
  >({});
  const [companyTechDescription, setCompanyTechDescription] = React.useState(
    formData.companyTech
  );

  // Get user and company ID from auth context
  const { user } = useAuth();
  const companyId = user?.companyId;

  // API mutation hooks
  const updateTechnology = useUpdateTechnology();
  const addOfficerIP = useAddOfficerIP();
  const updateOfficerIP = useUpdateOfficerIP();
  const deleteOfficerIP = useDeleteOfficerIP();

  // Initialize founderTechAssignments based on directors if empty
  useEffect(() => {
    if (
      formData.directors.length > 0 &&
      formData.founderTechAssignments.length === 0
    ) {
      const initialAssignments = formData.directors.map((director) => ({
        founderId: director.id,
        founderName: director.name,
        techItems: [],
      }));
      updateFormData("founderTechAssignments", initialAssignments);

      // Open the first section automatically
      if (formData.directors.length > 0) {
        setOpenSections([formData.directors[0].id]);
      }
    }
  }, [
    formData.directors,
    formData.founderTechAssignments.length,
    updateFormData,
  ]);

  // Toggle section open/close
  const toggleSection = (founderId: string) => {
    setOpenSections((prev) =>
      prev.includes(founderId)
        ? prev.filter((id) => id !== founderId)
        : [...prev, founderId]
    );
  };

  // Handle input change for new tech items
  const handleNewTechItemChange = (founderId: string, value: string) => {
    setNewTechItems({
      ...newTechItems,
      [founderId]: {
        description: value,
        isExcluded: newTechItems[founderId]?.isExcluded || false,
      },
    });
  };

  // Handle excluded change for new tech items
  const handleNewTechItemExcludedChange = (
    founderId: string,
    checked: boolean
  ) => {
    setNewTechItems({
      ...newTechItems,
      [founderId]: {
        description: newTechItems[founderId]?.description || "",
        isExcluded: checked,
      },
    });
  };

  // Add a new tech item for a founder
  const addTechItem = async (founderId: string) => {
    const techItem = newTechItems[founderId];
    const techDescription = techItem?.description.trim();

    if (!techDescription || !companyId) return;

    // Clear the input immediately
    setNewTechItems({
      ...newTechItems,
      [founderId]: {
        description: "",
        isExcluded: false,
      },
    });

    try {
      // Show loading toast
      toast.loading("Adding intellectual property...", { id: "add-ip" });

      // Call the API to add the intellectual property
      console.log("Adding new IP item:", techDescription);
      const result = await addOfficerIP.mutateAsync({
        officerId: founderId,
        companyId,
        name: techDescription,
        isExcluded: techItem.isExcluded,
      });
      console.log("Server returned ID:", result.id);

      // After successful API call, update the UI with the new item
      const updatedAssignments = [...formData.founderTechAssignments];
      const founderIndex = updatedAssignments.findIndex(
        (f) => f.founderId === founderId
      );

      if (founderIndex !== -1) {
        // Add the new item with the server-generated ID
        const newItem: TechItem = {
          id: result.id,
          description: techDescription,
          isExcluded: techItem.isExcluded,
          isSaving: false,
        };

        updatedAssignments[founderIndex] = {
          ...updatedAssignments[founderIndex],
          techItems: [...updatedAssignments[founderIndex].techItems, newItem],
        };

        // Update form data with the new state
        updateFormData("founderTechAssignments", updatedAssignments);
      }

      toast.success("Intellectual property added successfully", {
        id: "add-ip",
      });
    } catch (error) {
      console.error("Error adding intellectual property:", error);
      toast.error("Failed to add intellectual property", { id: "add-ip" });
    }
  };

  // Delete a tech item
  const deleteTechItem = async (founderId: string, techItemId: string) => {
    if (!companyId) return;

    // Clear editing state if this item was being edited
    if (
      editingTechItem?.founderId === founderId &&
      editingTechItem?.techItemId === techItemId
    ) {
      setEditingTechItem(null);
    }

    try {
      // Show loading toast
      toast.loading("Deleting intellectual property...", { id: "delete-ip" });

      // Call the API to delete the intellectual property
      await deleteOfficerIP.mutateAsync({
        id: techItemId,
        companyId,
      });

      // After successful API call, update the UI
      const updatedAssignments = [...formData.founderTechAssignments];
      const founderIndex = updatedAssignments.findIndex(
        (f) => f.founderId === founderId
      );

      if (founderIndex !== -1) {
        // Remove the item from the UI
        updatedAssignments[founderIndex] = {
          ...updatedAssignments[founderIndex],
          techItems: updatedAssignments[founderIndex].techItems.filter(
            (item: TechItem) => item.id !== techItemId
          ),
        };

        // Update form data with the new state
        updateFormData("founderTechAssignments", updatedAssignments);
      }

      toast.success("Intellectual property deleted successfully", {
        id: "delete-ip",
      });
    } catch (error) {
      console.error("Error deleting intellectual property:", error);
      toast.error("Failed to delete intellectual property", {
        id: "delete-ip",
      });
    }
  };

  // Start editing a tech item
  const startEditingTechItem = (
    founderId: string,
    techItemId: string,
    currentDescription: string,
    currentExcluded: boolean
  ) => {
    setEditingTechItem({ founderId, techItemId });

    // Pre-fill the input with the current description and excluded state
    setNewTechItems({
      ...newTechItems,
      [`${founderId}-${techItemId}`]: {
        description: currentDescription,
        isExcluded: currentExcluded,
      },
    });
  };

  // Save edited tech item
  const saveEditedTechItem = async (founderId: string, techItemId: string) => {
    const editedItem = newTechItems[`${founderId}-${techItemId}`];
    const updatedDescription = editedItem?.description.trim();

    if (!updatedDescription || !companyId) return;

    // Clear editing state
    setEditingTechItem(null);

    // Clear the input
    const updatedNewTechItems = { ...newTechItems };
    delete updatedNewTechItems[`${founderId}-${techItemId}`];
    setNewTechItems(updatedNewTechItems);

    try {
      // Show loading toast
      toast.loading("Updating intellectual property...", { id: "update-ip" });

      // Get the current tech item
      const currentAssignments = [...formData.founderTechAssignments];
      const founderIndex = currentAssignments.findIndex(
        (f) => f.founderId === founderId
      );

      if (founderIndex === -1) return;

      const techItemIndex = currentAssignments[
        founderIndex
      ].techItems.findIndex((item: TechItem) => item.id === techItemId);

      if (techItemIndex === -1) return;

      // Update the item on the server
      await updateOfficerIP.mutateAsync({
        id: techItemId,
        companyId,
        name: updatedDescription,
        isExcluded: editedItem.isExcluded,
      });

      // After successful API call, update the UI
      const updatedAssignments = [...formData.founderTechAssignments];
      const updatedFounderIndex = updatedAssignments.findIndex(
        (f) => f.founderId === founderId
      );

      if (updatedFounderIndex !== -1) {
        const updatedTechItemIndex = updatedAssignments[
          updatedFounderIndex
        ].techItems.findIndex((item: TechItem) => item.id === techItemId);

        if (updatedTechItemIndex !== -1) {
          // Update the item with the new description and excluded state
          updatedAssignments[updatedFounderIndex].techItems[
            updatedTechItemIndex
          ] = {
            ...updatedAssignments[updatedFounderIndex].techItems[
              updatedTechItemIndex
            ],
            description: updatedDescription,
            isExcluded: editedItem.isExcluded,
          };

          // Update form data with the new state
          updateFormData("founderTechAssignments", updatedAssignments);
        }
      }

      toast.success("Intellectual property updated successfully", {
        id: "update-ip",
      });
    } catch (error) {
      console.error("Error updating intellectual property:", error);
      toast.error("Failed to update intellectual property", {
        id: "update-ip",
      });
    }
  };

  // Handle input change for editing tech items
  const handleEditTechItemChange = (
    founderId: string,
    techItemId: string,
    value: string
  ) => {
    setNewTechItems({
      ...newTechItems,
      [`${founderId}-${techItemId}`]: {
        description: value,
        isExcluded:
          newTechItems[`${founderId}-${techItemId}`]?.isExcluded || false,
      },
    });
  };

  // Handle company tech description change
  const handleCompanyTechChange = (value: string) => {
    setCompanyTechDescription(value);
    updateFormData("companyTech", value);
  };

  // Update form data when company tech description changes
  useEffect(() => {
    updateFormData("companyTech", companyTechDescription);
  }, [companyTechDescription, updateFormData]);

  // Add a new function to handle exclusion toggle
  const toggleExclusion = async (
    founderId: string,
    techItemId: string,
    currentExcluded: boolean
  ) => {
    if (!companyId) return;

    try {
      // Show loading toast
      toast.loading("Updating intellectual property...", { id: "update-ip" });

      // Get the current tech item
      const currentAssignments = [...formData.founderTechAssignments];
      const founderIndex = currentAssignments.findIndex(
        (f) => f.founderId === founderId
      );

      if (founderIndex === -1) return;

      const techItemIndex = currentAssignments[
        founderIndex
      ].techItems.findIndex((item: TechItem) => item.id === techItemId);

      if (techItemIndex === -1) return;

      // Update the item on the server
      await updateOfficerIP.mutateAsync({
        id: techItemId,
        companyId,
        name: currentAssignments[founderIndex].techItems[techItemIndex]
          .description,
        isExcluded: !currentExcluded,
      });

      // After successful API call, update the UI
      const updatedAssignments = [...formData.founderTechAssignments];
      const updatedFounderIndex = updatedAssignments.findIndex(
        (f) => f.founderId === founderId
      );

      if (updatedFounderIndex !== -1) {
        const updatedTechItemIndex = updatedAssignments[
          updatedFounderIndex
        ].techItems.findIndex((item: TechItem) => item.id === techItemId);

        if (updatedTechItemIndex !== -1) {
          // Update the item with the new excluded state
          updatedAssignments[updatedFounderIndex].techItems[
            updatedTechItemIndex
          ] = {
            ...updatedAssignments[updatedFounderIndex].techItems[
              updatedTechItemIndex
            ],
            isExcluded: !currentExcluded,
          };

          // Update form data with the new state
          updateFormData("founderTechAssignments", updatedAssignments);
        }
      }

      toast.success("Intellectual property updated successfully", {
        id: "update-ip",
      });
    } catch (error) {
      console.error("Error updating intellectual property:", error);
      toast.error("Failed to update intellectual property", {
        id: "update-ip",
      });
    }
  };
  return (
    <div className="space-y-6">
      <div className="mb-8 p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">
          Describe the company's technology
        </h2>
        <p className="text-gray-600 mb-4">
          {formData.registerMode == "getting-started"
            ? `Provide a detailed description of your company's technology, products,
          or services. This information will be used in your company formation
          documents.`
            : `Provide a detailed description of your company's technology, products,
          or services. This is the information that was used in your company formation
          documents.`}
        </p>
        <div className="space-y-4">
          <Textarea
            id="company-tech"
            value={companyTechDescription}
            onChange={(e) => handleCompanyTechChange(e.target.value)}
            placeholder="Describe your company's technology, innovative aspects, products, or services..."
            className="min-h-[150px] w-full"
          />
        </div>
      </div>

      <div className="space-y-2">
        <h2 className="text-xl font-semibold mb-4">
          {formData.registerMode == "getting-started"
            ? "What IP, if any, is the founder assigning to the Company?"
            : "What IP, if any, was the founder assigning to the Company?"}
        </h2>

        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            {formData.registerMode == "getting-started"
              ? `Please include any known trademarks, patents, copyrights, and any applications thereto as well as any IP to be excluded from assignment.`
              : `Please include any known trademarks, patents, copyrights, and any applications thereto as well as any IP that was excluded from assignment.`}
          </p>

          {formData.founderTechAssignments.map((assignment) => (
            <Card key={assignment.founderId} className="overflow-hidden">
              <Collapsible
                open={openSections.includes(assignment.founderId)}
                onOpenChange={() => toggleSection(assignment.founderId)}
              >
                <CollapsibleTrigger asChild>
                  <div className="p-4 flex justify-between items-center cursor-pointer bg-gray-50 hover:bg-gray-100">
                    <h3 className="text-lg font-medium">
                      {assignment.founderName}
                    </h3>
                    {openSections.includes(assignment.founderId) ? (
                      <ChevronUp className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500" />
                    )}
                  </div>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent className="pt-4">
                    {assignment.techItems.length === 0 ? (
                      <p className="text-sm text-gray-500">
                        No IP assigned yet
                      </p>
                    ) : (
                      <ul className="space-y-3">
                        {assignment.techItems.map((item: TechItem) => (
                          <li
                            key={item.id}
                            className="p-2 border border-gray-200 rounded"
                          >
                            {editingTechItem?.founderId ===
                              assignment.founderId &&
                            editingTechItem?.techItemId === item.id ? (
                              <div className="flex flex-col space-y-2">
                                <div className="flex items-start space-x-2">
                                  <Input
                                    value={
                                      newTechItems[
                                        `${assignment.founderId}-${item.id}`
                                      ]?.description || ""
                                    }
                                    onChange={(e) =>
                                      handleEditTechItemChange(
                                        assignment.founderId,
                                        item.id,
                                        e.target.value
                                      )
                                    }
                                    className="flex-1"
                                    placeholder="Describe the IP/tech..."
                                  />
                                  <div className="flex items-center space-x-2 space-y-3">
                                    <Checkbox
                                      id={`edit-excluded-${item.id}`}
                                      checked={
                                        newTechItems[
                                          `${assignment.founderId}-${item.id}`
                                        ]?.isExcluded || false
                                      }
                                      className="mt-2"
                                      onCheckedChange={(checked) => {
                                        setNewTechItems({
                                          ...newTechItems,
                                          [`${assignment.founderId}-${item.id}`]:
                                            {
                                              ...newTechItems[
                                                `${assignment.founderId}-${item.id}`
                                              ],
                                              isExcluded: checked as boolean,
                                            },
                                        });
                                      }}
                                    />
                                    <label
                                      htmlFor={`edit-excluded-${item.id}`}
                                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                    >
                                      Excluded?
                                    </label>
                                  </div>
                                  <div className="flex space-x-1">
                                    <Button
                                      type="button"
                                      size="sm"
                                      onClick={() =>
                                        saveEditedTechItem(
                                          assignment.founderId,
                                          item.id
                                        )
                                      }
                                      disabled={item.isSaving}
                                    >
                                      {item.isSaving ? (
                                        <>
                                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                          Saving...
                                        </>
                                      ) : (
                                        "Save"
                                      )}
                                    </Button>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => setEditingTechItem(null)}
                                    >
                                      Cancel
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-start justify-between">
                                <div className="flex items-start space-x-2 flex-1">
                                  <p className="text-sm flex-1 mt-2">
                                    {item.description}
                                  </p>
                                  <div className="flex items-center space-x-2">
                                    <div className="flex items-center space-x-2 space-y-3">
                                      <Checkbox
                                        id={`excluded-${item.id}`}
                                        checked={item.isExcluded}
                                        onCheckedChange={() =>
                                          toggleExclusion(
                                            assignment.founderId,
                                            item.id,
                                            item.isExcluded || false
                                          )
                                        }
                                        className="mt-2"
                                      />
                                      <label
                                        htmlFor={`excluded-${item.id}`}
                                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                      >
                                        Excluded?
                                      </label>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex space-x-1 ml-2">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      startEditingTechItem(
                                        assignment.founderId,
                                        item.id,
                                        item.description,
                                        item.isExcluded
                                      )
                                    }
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      deleteTechItem(
                                        assignment.founderId,
                                        item.id
                                      )
                                    }
                                    disabled={item.isDeleting}
                                  >
                                    {item.isDeleting ? (
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                      <Trash className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              </div>
                            )}
                          </li>
                        ))}
                      </ul>
                    )}

                    <div className="mt-4 space-y-3">
                      <div className="flex items-start space-x-2">
                        <div className="flex items-center space-x-4 w-full">
                          <Input
                            value={
                              newTechItems[assignment.founderId]?.description ||
                              ""
                            }
                            onChange={(e) =>
                              handleNewTechItemChange(
                                assignment.founderId,
                                e.target.value
                              )
                            }
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault();
                                if (
                                  newTechItems[
                                    assignment.founderId
                                  ]?.description?.trim()
                                ) {
                                  addTechItem(assignment.founderId);
                                }
                              }
                            }}
                            placeholder="Describe a new IP/tech item..."
                            className="w-full"
                          />
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`new-excluded-${assignment.founderId}`}
                              checked={
                                newTechItems[assignment.founderId]
                                  ?.isExcluded || false
                              }
                              disabled={
                                !newTechItems[
                                  assignment.founderId
                                ]?.description?.trim()
                              }
                              onCheckedChange={(checked) =>
                                handleNewTechItemExcludedChange(
                                  assignment.founderId,
                                  checked as boolean
                                )
                              }
                            />
                            <label
                              htmlFor={`new-excluded-${assignment.founderId}`}
                              className={clsx(
                                "text-sm font-medium leading-none",
                                !newTechItems[
                                  assignment.founderId
                                ]?.description?.trim()
                                  ? "text-muted-foreground opacity-70"
                                  : ""
                              )}
                            >
                              Excluded?
                            </label>
                          </div>
                        </div>
                        <Button
                          type="button"
                          onClick={() => addTechItem(assignment.founderId)}
                          disabled={
                            !newTechItems[
                              assignment.founderId
                            ]?.description?.trim()
                          }
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add Tech Item
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>

        {/* If no directors have been added yet */}
        {formData.directors.length === 0 && (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
            <p className="text-amber-800">
              Please add directors/founders in the "Directors & Officers" step
              before assigning intellectual property.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TechStep;
