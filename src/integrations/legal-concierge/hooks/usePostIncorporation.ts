import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { QueryKey } from "../keys";
import {
  PostIncorporationTaskResponse,
  ForeignQualificationState,
} from "../types/PostIncorporation";

const api = new APIClient();

// Type guard to check if response has an error
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// Hook for completing EIN task
export const useCompleteEIN = () => {
  const queryClient = useQueryClient();

  return useMutation<PostIncorporationTaskResponse, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.completeEIN(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().postIncorporation, companyId],
      });
    },
  });
};

// Hook for completing business account task
export const useCompleteBusinessAccount = () => {
  const queryClient = useQueryClient();

  return useMutation<PostIncorporationTaskResponse, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.completeBusinessAccount(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().postIncorporation, companyId],
      });
    },
  });
};

// Hook for completing foreign qualification task
export const useCompleteForeignQualification = () => {
  const queryClient = useQueryClient();

  return useMutation<PostIncorporationTaskResponse, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.completeForeignQualification(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().postIncorporation, companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().foreignQualificationStates, companyId],
      });
    },
  });
};

// Hook for adding foreign qualification state
export const useAddForeignQualificationState = () => {
  const queryClient = useQueryClient();

  return useMutation<
    PostIncorporationTaskResponse,
    Error,
    { companyId: string; state: string }
  >({
    mutationFn: async ({ companyId, state }) => {
      const response = await api.addForeignQualificationState(companyId, state);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().foreignQualificationStates, variables.companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().postIncorporation, variables.companyId],
      });
    },
  });
};

// Hook for removing foreign qualification state
export const useRemoveForeignQualificationState = () => {
  const queryClient = useQueryClient();

  return useMutation<
    PostIncorporationTaskResponse,
    Error,
    { id: string; companyId: string }
  >({
    mutationFn: async ({ id }) => {
      const response = await api.removeForeignQualificationState(id);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().foreignQualificationStates, variables.companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().postIncorporation, variables.companyId],
      });
    },
  });
};

// Hook for confirming post-incorporation completion
export const useConfirmPostIncorporation = () => {
  const queryClient = useQueryClient();

  return useMutation<PostIncorporationTaskResponse, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.confirmPostIncorporation(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().postIncorporation, companyId],
      });
    },
  });
};
