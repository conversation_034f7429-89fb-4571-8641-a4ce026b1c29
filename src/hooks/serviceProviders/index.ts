import {
  useServiceProviderState,
  useServiceProviderStateSetters,
} from "./useServiceProviderState";
import { useServiceProviderActions } from "./useServiceProviderActions";
import { UseServiceProvidersReturn } from "./types";

export function useServiceProviders(): UseServiceProvidersReturn {
  const { serviceProviders, promisedGrants, companyEquity, loading } =
    useServiceProviderState();

  const {
    setServiceProviders,
    setPromisedGrants,
    setCompanyEquity,
    setLoading,
  } = useServiceProviderStateSetters();

  const actions = useServiceProviderActions(
    serviceProviders,
    setServiceProviders,
    promisedGrants,
    setPromisedGrants,
    companyEquity,
    setCompanyEquity,
    setLoading
  );

  return {
    // State
    serviceProviders,
    promisedGrants,
    companyEquity,
    loading,

    // Actions
    ...actions,
  };
}

export * from "./types";
export * from "./utils";
