import React from "react";
import { Progress } from "@/components/ui/progress";
import { CheckCircle } from "lucide-react";

interface ProgressBarProps {
  completedTasks: number;
  totalTasks: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  completedTasks,
  totalTasks,
}) => {
  const percentComplete =
    totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">
          Task Completion
        </span>
        <div className="flex items-center">
          <span className="text-sm font-semibold text-primary mr-2">
            {percentComplete}%
          </span>
          <span className="text-sm text-gray-600">
            ({completedTasks} of {totalTasks} complete)
          </span>
        </div>
      </div>
      <div className="relative">
        <Progress value={percentComplete} className="h-2" />
        {percentComplete === 100 && (
          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 -translate-x-6">
            <CheckCircle size={20} className="text-green-500" />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressBar;
