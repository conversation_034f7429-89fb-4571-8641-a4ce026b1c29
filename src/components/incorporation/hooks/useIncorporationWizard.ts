import { useState, useEffect } from "react";
import { WizardStep, FormData, initialFormData } from "../types";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { companyService } from "@/services/companyService";
import { supabase } from "@/integrations/supabase/client";

export const useIncorporationWizard = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<WizardStep>("company-name");
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [loading, setLoading] = useState(false);
  const [nameAvailable, setNameAvailable] = useState<boolean | null>(null);
  const [checkingName, setCheckingName] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);

  // Get current user on component mount
  useEffect(() => {
    const checkAuth = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUserId(user?.id || null);
    };

    checkAuth();
  }, []);

  const updateFormData = (field: string, value: string) => {
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      setFormData({
        ...formData,
        [parent]: {
          ...(formData as any)[parent],
          [child]: value,
        },
      });
    } else {
      setFormData({
        ...formData,
        [field]: value,
      });

      // Reset name availability check when name changes
      if (field === "companyName") {
        setNameAvailable(null);
      }
    }
  };

  const handleNextStep = () => {
    if (step === "company-name") {
      if (!formData.companyName) {
        toast.error("Please enter a company name");
        return;
      }
      if (nameAvailable !== true) {
        toast.error("Please check if the company name is available");
        return;
      }
      setStep("company-address");
    } else if (step === "company-address") {
      const { street, city, state, zipCode } = formData.companyAddress;
      if (!street || !city || !state || !zipCode) {
        toast.error("Please fill in all address fields");
        return;
      }
      setStep("review");
    }
  };

  const handlePreviousStep = () => {
    if (step === "company-address") {
      setStep("company-name");
    } else if (step === "review") {
      setStep("company-address");
    }
  };

  const handleSubmit = async () => {
    setLoading(true);

    try {
      // Create a company record in Supabase
      if (!userId) {
        toast.error("You must be logged in to incorporate");
        setLoading(false);
        return;
      }

      // Convert form data to company details format
      const companyDetails = {
        company_name: formData.companyName,
        incorporation_date: new Date().toISOString().split("T")[0],
        company_address: {
          street1: formData.companyAddress.street,
          street2: "",
          city: formData.companyAddress.city,
          state: formData.companyAddress.state,
          zipCode: formData.companyAddress.zipCode,
        },
        authorized_shares: 10000000,
        issued_shares: 0,
        par_value_per_share: 0.00001,
      };

      // Save to Supabase
      const saved = await companyService.saveCompanyDetails(companyDetails);

      if (!saved) {
        throw new Error("Failed to save company details");
      }

      // Also store in localStorage for backwards compatibility
      const companyData = {
        companyName: formData.companyName,
        nameAvailabilityChecked: true,
        incorporatingToday: true,
        incorporationDate: new Date(),
        companyAddressStructured: {
          street1: formData.companyAddress.street,
          street2: "",
          city: formData.companyAddress.city,
          state: formData.companyAddress.state,
          zipCode: formData.companyAddress.zipCode,
        },
      };

      localStorage.setItem(
        "completedCompanyDetails",
        JSON.stringify(companyData)
      );
      localStorage.setItem("companyQuestionnaire", JSON.stringify(companyData));

      toast.success("Incorporation request submitted successfully!");

      // Navigate to questions page
      navigate("/questions");
    } catch (error) {
      console.error("Error submitting incorporation:", error);
      toast.error("Failed to submit incorporation. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const checkCompanyName = () => {
    if (!formData.companyName) {
      toast.error("Please enter a company name");
      return;
    }

    setCheckingName(true);

    // Simulate API call to check name availability
    setTimeout(() => {
      setCheckingName(false);
      // For demo, we'll randomly make some names available
      const isAvailable = Math.random() > 0.3;
      setNameAvailable(isAvailable);

      if (isAvailable) {
        toast.success(`"${formData.companyName}, Inc." is available!`);
      } else {
        toast.error(
          `"${formData.companyName}, Inc." is already taken. Please try another name.`
        );
      }
    }, 1500);
  };

  return {
    step,
    formData,
    loading,
    nameAvailable,
    checkingName,
    updateFormData,
    handleNextStep,
    handlePreviousStep,
    handleSubmit,
    checkCompanyName,
  };
};
