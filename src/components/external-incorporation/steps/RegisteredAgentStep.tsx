import React from "react";
import { ExternalIncorporationFormData } from "../types";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

interface RegisteredAgentStepProps {
  formData: ExternalIncorporationFormData;
  updateFormData: (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => void;
}

const RegisteredAgentStep: React.FC<RegisteredAgentStepProps> = ({
  formData,
  updateFormData,
}) => {
  const handleRegisteredAgentChange = (
    field: keyof typeof formData.registeredAgent,
    value: string
  ) => {
    updateFormData("registeredAgent", {
      ...formData.registeredAgent,
      [field]: value,
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Please provide details about your registered agent.
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="agent-name">Registered Agent Name</Label>
          <Input
            id="agent-name"
            value={formData.registeredAgent.name}
            onChange={(e) =>
              handleRegisteredAgentChange("name", e.target.value)
            }
            placeholder="e.g., Cogency Global Inc."
            className="mt-1"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="agent-address">Registered Agent Address</Label>
          <Input
            id="agent-address"
            value={formData.registeredAgent.address}
            onChange={(e) =>
              handleRegisteredAgentChange("address", e.target.value)
            }
            placeholder="Complete address"
            className="mt-1"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="agent-email">Registered Agent Email (Optional)</Label>
          <Input
            id="agent-email"
            type="email"
            value={formData.registeredAgent.email}
            onChange={(e) =>
              handleRegisteredAgentChange("email", e.target.value)
            }
            placeholder="Email address"
            className="mt-1"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="agent-phone">Registered Agent Phone (Optional)</Label>
          <Input
            id="agent-phone"
            value={formData.registeredAgent.phone}
            onChange={(e) =>
              handleRegisteredAgentChange("phone", e.target.value)
            }
            placeholder="Phone number"
            className="mt-1"
          />
        </div>
      </div>
    </div>
  );
};

export default RegisteredAgentStep;
