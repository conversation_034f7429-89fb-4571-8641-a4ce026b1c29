// components/common/ToggleGroup.tsx
import * as React from "react";
import { QuestionnaireFormData } from "../questions/types";

interface ToggleGroupProps {
  value: string;
  onChange: (value: string) => void;
  options: { label: string; value: string }[];
  formData: QuestionnaireFormData;
}

const GettingStartedSectionToggleGroup: React.FC<ToggleGroupProps> = ({ value, onChange, options, formData }) => {
  return (
    <div className="inline-flex rounded-lg border bg-white shadow-sm overflow-hidden">
      {options.map((opt) => (
        <button
          key={opt.value}
          disabled={formData?.isFormConfirmed && value !== opt.value}
          onClick={() => value!== opt.value && onChange(opt.value)}
          className={`px-3 py-1 text-sm font-medium transition-colors ${
            value === opt.value
              ? "bg-blue-600 text-white"
              : "bg-white text-gray-700 hover:bg-gray-100"
          } ${formData?.isFormConfirmed && value !== opt.value ? "opacity-50 cursor-not-allowed" : ""}`}
        >
          {opt.label}
        </button>
      ))}
    </div>
  );
};

export default GettingStartedSectionToggleGroup;
