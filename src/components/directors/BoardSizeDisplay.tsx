import React from "react";
import { Card, CardContent } from "@/components/ui/card";

interface BoardSizeDisplayProps {
  boardSize: number;
  currentDirectors: number;
  vacancies: number;
}

const BoardSizeDisplay: React.FC<BoardSizeDisplayProps> = ({
  boardSize,
  currentDirectors,
  vacancies,
}) => {
  return (
    <div className="space-y-2">
      <h3 className="text-lg font-medium">Board of Directors Summary</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-sm text-gray-500">Authorized Size</p>
              <p className="text-3xl font-bold">{boardSize}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-sm text-gray-500">Current Directors</p>
              <p className="text-3xl font-bold">{currentDirectors}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-sm text-gray-500">Vacant Seats</p>
              <p className="text-3xl font-bold">{vacancies}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BoardSizeDisplay;
