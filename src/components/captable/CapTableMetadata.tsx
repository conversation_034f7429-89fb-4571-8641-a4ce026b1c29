import React from "react";
import { Calendar } from "lucide-react";
import { format } from "date-fns";
import AnimatedTransition from "@/components/common/AnimatedTransition";

interface CapTableMetadataProps {
  lastUpdated: Date;
  companyName?: string;
}

const CapTableMetadata: React.FC<CapTableMetadataProps> = ({
  lastUpdated,
  companyName,
}) => {
  return (
    <AnimatedTransition delay={0.1}>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Calendar size={16} />
          <span>Last updated: {format(lastUpdated, "MMMM d, yyyy")}</span>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <span className="font-medium">Company:</span>
          <span>{companyName || "Unnamed Company"}</span>
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default CapTableMetadata;
