import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/common/Card";
import Button from "@/components/common/Button";
import AnimatedTransition from "@/components/common/AnimatedTransition";

const ResourcesCard: React.FC = () => {
  return (
    <AnimatedTransition delay={0.6}>
      <Card>
        <CardHeader>
          <CardTitle>Resources</CardTitle>
          <CardDescription>Helpful resources for entrepreneurs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border border-gray-100 rounded-lg p-4 transition-all hover:shadow-md hover:border-legal-200">
              <h3 className="font-medium text-gray-900">Incorporation Guide</h3>
              <p className="text-gray-600 text-sm mt-1">
                Learn about the process of incorporating your business.
              </p>
              <Button variant="link" className="pl-0 mt-2" size="sm">
                Read More
              </Button>
            </div>

            <div className="border border-gray-100 rounded-lg p-4 transition-all hover:shadow-md hover:border-legal-200">
              <h3 className="font-medium text-gray-900">Founder's Toolkit</h3>
              <p className="text-gray-600 text-sm mt-1">
                Essential resources and templates for startup founders.
              </p>
              <Button variant="link" className="pl-0 mt-2" size="sm">
                Access Toolkit
              </Button>
            </div>

            <div className="border border-gray-100 rounded-lg p-4 transition-all hover:shadow-md hover:border-legal-200">
              <h3 className="font-medium text-gray-900">Legal FAQ</h3>
              <p className="text-gray-600 text-sm mt-1">
                Answers to common legal questions for new businesses.
              </p>
              <Button variant="link" className="pl-0 mt-2" size="sm">
                View FAQ
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </AnimatedTransition>
  );
};

export default ResourcesCard;
