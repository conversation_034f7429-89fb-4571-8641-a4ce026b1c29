import {
  DocumentCommentRequest,
  DocumentCommentResponse,
} from "@/integrations/legal-concierge/types/DocumentComment";
import { DiscussionMessageRequest } from "@/integrations/legal-concierge/types/Discussion";

const BASE_URL =
  import.meta.env.VITE_API_URL || "https://apidev.foundersform.com";

interface TokenResponse {
  message: string;
  data: string;
  error: string | null;
  validationErrors: Record<string, unknown> | null;
}

interface OnlyOfficeConfig {
  document: {
    fileType: string;
    key: string;
    title: string;
    url: string;
    permissions: {
      edit: boolean;
      download: boolean;
      forcesave: boolean;
    };
  };
  documentType: string;
  editorConfig: {
    mode: string;
    callbackUrl: string;
    user: {
      id: string;
      name: string;
    };
    customization?: {
      autosave?: boolean;
      compactHeader?: boolean;
      compactToolbar?: boolean;
      hideRulers?: boolean;
      hideStatusBar?: boolean;
      hideRightMenu?: boolean;
      hideLeftMenu?: boolean;
      toolbarHideFileName?: boolean;
      about?: boolean;
      feedback?: boolean;
      help?: boolean;
      plugins?: boolean;
      toolbar?: boolean;
      header?: boolean;
      disableContextMenu?: boolean;
      forcesave?: boolean;
    };
  };
  height: string;
  width: string;
}

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const error = await response.text();
    return { error, data: null };
  }

  // Handle 204 No Content response
  if (response.status === 204) {
    return { error: null, data: null };
  }

  try {
    const data = await response.json();
    // If the response is a single object with a data property, return that
    // Otherwise, return the response as is
    return { error: null, data: data.data || data };
  } catch (error) {
    // If we can't parse JSON and it's not a 204, then it's an error
    return { error: "Failed to parse response", data: null };
  }
};

// Helper function to get headers with auth token
const getHeaders = () => {
  const token = localStorage.getItem("token");
  return {
    "Content-Type": "application/json",
    Authorization: token ? `Bearer ${token}` : "",
  };
};

export const api = {
  // Get document token
  getDocumentToken: async (config: OnlyOfficeConfig, companyId: string) => {
    try {
      console.log("Getting document token", companyId);
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/documents/token`,
        {
          method: "POST",
          headers: getHeaders(),
          credentials: "include",
          body: JSON.stringify(config),
        }
      );
      const result = (await response.json()) as TokenResponse;
      console.log("Document token result", result);
      return { error: result.error, data: { token: result.data } };
    } catch (error) {
      return { error: "Failed to get document token", data: null };
    }
  },

  // Get comments for a document
  getDocumentComments: async (
    companyId: string,
    documentIdentifier: string
  ) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/documents/${encodeURIComponent(documentIdentifier)}/comments`,
        {
          method: "GET",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to fetch comments", data: null };
    }
  },

  // Get all comments for a company
  getCompanyComments: async (companyId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/documents/comments`,
        {
          method: "GET",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to fetch company comments", data: null };
    }
  },

  // Create a new comment
  createDocumentComment: async (
    companyId: string,
    documentIdentifier: string,
    comment: DocumentCommentRequest
  ) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/documents/${encodeURIComponent(documentIdentifier)}/comments`,
        {
          method: "POST",
          headers: getHeaders(),
          credentials: "include",
          body: JSON.stringify(comment),
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to create comment", data: null };
    }
  },

  // Update a comment
  updateDocumentComment: async (
    commentId: string,
    comment: DocumentCommentRequest
  ) => {
    try {
      const response = await fetch(`${BASE_URL}/api/comments/${commentId}`, {
        method: "PUT",
        headers: getHeaders(),
        credentials: "include",
        body: JSON.stringify(comment),
      });
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to update comment", data: null };
    }
  },

  // Delete a comment
  deleteDocumentComment: async (commentId: string) => {
    try {
      const response = await fetch(`${BASE_URL}/api/comments/${commentId}`, {
        method: "DELETE",
        headers: getHeaders(),
        credentials: "include",
      });
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to delete comment", data: null };
    }
  },

  // Get all discussion messages for a company
  getDiscussionMessages: async (companyId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/discussions/companies/${companyId}/messages`,
        {
          method: "GET",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to fetch discussion messages", data: null };
    }
  },

  // Create a new discussion message
  createDiscussionMessage: async (
    companyId: string,
    message: DiscussionMessageRequest
  ) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/discussions/companies/${companyId}/messages`,
        {
          method: "POST",
          headers: getHeaders(),
          credentials: "include",
          body: JSON.stringify(message),
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to create discussion message", data: null };
    }
  },

  // Get discussion message replies
  getDiscussionReplies: async (companyId: string, replyToId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/discussions/companies/${companyId}/messages/${replyToId}/replies`,
        {
          method: "GET",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to fetch discussion replies", data: null };
    }
  },

  // Update a discussion message
  updateDiscussionMessage: async (
    messageId: string,
    message: DiscussionMessageRequest
  ) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/discussions/messages/${messageId}`,
        {
          method: "PUT",
          headers: getHeaders(),
          credentials: "include",
          body: JSON.stringify(message),
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to update discussion message", data: null };
    }
  },

  // Delete a discussion message
  deleteDiscussionMessage: async (messageId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/discussions/messages/${messageId}`,
        {
          method: "DELETE",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to delete discussion message", data: null };
    }
  },

  // PostIncorporation API methods

  // Complete EIN task
  completeEIN: async (companyId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/completeein`,
        {
          method: "PUT",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to complete EIN task", data: null };
    }
  },

  // Complete business account task
  completeBusinessAccount: async (companyId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/completebusinessaccount`,
        {
          method: "PUT",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to complete business account task", data: null };
    }
  },

  // Complete foreign qualification task
  completeForeignQualification: async (companyId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/completeforeignqualification`,
        {
          method: "PUT",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return {
        error: "Failed to complete foreign qualification task",
        data: null,
      };
    }
  },

  // Add foreign qualification state
  addForeignQualificationState: async (companyId: string, state: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/foreignqualification/states`,
        {
          method: "POST",
          headers: getHeaders(),
          credentials: "include",
          body: JSON.stringify(state),
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to add foreign qualification state", data: null };
    }
  },

  // Remove foreign qualification state
  removeForeignQualificationState: async (id: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/foreignqualification/states/${id}`,
        {
          method: "DELETE",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return {
        error: "Failed to remove foreign qualification state",
        data: null,
      };
    }
  },

  // Confirm post-incorporation completion
  confirmPostIncorporation: async (companyId: string) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/companies/${companyId}/postincorporation/confirm`,
        {
          method: "PUT",
          headers: getHeaders(),
          credentials: "include",
        }
      );
      return handleResponse(response);
    } catch (error) {
      return { error: "Failed to confirm post-incorporation", data: null };
    }
  },
};
