import React from "react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import MaintenanceItem from "./MaintenanceItem";
import ContactAttorneySection from "./ContactAttorneySection";
import { FileUp, Briefcase, Users, Calendar } from "lucide-react";

const DashboardSeriesSeedFinancing: React.FC = () => {
  const seriesSeedItems = [
    {
      icon: FileUp,
      title: "Create Term Sheet",
      description: "Draft and negotiate the key terms of your financing round.",
      buttonText: "Start",
      linkTo: "/term-sheet",
    },
    {
      icon: Briefcase,
      title: "Draft Transaction Documents",
      description:
        "Prepare the legal documents required for your financing round.",
      buttonText: "Start",
      linkTo: "/transaction-documents",
    },
    {
      icon: Users,
      title: "Obtain Requisite Approvals",
      description:
        "Secure necessary approvals from your board and stockholders.",
      buttonText: "Start",
      linkTo: "/approvals",
    },
    {
      icon: Calendar,
      title: "Generate Disclosure Schedules",
      description:
        "Create comprehensive disclosure schedules for your financing documents.",
      buttonText: "Start",
      linkTo: "/disclosure-schedules",
    },
  ];

  return (
    <AnimatedTransition delay={0.5} className="mb-8">
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold mb-4">Series Seed Financing</h2>
        <p className="text-gray-600">
          Tools and resources to help with your Series Seed fundraising round.
        </p>
        <div className="mt-6 space-y-4">
          {seriesSeedItems.map((item, index) => (
            <MaintenanceItem
              key={index}
              icon={item.icon}
              title={item.title}
              description={item.description}
              buttonText={item.buttonText}
              linkTo={item.linkTo}
            />
          ))}
        </div>
      </div>
      <ContactAttorneySection />
    </AnimatedTransition>
  );
};

export default DashboardSeriesSeedFinancing;
