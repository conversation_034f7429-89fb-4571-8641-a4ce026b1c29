import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import { toast } from "sonner";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { useAuth } from "@/contexts/AuthContext";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import LoginForm from "./LoginForm";
import SignupForm from "./SignupForm";
import SocialAuth from "./SocialAuth";
import { useNavigate, useSearchParams } from "react-router-dom";

const AuthForm: React.FC = () => {
  const [fullName, setFullName] = useState("");
  const [params] = useSearchParams();
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(
    window.location.pathname === "/login"
      ? "login"
      : window.location.pathname === "/register"
        ? "signup"
        : "login"
  );

  const { login, signUp } = useAuth();
  const navigate = useNavigate();
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (activeTab === "login") {
      if (!email || !password) {
        toast.error("Please fill in all fields");
        return;
      }
    } else {
      if (!fullName || !email || !password || !phone) {
        toast.error("Please fill in all fields");
        return;
      }
      if (!params.get("invite")) {
        toast.error("Bad registration");
        return;
      }
    }

    setLoading(true);

    try {
      if (activeTab === "login") {
        await login(email, password);
        navigate(`/mfa?email=${email}`);
      } else {
        await signUp(email, phone, password, fullName, params.get("invite"));
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    setLoading(true);
    try {
      // Use demo credentials
      await login("<EMAIL>", "password");
      // No need to set loading to false on success as we'll redirect
    } catch (error) {
      console.error("Demo login error:", error);
      toast.error("Unable to access demo account. Please try again.");
      setLoading(false);
    }
  };

  return (
    <div className="flex justify-center w-full">
      <Card variant="glass" className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-xl md:text-2xl">Welcome</CardTitle>
          <CardDescription>
            Access your legal concierge dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AnimatedTransition>
            <Tabs
              defaultValue="login"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsContent value="login">
                <LoginForm
                  email={email}
                  setEmail={setEmail}
                  password={password}
                  setPassword={setPassword}
                  loading={loading}
                  onSubmit={handleSubmit}
                />
              </TabsContent>

              <TabsContent value="signup">
                <SignupForm
                  fullName={fullName}
                  setFullName={setFullName}
                  email={email}
                  setEmail={setEmail}
                  phone={phone}
                  setPhone={setPhone}
                  password={password}
                  setPassword={setPassword}
                  loading={loading}
                  onSubmit={handleSubmit}
                />
              </TabsContent>
            </Tabs>

            {/* <SocialAuth loading={loading} onDemoLogin={handleDemoLogin} /> */}
          </AnimatedTransition>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthForm;
