import React from "react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { CheckCircle } from "lucide-react";
import { FormData } from "../types";

interface ReviewStepProps {
  formData: FormData;
}

const ReviewStep: React.FC<ReviewStepProps> = ({ formData }) => {
  return (
    <AnimatedTransition>
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium text-gray-500">Company Name</h3>
          <p className="text-gray-900 font-medium mt-1">
            {formData.companyName}, Inc.
          </p>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-500">Address</h3>
          <div className="text-gray-900 mt-1">
            <p>{formData.companyAddress.street}</p>
            <p>
              {formData.companyAddress.city}, {formData.companyAddress.state}{" "}
              {formData.companyAddress.zipCode}
            </p>
          </div>
        </div>

        <div className="rounded-lg bg-legal-50 p-4 border border-legal-100">
          <h3 className="text-sm font-semibold text-legal-800">Next Steps</h3>
          <p className="text-sm text-legal-700 mt-1">
            After submitting this information, you'll be guided through the
            following steps:
          </p>
          <ul className="mt-2 text-sm text-legal-700 space-y-2">
            <li className="flex items-start">
              <CheckCircle size={16} className="text-legal-500 mr-2 mt-0.5" />
              <span>Providing details about founders and share allocation</span>
            </li>
            <li className="flex items-start">
              <CheckCircle size={16} className="text-legal-500 mr-2 mt-0.5" />
              <span>
                Setting up your governance structure and initial roles
              </span>
            </li>
            <li className="flex items-start">
              <CheckCircle size={16} className="text-legal-500 mr-2 mt-0.5" />
              <span>Reviewing and signing incorporation documents</span>
            </li>
          </ul>
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default ReviewStep;
