import React from "react";
import { MessageSquare } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { CardTitle } from "@/components/ui/card";
import { Comment } from "./types";

interface CommentsHeaderProps {
  comments: Comment[];
  showResolved: boolean;
  toggleShowResolved: () => void;
}

const CommentsHeader: React.FC<CommentsHeaderProps> = ({
  comments,
  showResolved,
  toggleShowResolved,
}) => {
  const activeCommentCount = comments.filter((c) => !c.resolved).length;

  return (
    <div className="flex justify-between items-center">
      <CardTitle className="text-lg flex items-center">
        <MessageSquare className="h-5 w-5 mr-2" />
        Document Comments
      </CardTitle>
      <div className="flex items-center space-x-2">
        <Badge
          variant="outline"
          className="cursor-pointer hover:bg-gray-100"
          onClick={toggleShowResolved}
        >
          {showResolved ? "Hide Resolved" : "Show Resolved"}
        </Badge>
        <Badge variant="secondary">{activeCommentCount} Active</Badge>
      </div>
    </div>
  );
};

export default CommentsHeader;
