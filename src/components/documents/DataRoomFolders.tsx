import React, { useState } from "react";
import { FolderStructure, DocumentFile } from "@/types/capTable";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Folder,
  FolderOpen,
  FileText,
  Upload,
  Download,
  ArrowUpRight,
  Loader2,
  CheckCircle2,
} from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import {
  getPresignedUploadUrl,
  uploadFileToPresignedUrl,
} from "@/services/documentService";
import { convertToFolderKey } from "@/utils/folderUtils";

interface DataRoomFoldersProps {
  folderStructure: FolderStructure;
  onUploadComplete?: () => void;
  expandedFolders: string[];
  onToggleFolder: (folderId: string) => void;
  isRefreshing?: boolean;
  allowUploads?: boolean; // New prop to control upload functionality
}

interface FolderContent {
  documents: DocumentFile[];
  subfolders?: Record<string, FolderContent>;
  isCompleted?: boolean;
}

const DataRoomFolders: React.FC<DataRoomFoldersProps> = ({
  folderStructure,
  onUploadComplete,
  expandedFolders,
  onToggleFolder,
  isRefreshing = false,
  allowUploads = true, // Default to true for backward compatibility
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, boolean>>(
    {}
  );
  const { user } = useAuth();

  const handleFileUpload = async (folderPath: string, files: FileList) => {
    if (!user?.companyId) {
      toast.error("Company ID not found");
      return;
    }

    setUploadingFiles((prev) => ({ ...prev, [folderPath]: true }));

    try {
      for (const file of Array.from(files)) {
        const folderKey = convertToFolderKey(folderPath);
        const fileName = file.name.toLowerCase().replace(/[^a-z0-9.-]/g, "-");
        const key = `${folderKey}/${fileName}`;

        // Get presigned URL
        const { uploadUrl } = await getPresignedUploadUrl(
          user.companyId,
          folderKey,
          fileName,
          file.type
        );
        // Upload file to S3
        await uploadFileToPresignedUrl(uploadUrl, file);
        toast.success(`Successfully uploaded ${file.name}`);
      }

      // Ensure the folder is expanded after upload
      if (!expandedFolders.includes(folderPath)) {
        onToggleFolder(folderPath);
      }

      // Call the refresh callback after successful upload
      onUploadComplete?.();
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Failed to upload files");
    } finally {
      setUploadingFiles((prev) => ({ ...prev, [folderPath]: false }));
    }
  };

  const handleUploadClick = (e: React.MouseEvent, folderPath: string) => {
    e.stopPropagation(); // Prevent accordion toggle
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".pdf,.doc,.docx,.txt";
    input.multiple = true;

    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        handleFileUpload(folderPath, files);
      }
    };

    input.click();
  };

  const renderFolder = (
    folderName: string,
    folder: FolderContent,
    parentStructure: FolderStructure | string,
    path: string = ""
  ) => {
    const currentPath = path ? `${path}/${folderName}` : folderName;
    const isExpanded = expandedFolders.includes(currentPath);
    const hasSubfolders =
      folder.subfolders && Object.keys(folder.subfolders).length > 0;
    const isUploading = uploadingFiles[currentPath];
    const isRootFolder =
      typeof parentStructure !== "string" &&
      parentStructure[folderName] === folder;

    const hasDocuments = folder.documents && folder.documents.length > 0;
    const isCompleted = !isRootFolder && (folder.isCompleted || hasDocuments);
    const fileCount = folder.documents?.length || 0;

    return (
      <Card
        key={currentPath}
        className={`mb-4 transition-opacity duration-200 ${isRefreshing ? "opacity-50" : "opacity-100"}`}
      >
        <AccordionItem value={currentPath} className="border-none">
          <AccordionTrigger
            className="px-4 py-3 hover:no-underline"
            onClick={(e) => {
              e.preventDefault();
              onToggleFolder(currentPath);
            }}
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                {isExpanded ? (
                  <FolderOpen className="h-5 w-5 text-yellow-500" />
                ) : (
                  <Folder className="h-5 w-5 text-yellow-500" />
                )}
                <span className="font-medium">{folderName}</span>
                {isCompleted && (
                  <div className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4" />
                  </div>
                )}
                {fileCount > 0 && (
                  <span className="text-sm text-gray-500">
                    ({fileCount} file{fileCount !== 1 ? "s" : ""})
                  </span>
                )}
              </div>

              <div className="flex items-center gap-4 mr-5">
                {!isRootFolder && allowUploads && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => handleUploadClick(e, currentPath)}
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4 mr-1" />
                    )}
                    {isUploading ? "Uploading..." : "Upload"}
                  </Button>
                )}
              </div>
            </div>
          </AccordionTrigger>

          <AccordionContent className="px-4 pb-3">
            {hasDocuments && (
              <div className="mt-2 space-y-2">
                {folder.documents.map((doc: DocumentFile) => (
                  <div
                    key={doc.fileName}
                    className="flex items-center justify-between bg-gray-50 px-8 py-2 rounded-md hover:bg-gray-100"
                  >
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 text-gray-500 mr-2" />
                      <span>{doc.fileName}</span>
                    </div>
                    <div className="flex space-x-2">
                      {doc.url && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(doc.url, "_blank");
                            }}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(doc.url, "_blank");
                            }}
                          >
                            <ArrowUpRight className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
            {!isRootFolder && !hasDocuments && (
              <div className="text-sm text-gray-500 italic mt-2">
                {allowUploads
                  ? "No documents in this folder yet. Use the Upload button to add files."
                  : "No documents in this folder yet."}
              </div>
            )}

            {hasSubfolders && (
              <div className="mt-4 space-y-4">
                {Object.entries(folder.subfolders).map(
                  ([subfolderName, subfolder]) =>
                    renderFolder(subfolderName, subfolder, currentPath)
                )}
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Card>
    );
  };

  return (
    <div>
      <div className="grid grid-cols-1 gap-6">
        <Accordion
          type="multiple"
          className="space-y-4"
          value={expandedFolders}
        >
          {Object.entries(folderStructure).map(([folderName, folder]) =>
            renderFolder(folderName, folder, folderStructure)
          )}
        </Accordion>
      </div>
    </div>
  );
};

export default DataRoomFolders;
