import { useMutation, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { OfficerRequest } from "../types/Officer";
import { OfficerUpdateRequest } from "../types";
import { QueryKey } from "../keys";

const api = new APIClient();

// Officer management hooks
export const useAddOfficer = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      companyId,
      ...payload
    }: { companyId: string } & Omit<OfficerRequest, "companyId">) => {
      // Create a complete OfficerRequest with companyId
      const officerRequest: OfficerRequest = {
        ...payload,
        companyId,
      };
      return api.addOfficer(companyId, officerRequest);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.companyId],
      });
    },
  });
};

export const useUpdateOfficer = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      companyId,
      ...payload
    }: { id: string; companyId: string } & OfficerUpdateRequest) => {
      await api.updateOfficer(id, payload);
      return { companyId }; // Return companyId for invalidation
    },
    onSuccess: (result: { companyId: string }) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId],
      });
    },
  });
};

export const useDeleteOfficer = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      companyId,
    }: {
      id: string;
      companyId: string;
    }) => {
      await api.deleteOfficer(id);
      return { companyId }; // Return companyId for invalidation
    },
    onSuccess: (result: { companyId: string }) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId],
      });
    },
  });
};
