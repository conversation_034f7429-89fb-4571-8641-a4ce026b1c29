import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  DollarSign,
  ExternalLink,
  FileText,
  Info,
  Users,
} from "lucide-react";
import AnimatedTransition from "@/components/common/AnimatedTransition";

// Importing the components for each tab
import ConvertibleNoteTab from "@/components/financing/ConvertibleNoteTab";
import SafeTab from "@/components/financing/SafeTab";
import MfnTab from "@/components/financing/MfnTab";
import SafeRepurchaseTab from "@/components/financing/SafeRepurchaseTab";

const PreSeedFinancingCard: React.FC = () => {
  const [activeTab, setActiveTab] = useState("convertible-note");

  return (
    <AnimatedTransition delay={0.5} className="mb-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Pre-Seed Financing</CardTitle>
          <CardDescription>
            Tools and resources to help with your initial fundraising
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="convertible-note"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="convertible-note">
                Convertible Note
              </TabsTrigger>
              <TabsTrigger value="safes">SAFEs</TabsTrigger>
              <TabsTrigger value="mfn">MFN</TabsTrigger>
              <TabsTrigger value="safe-repurchase">
                SAFE Repurchase and Cancel
              </TabsTrigger>
            </TabsList>

            <TabsContent value="convertible-note">
              <ConvertibleNoteTab />
            </TabsContent>

            <TabsContent value="safes">
              <SafeTab />
            </TabsContent>

            <TabsContent value="mfn">
              <MfnTab />
            </TabsContent>

            <TabsContent value="safe-repurchase">
              <SafeRepurchaseTab />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </AnimatedTransition>
  );
};

export default PreSeedFinancingCard;
