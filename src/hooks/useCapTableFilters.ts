import { useState, useEffect } from "react";
import {
  Shareholder,
  ShareholderRole,
  ShareClass,
  CapTableSummary,
} from "@/types/capTable";

export const useCapTableFilters = (currentCapTable: CapTableSummary | null) => {
  const [view, setView] = useState<"standard" | "fullyDiluted">("standard");
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<ShareholderRole | "All">("All");
  const [shareClassFilter, setShareClassFilter] = useState<ShareClass | "All">(
    "All"
  );
  const [filteredShareholders, setFilteredShareholders] = useState<
    Shareholder[]
  >([]);

  // Apply filters whenever they change or when currentCapTable changes
  useEffect(() => {
    if (!currentCapTable) return;

    let filtered = currentCapTable.shareholders;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter((s) =>
        s.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply role filter
    if (roleFilter !== "All") {
      filtered = filtered.filter((s) => s.role === roleFilter);
    }

    // Apply share class filter
    if (shareClassFilter !== "All") {
      filtered = filtered.filter(
        (s) =>
          s.holdings[shareClassFilter] !== undefined &&
          s.holdings[shareClassFilter]! > 0
      );
    }

    setFilteredShareholders(filtered);
  }, [searchTerm, roleFilter, shareClassFilter, currentCapTable]);

  return {
    view,
    setView,
    searchTerm,
    setSearchTerm,
    roleFilter,
    setRoleFilter,
    shareClassFilter,
    setShareClassFilter,
    filteredShareholders,
  };
};
