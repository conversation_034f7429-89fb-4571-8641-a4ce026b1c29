import React from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info } from "lucide-react";
import AnimatedTransition from "@/components/common/AnimatedTransition";

const CapTableInfoAlert: React.FC = () => {
  return (
    <AnimatedTransition delay={0.15}>
      <Alert className="mb-6">
        <Info className="h-4 w-4" />
        <AlertTitle>About Cap Tables</AlertTitle>
        <AlertDescription>
          A cap table shows who owns what in your company. It includes all
          shareholders and their respective ownership percentages. Use this page
          to track equity distribution across founders, investors, and
          employees.
        </AlertDescription>
      </Alert>
    </AnimatedTransition>
  );
};

export default CapTableInfoAlert;
