import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@/test/utils";
import userEvent from "@testing-library/user-event";
import AuthorizedSharesStep from "../AuthorizedSharesStep";
import { mockLegacyFormData } from "@/test/mocks/companyData";

// Mock the auth context
vi.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({
    user: {
      companyId: "test-company-id",
    },
  }),
}));

// Mock the update authorized shares hook
vi.mock("@/integrations/legal-concierge/hooks/useCompanyInfo", () => ({
  useUpdateAuthorizedShares: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
  }),
}));

describe("AuthorizedSharesStep", () => {
  const mockUpdateFormData = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render all share-related fields", () => {
    render(
      <AuthorizedSharesStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByLabelText(/Number of Authorized Shares/i)
    ).toBeInTheDocument();
    expect(
      screen.getByLabelText(/Number of Shares to be Issued Initially/i)
    ).toBeInTheDocument();
    expect(screen.getByLabelText(/Par Value per Share/i)).toBeInTheDocument();
  });

  it("should display current share values", () => {
    render(
      <AuthorizedSharesStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(screen.getByDisplayValue("10,000,000")).toBeInTheDocument();
    expect(screen.getByDisplayValue("8,000,000")).toBeInTheDocument();
    expect(screen.getByDisplayValue("0.00001")).toBeInTheDocument();
  });

  it("should call updateFormData when authorized shares change", async () => {
    const user = userEvent.setup();

    render(
      <AuthorizedSharesStep
        formData={{
          ...mockLegacyFormData,
          authorizedShares: 0,
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const authorizedSharesInput = screen.getByLabelText(
      /Number of Authorized Shares/i
    );
    await user.type(authorizedSharesInput, "1");

    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalled();
    });
  });

  it("should call updateFormData when issued shares change", async () => {
    const user = userEvent.setup();

    render(
      <AuthorizedSharesStep
        formData={{
          ...mockLegacyFormData,
          issuedShares: 0,
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const issuedSharesInput = screen.getByLabelText(
      /Number of Shares to be Issued Initially/i
    );
    await user.type(issuedSharesInput, "1");

    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalled();
    });
  });

  it("should call updateFormData when par value changes", async () => {
    const user = userEvent.setup();

    render(
      <AuthorizedSharesStep
        formData={{
          ...mockLegacyFormData,
          parValuePerShare: 0,
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const parValueInput = screen.getByLabelText(/Par Value per Share/i);
    await user.type(parValueInput, "0.001");

    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalled();
    });
  });

  it("should show error when issued shares exceed authorized shares", () => {
    render(
      <AuthorizedSharesStep
        formData={{
          ...mockLegacyFormData,
          authorizedShares: 1000000,
          issuedShares: 2000000,
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByText(
        /The number of shares to be issued initially cannot exceed the number of authorized shares/i
      )
    ).toBeInTheDocument();
  });

  it("should format large numbers with commas", () => {
    render(
      <AuthorizedSharesStep
        formData={{
          ...mockLegacyFormData,
          authorizedShares: 1000000,
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const authorizedSharesInput = screen.getByLabelText(
      /Number of Authorized Shares/i
    );

    // The input should display the number with commas
    expect(authorizedSharesInput).toHaveValue("1,000,000");
  });

  it("should call updateFormData when decimal par value changes", async () => {
    const user = userEvent.setup();

    render(
      <AuthorizedSharesStep
        formData={{
          ...mockLegacyFormData,
          parValuePerShare: 0,
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const parValueInput = screen.getByLabelText(/Par Value per Share/i);
    await user.type(parValueInput, "0.00005");

    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalled();
    });
  });

  it("should show helpful descriptions for each field", () => {
    render(
      <AuthorizedSharesStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByText(
        /The total number of shares the company is authorized to issue/i
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        /The number of shares that will be immediately issued to founders and initial investors/i
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(/The nominal value of each share/i)
    ).toBeInTheDocument();
  });
});
