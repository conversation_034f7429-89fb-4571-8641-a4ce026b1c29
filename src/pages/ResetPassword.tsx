import React, { useState } from "react";
import Layout from "@/components/Layout";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { Navigate, useLocation, useSearchParams } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import ResetPasswordForm from "@/components/auth/ResetPasswordForm";
import { toast } from "sonner";

const ResetPassword: React.FC = () => {
  const { isAuthenticated, loading, resetPassword } = useAuth();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const email = searchParams.get("email") || "";
  const token = searchParams.get("token") || "";
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (loading) {
    return null; // Don't render anything while checking auth status
  }

  // If authenticated, redirect to dashboard
  if (isAuthenticated) {
    const from = (location.state as any)?.from?.pathname || "/dashboard";
    return <Navigate to={from} replace />;
  }

  // If no email or token in URL, show error
  if (!email || !token) {
    return (
      <Layout>
        <div className="min-h-[calc(100vh-16rem)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <AnimatedTransition className="w-full max-w-md">
            <Card variant="glass" className="w-full max-w-md">
              <CardHeader>
                <CardTitle className="text-xl md:text-2xl">
                  Invalid Reset Link
                </CardTitle>
                <CardDescription>
                  The password reset link is invalid or has expired.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-4">
                  <p className="mb-4">
                    Please request a new password reset link from the forgot
                    password page.
                  </p>
                  <a
                    href="/forgot-password"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Go to Forgot Password
                  </a>
                </div>
              </CardContent>
            </Card>
          </AnimatedTransition>
        </div>
      </Layout>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newPassword || !confirmPassword) {
      toast.error("Please fill in all fields");
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    setIsSubmitting(true);

    try {
      await resetPassword(email, token, newPassword);
      setIsSubmitting(false);
      // Navigation to login is handled in the resetPassword function
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-[calc(100vh-16rem)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <AnimatedTransition className="w-full max-w-md">
          <Card variant="glass" className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-xl md:text-2xl">
                Reset Password
              </CardTitle>
              <CardDescription>
                Create a new password for your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResetPasswordForm
                email={email}
                token={token}
                newPassword={newPassword}
                setNewPassword={setNewPassword}
                confirmPassword={confirmPassword}
                setConfirmPassword={setConfirmPassword}
                loading={isSubmitting}
                onSubmit={handleSubmit}
              />
            </CardContent>
          </Card>
        </AnimatedTransition>
      </div>
    </Layout>
  );
};

export default ResetPassword;
