import React from "react";
import { QuestionnaireFormData } from "../../../types";
import { Card, CardContent } from "@/components/common/Card";
import { Check } from "lucide-react";
import DirectorCard from "./stakeholders/DirectorCard";
import StockOptionPlanCard from "./stakeholders/StockOptionPlanCard";

interface StakeholderSectionProps {
  formData: QuestionnaireFormData;
}

const StakeholderSection: React.FC<StakeholderSectionProps> = ({
  formData,
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-3 flex items-center">
          <Check size={18} className="text-green-500 mr-2" />
          Directors, Officers & Shareholders
        </h3>

        {formData.directors.length === 0 ? (
          <p className="text-gray-500">
            No directors/officers/shareholders added
          </p>
        ) : (
          <div className="space-y-4">
            {formData.directors.map((director, index) => (
              <DirectorCard
                key={director.id}
                director={director}
                index={index}
              />
            ))}

            {formData.includeStockOptionPlan && (
              <StockOptionPlanCard
                percentage={formData.stockOptionPlanPercentage}
                issuedShares={formData.issuedShares}
              />
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StakeholderSection;
