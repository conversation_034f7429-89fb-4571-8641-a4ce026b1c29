import React, { useState } from "react";
import { Calendar, Mail, Send } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

const ContactAttorneySection: React.FC = () => {
  const [emailOpen, setEmailOpen] = useState(false);
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitEmail = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate sending email
    setTimeout(() => {
      toast.success("Message sent successfully");
      setIsSubmitting(false);
      setEmailOpen(false);
      setSubject("");
      setMessage("");
    }, 1000);
  };

  return (
    <Card className="mt-6 border-dashed border-gray-300">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg text-gray-700">
          Need Legal Assistance?
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 mb-4 text-sm">
          Connect with a Founders Form attorney for specialized guidance on your
          legal matters.
        </p>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() =>
              window.open("https://calendly.com/foundersform", "_blank")
            }
          >
            <Calendar className="h-4 w-4" />
            Schedule a Call
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setEmailOpen(true)}
          >
            <Mail className="h-4 w-4" />
            Send an Email
          </Button>
        </div>

        <Dialog open={emailOpen} onOpenChange={setEmailOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Contact a Founders Form Attorney</DialogTitle>
              <DialogDescription>
                Send a message to our team and we'll get back to you promptly.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmitEmail}>
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <label htmlFor="subject" className="text-sm font-medium">
                    Subject
                  </label>
                  <Input
                    id="subject"
                    placeholder="Enter subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm font-medium">
                    Message
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Describe your legal question or concern"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    required
                    className="min-h-[120px]"
                  />
                </div>
              </div>
              <DialogFooter className="mt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setEmailOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>Sending...</>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send Message
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default ContactAttorneySection;
