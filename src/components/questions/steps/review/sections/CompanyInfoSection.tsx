import React from "react";
import { QuestionnaireFormData } from "../../../types";
import { Card, CardContent } from "@/components/common/Card";
import { Check } from "lucide-react";

interface CompanyInfoSectionProps {
  formData: QuestionnaireFormData;
}

const CompanyInfoSection: React.FC<CompanyInfoSectionProps> = ({
  formData,
}) => {
  // Format the company address for display
  const formatAddress = () => {
    const addr = formData.companyAddressStructured;
    if (!addr.street1) return "Not provided";

    let formatted = addr.street1;
    if (addr.street2) formatted += `\n${addr.street2}`;
    formatted += `\n${addr.city}, ${addr.state} ${addr.zipCode}`;

    return formatted;
  };

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-3 flex items-center">
          <Check size={18} className="text-green-500 mr-2" />
          Company Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Company Name</p>
            <p className="font-medium">
              {formData.companyName || "Not provided"}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Incorporating Today</p>
            <p className="font-medium">
              {formData.incorporatingToday ? "Yes" : "No"}
            </p>
          </div>
          {!formData.incorporatingToday &&
            formData.plannedIncorporationDate && (
              <div>
                <p className="text-sm text-gray-500">
                  Planned Incorporation Date
                </p>
                <p className="font-medium">
                  {new Date(
                    formData.plannedIncorporationDate
                  ).toLocaleDateString()}
                </p>
              </div>
            )}
          <div className="md:col-span-2">
            <p className="text-sm text-gray-500">Company Address</p>
            <p className="font-medium whitespace-pre-line">{formatAddress()}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CompanyInfoSection;
