import { APIClient } from "@/integrations/legal-concierge/client";
import {
  CompanyNameRequest,
  CompanyAddressRequest,
  AuthorizedSharesRequest,
} from "@/integrations/legal-concierge/types/Company";
import { StockOptionPlanRequest } from "@/integrations/legal-concierge/types/Stocks";
import { QuestionnaireFormData } from "./types";

// Save company details to legal-concierge API
export const saveCompanyDetails = async (
  _userId: string, // Keeping parameter for API consistency but not using it
  companyId: string,
  formData: QuestionnaireFormData,
  step?: string // Optional step parameter to save only specific data
): Promise<void> => {
  try {
    console.log(
      `saveCompanyDetails called with companyId: ${companyId}, step: ${
        step || "all"
      }`
    );
    const api = new APIClient();

    // Determine which data to save based on the step
    const saveCompanyName =
      !step || step === "company-name" || step === "review";
    const saveAddress =
      !step || step === "company-address" || step === "review";
    const saveShares =
      !step || step === "authorized-shares" || step === "review";
    const saveStockOption = !step || step === "stock-plan" || step === "review";

    console.log(
      `Save flags - name: ${saveCompanyName}, address: ${saveAddress}, shares: ${saveShares}, stockOption: ${saveStockOption}`
    );

    // Update company name and incorporation details if needed
    if (saveCompanyName) {
      console.log("Preparing to save company name data...");
      const nameRequest: CompanyNameRequest = {
        companyId: companyId,
        companyName: formData.companyName,
        isIncorporatedToday: formData.incorporatingToday,
        incorporationDate:
          formData.incorporationDate?.toISOString().split("T")[0] || null,
      };
      console.log("Company name request:", nameRequest);

      try {
        const response = await api.updateCompanyName(companyId, nameRequest);
        console.log("Company name saved successfully:", response);
      } catch (nameError) {
        console.error("Error saving company name:", nameError);
        throw nameError;
      }
    }

    // Update company address if needed
    if (saveAddress) {
      console.log("Preparing to save company address data...");
      const addressRequest: CompanyAddressRequest = {
        companyId: companyId,
        streetAddress: formData.companyAddressStructured.street1,
        streetAddress2: formData.companyAddressStructured.street2,
        city: formData.companyAddressStructured.city,
        state: formData.companyAddressStructured.state,
        zipCode: formData.companyAddressStructured.zipCode,
      };
      console.log("Company address request:", addressRequest);

      try {
        const response = await api.updateCompanyAddress(
          companyId,
          addressRequest
        );
        console.log("Company address saved successfully:", response);
      } catch (addressError) {
        console.error("Error saving company address:", addressError);
        // Continue with other saves even if this one fails
      }
    }

    // Update authorized shares if needed
    if (saveShares) {
      console.log("Preparing to save authorized shares data...");
      const sharesRequest: AuthorizedSharesRequest = {
        companyId: companyId,
        numberOfAuthorizedShares: formData.authorizedShares,
        numberOfAuthorizedSharesToBeIssuedInitially: formData.issuedShares,
        parValuePerShare: formData.parValuePerShare,
      };
      console.log("Authorized shares request:", sharesRequest);

      try {
        const response = await api.updateAuthorizedShares(
          companyId,
          sharesRequest
        );
        console.log("Authorized shares saved successfully:", response);
      } catch (sharesError) {
        console.error("Error saving authorized shares:", sharesError);
        // Continue with other saves even if this one fails
      }
    }

    // Update stock option plan if needed
    if (saveStockOption) {
      console.log("Preparing to save stock option plan data...");
      // Prepare officers ownership data
      const officersOwnership = formData.directors.map((officer) => ({
        officerId: officer.id,
        ownershipPercentage: officer.stockOwnership,
        amountOfShares: officer.amountOfShares,
      }));

      const stockOptionRequest: StockOptionPlanRequest = {
        companyId: companyId,
        includeStockOptionPlan: formData.includeStockOptionPlan,
        stockOptionPlanPercentage: formData.stockOptionPlanPercentage,
        officersOwnership,
      };
      console.log("Stock option plan request:", stockOptionRequest);

      try {
        // Validate total percentage is 100%
        const totalDirectorPercentage = formData.directors.reduce(
          (sum, director) => sum + director.stockOwnership,
          0
        );

        const totalPercentage = formData.includeStockOptionPlan
          ? totalDirectorPercentage + formData.stockOptionPlanPercentage
          : totalDirectorPercentage;

        if (
          Math.abs(totalPercentage - 100) > 0.01 &&
          formData.directors.length > 0
        ) {
          console.warn(
            "Total ownership does not equal 100%. Adjusting percentages..."
          );
          // Auto-adjust stock option plan percentage if enabled
          if (formData.includeStockOptionPlan) {
            stockOptionRequest.stockOptionPlanPercentage =
              100 - totalDirectorPercentage;
          }
        }

        const response = await api.updateStockOptionPlan(
          companyId,
          stockOptionRequest
        );
        console.log("Stock option plan saved successfully:", response);
      } catch (stockOptionError) {
        console.error("Error saving stock option plan:", stockOptionError);
        // Continue with other saves even if this one fails
      }
    }

    console.log("saveCompanyDetails completed successfully");
  } catch (error) {
    console.error("Error in saveCompanyDetails:", error);
    throw error;
  }
};
