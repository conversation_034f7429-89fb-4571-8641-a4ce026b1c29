import React, { useState } from "react";
import {
  Di<PERSON><PERSON>eader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Scroll, ScrollBar } from "@/components/ui/scroll-area";
import SignatureModal from "@/components/signature/SignatureModal";

interface StockholderApprovalSectionProps {
  currentShares: number;
  newTotalShares: number;
  onComplete: () => void;
  onCancel: () => void;
}

const StockholderApprovalSection: React.FC<StockholderApprovalSectionProps> = ({
  currentShares,
  newTotalShares,
  onComplete,
  onCancel,
}) => {
  const [isSignatureModalOpen, setIsSignatureModalOpen] = useState(false);
  const [hasAdminSigned, setHasAdminSigned] = useState(false);

  const handleAdminSignatureComplete = () => {
    setHasAdminSigned(true);
    setIsSignatureModalOpen(false);
  };

  const handleFinish = () => {
    // In a real app, this would send emails to stockholders
    onComplete();
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>Stockholder Approval</DialogTitle>
        <DialogDescription>
          Review and sign the Stockholder Consent for the Certificate of
          Amendment
        </DialogDescription>
      </DialogHeader>

      <div className="my-4 space-y-4">
        <div className="border rounded-md p-4">
          <h3 className="font-semibold mb-2">Stockholder Consent</h3>
          <Scroll className="h-48 overflow-auto">
            <div className="p-2 text-sm">
              <p>ACTION BY WRITTEN CONSENT</p>
              <p>OF THE STOCKHOLDERS OF</p>
              <p>[COMPANY NAME], INC.</p>
              <br />
              <p>
                The undersigned stockholders of [COMPANY NAME], INC., a Delaware
                corporation (the "Company"), representing the holders of a
                majority of the outstanding voting stock of the Company, hereby
                adopt the following resolutions by written consent:
              </p>
              <br />
              <p>
                WHEREAS, the Board of Directors of the Company has approved, and
                recommends that the stockholders of the Company approve, an
                amendment to the Certificate of Incorporation of the Company to
                increase the number of authorized shares of Common Stock from{" "}
                {currentShares.toLocaleString()} to{" "}
                {newTotalShares.toLocaleString()};
              </p>
              <br />
              <p>
                RESOLVED, that the Certificate of Amendment to the Certificate
                of Incorporation of the Company, in the form attached hereto as
                Exhibit A, is hereby approved and adopted; and
              </p>
              <br />
              <p>
                RESOLVED FURTHER, that the officers of the Company are
                authorized and directed to execute and deliver the Certificate
                of Amendment to the Certificate of Incorporation and to take all
                such actions as they deem necessary or appropriate to effectuate
                the foregoing resolutions.
              </p>
              <br />
              <p>
                This action by written consent shall be effective as of the date
                the stockholders holding a majority of the voting power of the
                Company's outstanding stock have signed.
              </p>
            </div>
            <ScrollBar />
          </Scroll>
        </div>

        <div className="pt-2 text-center">
          <Button
            onClick={() => setIsSignatureModalOpen(true)}
            disabled={hasAdminSigned}
            className="mx-auto"
          >
            {hasAdminSigned ? "Signed" : "Sign Consent"}
          </Button>
        </div>
      </div>

      <DialogFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleFinish} disabled={!hasAdminSigned}>
          Finish
        </Button>
      </DialogFooter>

      <SignatureModal
        isOpen={isSignatureModalOpen}
        onClose={() => setIsSignatureModalOpen(false)}
        documentName="Stockholder Consent"
        onComplete={handleAdminSignatureComplete}
      />
    </>
  );
};

export default StockholderApprovalSection;
