import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { CapTableSummary, ProFormaInvestment } from "@/types/capTable";

import ProFormaSummaryHeader from "./proforma/ProFormaSummaryHeader";
import ProFormaInputForm from "./proforma/ProFormaInputForm";
import ProFormaResultsTable from "./proforma/ProFormaResultsTable";
import { useProFormaCalculator } from "./proforma/ProFormaCalculator";
import { formatCurrency } from "./proforma/utils";
import { useProFormaForm } from "./proforma/useProFormaForm";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";

interface ProFormaSummaryProps {
  capTableData: CapTableSummary;
  proFormaInvestment?: ProFormaInvestment;
}

const ProFormaSummary: React.FC<ProFormaSummaryProps> = ({
  capTableData,
  proFormaInvestment,
}) => {
  // Use our custom hook for form state and handling
  const {
    form,
    localProForma,
    valuationMode,
    handleValuationChange,
    handleValuationModeChange,
  } = useProFormaForm(capTableData, proFormaInvestment);

  // Calculate Pro Forma metrics using our custom hook
  const proFormaMetrics = useProFormaCalculator({
    capTableData,
    localProForma,
  });

  if (!localProForma) {
    return (
      <Card>
        <ProFormaSummaryHeader title="" />
        <CardContent>
          <p className="text-center text-muted-foreground py-8">
            No investment data available. Please add a new investment to see pro
            forma calculations.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <ProFormaSummaryHeader title={localProForma.name} />
      <CardContent>
        <Alert className="mb-6 bg-blue-50 border-blue-200">
          <Info className="h-4 w-4 text-blue-500" />
          <AlertDescription className="text-blue-700">
            This is a simulation tool only. Changes made here will not affect
            your actual cap table data or be saved to the system.
          </AlertDescription>
        </Alert>

        <ProFormaInputForm
          form={form}
          handleValuationChange={handleValuationChange}
          valuationMode={valuationMode}
          handleValuationModeChange={handleValuationModeChange}
        />

        <ProFormaResultsTable
          proFormaMetrics={proFormaMetrics}
          formatCurrency={formatCurrency}
          isAmountEdited={localProForma.isAmountEdited}
          isUserEdited={localProForma.isUserEdited}
          totalShares={capTableData.totalShares}
          pricePerShare={localProForma.pricePerShare}
          dilutedPoolUserDefined={
            localProForma.dilutedPoolPercentage !== undefined
          }
        />
      </CardContent>
    </Card>
  );
};

export default ProFormaSummary;
