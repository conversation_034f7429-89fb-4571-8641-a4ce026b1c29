import { useMutation, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { TechnologyRequest } from "../types/Technology";
import { QueryKey } from "../keys";

const api = new APIClient();

// Technology hooks
export const useUpdateTechnology = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, ...payload }: { id: string } & TechnologyRequest) =>
      api.updateTechnology(id, payload),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.id],
      });
    },
  });
};
