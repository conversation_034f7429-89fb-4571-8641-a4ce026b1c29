// hooks/useCompanies.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { Company } from "../types";
import { QueryKey } from "../keys";

const api = new APIClient();

export const useCompanies = (enabled: boolean) =>
  useQuery<Company[], Error>({
    queryKey: [QueryKey().companies],
    queryFn: () => api.getCompanies().then((res) => res.data),
    enabled,
  });

export const useSelectCompany = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => api.selectCompany(id).then((res) => res.data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKey().user] }); // if user info is stored with companyId
    },
  });
};
