import React, { useState } from "react";
import { Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface CommentFormProps {
  onAddComment: (data: { content: string; position?: { x: number; y: number } }) => Promise<void>;
  isUserLoggedIn: boolean;
}

const CommentForm: React.FC<CommentFormProps> = ({
  onAddComment,
  isUserLoggedIn,
}) => {
  const [content, setContent] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) return;

    await onAddComment({ content });
    setContent("");
  };

  if (!isUserLoggedIn) {
    return (
      <div className="text-sm text-gray-500 mb-2">
        Please log in to add comments
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="flex space-x-2">
      <Input
        placeholder="Add a comment..."
        value={content}
        onChange={(e) => setContent(e.target.value)}
        className="flex-1"
      />
      <Button type="submit" size="sm">
        <Plus className="h-4 w-4 mr-1" />
        Add
      </Button>
    </form>
  );
};

export default CommentForm;
