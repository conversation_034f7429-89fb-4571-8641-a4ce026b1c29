import { CompanyEquityInfo } from "@/types/serviceProvider";

export const validateShares = (
  shares: number,
  grantType: "Option" | "Restricted Stock",
  companyEquity: CompanyEquityInfo
): boolean => {
  if (grantType === "Option") {
    if (!companyEquity.stockOptionPlan) return false;
    const remainingOptionShares =
      companyEquity.stockOptionPlan.totalShares -
      companyEquity.stockOptionPlan.allocatedShares;
    return shares <= remainingOptionShares;
  } else {
    // For Restricted Stock
    const remainingShares =
      companyEquity.authorizedShares - companyEquity.issuedShares;
    return shares <= remainingShares;
  }
};
