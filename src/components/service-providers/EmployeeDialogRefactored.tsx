import React from "react";
import BaseServiceProviderDialog from "./BaseServiceProviderDialog";

interface EmployeeDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const EmployeeDialog: React.FC<EmployeeDialogProps> = ({ isOpen, onClose }) => {
  return (
    <BaseServiceProviderDialog
      isOpen={isOpen}
      onClose={onClose}
      type="Employee"
      title="Onboard Employee"
      description="Add a new employee to your company and set up their equity grant."
      confirmTitle="Confirm Employee Details"
      confirmDescription="Review the information before finalizing."
      successTitle="Employee Onboarding Initiated"
      successDescription="The employee has been added successfully and will receive onboarding documentation."
    />
  );
};

export default EmployeeDialog;
