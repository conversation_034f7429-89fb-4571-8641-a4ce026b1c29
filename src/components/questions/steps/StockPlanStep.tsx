import React, { useEffect } from "react";
import { QuestionnaireFormData } from "../types";
import { useSharesCalculation } from "../hooks/useSharesCalculation";
import StockOptionPlanSection from "./directors/StockOptionPlanSection";
import EquityVisualization from "./directors/EquityVisualization";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { useUpdateStockOptionPlan } from "@/integrations/legal-concierge/hooks/useStockAndVesting";

interface StockPlanStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void;
}

// Export the save function so it can be called from the continue button
export const saveStockPlanData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  updateStockOptionPlan: any
): Promise<boolean> => {
  try {
    // Prepare officers ownership data
    const officersOwnership = formData.directors.map((officer) => ({
      officerId: officer.id,
      ownershipPercentage: officer.stockOwnership,
      amountOfShares: officer.amountOfShares,
    }));

    // Validate total percentage is 100%
    const totalDirectorPercentage = formData.directors.reduce(
      (sum, director) => sum + director.stockOwnership,
      0
    );

    const totalPercentage = formData.includeStockOptionPlan
      ? totalDirectorPercentage + formData.stockOptionPlanPercentage
      : totalDirectorPercentage;

    if (
      Math.abs(totalPercentage - 100) > 0.01 &&
      formData.directors.length > 0
    ) {
      console.warn(
        "Total ownership does not equal 100%. Current total:",
        totalPercentage
      );
      toast.error(
        "Total ownership must equal 100%. Please adjust the percentages."
      );
      return false;
    }

    const payload = {
      id: companyId,
      companyId: companyId,
      includeStockOptionPlan: formData.includeStockOptionPlan,
      stockOptionPlanPercentage: formData.stockOptionPlanPercentage,
      officersOwnership,
    };

    console.log("Saving stock plan with payload:", payload);

    await updateStockOptionPlan.mutateAsync(payload);

    console.log("Stock plan saved successfully");
    toast.success("Stock plan information saved successfully");
    return true;
  } catch (error) {
    console.error("Error saving stock plan:", error);
    toast.error("Failed to save stock plan information");
    return false;
  }
};

const StockPlanStep: React.FC<StockPlanStepProps> = ({
  formData,
  updateFormData,
}) => {
  // Use custom hooks to handle complex logic
  const { totalDirectorPercentage, remainingPercentage } = useSharesCalculation(
    formData,
    updateFormData
  );

  // Effect to validate total percentage is 100%
  useEffect(() => {
    // Calculate total percentage including stock option plan if enabled
    const totalPercentage = formData.includeStockOptionPlan
      ? totalDirectorPercentage + formData.stockOptionPlanPercentage
      : totalDirectorPercentage;

    // Check if we need to adjust percentages to sum to 100%
    if (
      Math.abs(totalPercentage - 100) > 0.01 &&
      formData.directors.length > 0
    ) {
      // If we're over 100%, we'll need to adjust
      if (totalPercentage > 100) {
        toast.error(
          "Total ownership exceeds 100%. Please adjust the percentages."
        );
      }
    }
  }, [
    formData.directors,
    formData.includeStockOptionPlan,
    formData.stockOptionPlanPercentage,
    totalDirectorPercentage,
  ]);

  // Handle updating stock option plan percentage
  const handleUpdateOptionPlanPercentage = (percentage: number) => {
    // Calculate the current total for all directors
    const totalDirectorPercentage = formData.directors.reduce(
      (sum, dir) => sum + dir.stockOwnership,
      0
    );

    // Calculate the maximum allowed percentage for the stock option plan
    const maxAllowed = 100 - totalDirectorPercentage;

    // Ensure the percentage doesn't exceed what's available
    const validPercentage = Math.min(percentage, maxAllowed);

    updateFormData("stockOptionPlanPercentage", validPercentage);

    // Recalculate shares for all directors based on the new percentage
    recalculateShares();
  };

  // Recalculate shares for all directors
  const recalculateShares = () => {
    const updatedDirectors = formData.directors.map((director) => {
      const amountOfShares = Math.floor(
        (director.stockOwnership / 100) * formData.issuedShares
      );
      return {
        ...director,
        amountOfShares,
      };
    });

    updateFormData("directors", updatedDirectors);
  };

  // Handle updating officer stock ownership percentage
  const handleUpdateOfficerOwnership = (
    officerId: string,
    percentage: number
  ) => {
    // Calculate the current total excluding this officer
    const currentTotal = formData.directors.reduce(
      (sum, dir) => sum + (dir.id !== officerId ? dir.stockOwnership : 0),
      0
    );

    // Calculate the maximum allowed percentage for this officer
    const maxAllowed = 100 - currentTotal;

    // Ensure the percentage doesn't exceed what's available
    const validPercentage = Math.min(percentage, maxAllowed);

    const updatedDirectors = formData.directors.map((director) => {
      if (director.id === officerId) {
        const amountOfShares = Math.floor(
          (validPercentage / 100) * formData.issuedShares
        );
        return {
          ...director,
          stockOwnership: validPercentage,
          amountOfShares,
        };
      }
      return director;
    });

    updateFormData("directors", updatedDirectors);

    // Auto-update stock option plan percentage if enabled
    if (formData.includeStockOptionPlan) {
      // Calculate remaining percentage after all directors
      const totalDirectorPercentage = updatedDirectors.reduce(
        (sum, dir) => sum + dir.stockOwnership,
        0
      );

      // Set stock option plan to remaining percentage
      const remainingForPlan = Math.max(0, 100 - totalDirectorPercentage);
      updateFormData("stockOptionPlanPercentage", remainingForPlan);
    }
  };

  // This function is not used directly in this component anymore
  // It's kept for reference and potential future use
  // The actual saving happens in the formStorage/saveCompanyDetails.ts file
  // when the user clicks Continue

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-base font-medium mb-4">
          Stock Ownership Distribution
        </h3>

        <div className="space-y-4">
          {formData.directors.map((director) => (
            <div key={director.id} className="p-4 border rounded-lg bg-gray-50">
              <h4 className="font-medium mb-3">
                {director.name ? director.name : `Person #${director.id}`}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`percentage-${director.id}`}>
                    Stock Ownership %
                  </Label>
                  <div className="relative">
                    <Input
                      id={`percentage-${director.id}`}
                      type="text"
                      value={
                        director.stockOwnership === 0
                          ? ""
                          : director.stockOwnership
                      }
                      onChange={(e) => {
                        // Allow empty string for clearing the input
                        if (e.target.value === "") {
                          handleUpdateOfficerOwnership(director.id, 0);
                          return;
                        }

                        const newValue = parseFloat(e.target.value);
                        if (
                          !isNaN(newValue) &&
                          newValue >= 0 &&
                          newValue <= 100
                        ) {
                          handleUpdateOfficerOwnership(director.id, newValue);
                        }
                      }}
                      className="pr-7"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
                      %
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`shares-${director.id}`}>
                    Amount of Shares
                  </Label>
                  <Input
                    id={`shares-${director.id}`}
                    type="text"
                    value={director.amountOfShares?.toLocaleString()}
                    readOnly
                    className="bg-gray-100"
                  />
                  <p className="text-xs text-gray-500">
                    Calculated as {director.stockOwnership}% of total issued
                    shares
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <StockOptionPlanSection
        includeStockOptionPlan={formData.includeStockOptionPlan}
        stockOptionPlanPercentage={formData.stockOptionPlanPercentage}
        issuedShares={formData.issuedShares}
        onToggleOptionPlan={(checked) => {
          updateFormData("includeStockOptionPlan", checked);

          // If enabling the stock option plan, auto-calculate the percentage
          if (checked) {
            const totalDirectorPercentage = formData.directors.reduce(
              (sum, dir) => sum + dir.stockOwnership,
              0
            );

            // Set stock option plan to remaining percentage
            const remainingForPlan = Math.max(0, 100 - totalDirectorPercentage);
            updateFormData("stockOptionPlanPercentage", remainingForPlan);
          } else {
            // If disabling, set to 0
            updateFormData("stockOptionPlanPercentage", 0);
          }
        }}
        onUpdatePercentage={handleUpdateOptionPlanPercentage}
      />

      {formData.directors.length > 0 && (
        <EquityVisualization
          directors={formData.directors}
          includeStockOptionPlan={formData.includeStockOptionPlan}
          stockOptionPlanPercentage={formData.stockOptionPlanPercentage}
        />
      )}

      <div className="mt-4 p-4 border rounded-lg bg-blue-50">
        <div className="flex justify-between items-center">
          <div>
            <p className="font-medium">
              Total Ownership: {totalDirectorPercentage}%
            </p>
            {formData.includeStockOptionPlan && (
              <p className="font-medium">
                Stock Option Plan: {formData.stockOptionPlanPercentage}%
              </p>
            )}
            <p className="text-sm mt-1">
              {remainingPercentage > 0
                ? `Remaining: ${remainingPercentage}%`
                : remainingPercentage < 0
                  ? `Over-allocated by ${Math.abs(remainingPercentage)}%`
                  : "All shares allocated"}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockPlanStep;
