import React, { useState } from "react";
import { Check, FileText } from "lucide-react";
import { Card, CardContent } from "@/components/common/Card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";

const MfnSignDocumentsSection: React.FC = () => {
  const { toast } = useToast();
  const [signDialogOpen, setSignDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);

  const mockDocuments = [
    {
      id: "1",
      name: "Amended and Restated Convertible Note - Investor A",
      type: "convertible-note",
    },
    {
      id: "2",
      name: "Amended and Restated Convertible Note - Investor B",
      type: "convertible-note",
    },
    { id: "3", name: "Amended and Restated SAFE - Investor C", type: "safe" },
    { id: "4", name: "Amended and Restated SAFE - Investor D", type: "safe" },
  ];

  const handleSignClick = () => {
    setSignDialogOpen(true);
  };

  const handleDocumentSelect = (documentId: string) => {
    setSelectedDocument(documentId);
  };

  const handleSign = () => {
    toast({
      title: "Documents Signed",
      description: "The selected documents have been signed successfully.",
    });
    setSignDialogOpen(false);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Sign Documents</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Review and sign amended and restated convertible instruments that
            have been accepted by investors.
          </p>

          <Button onClick={handleSignClick} className="w-full">
            Sign Documentation
          </Button>
        </div>

        <Dialog open={signDialogOpen} onOpenChange={setSignDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Sign Amended Documents</DialogTitle>
              <DialogDescription>
                Please review and sign the following amended and restated
                documents.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="space-y-4">
                {mockDocuments.map((doc) => (
                  <div
                    key={doc.id}
                    className={`p-4 border rounded-md flex items-center justify-between cursor-pointer ${selectedDocument === doc.id ? "border-blue-500 bg-blue-50" : ""}`}
                    onClick={() => handleDocumentSelect(doc.id)}
                  >
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 mr-3 text-gray-500" />
                      <span>{doc.name}</span>
                    </div>
                    {selectedDocument === doc.id && (
                      <Check className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {selectedDocument && (
              <div className="border p-4 rounded-md my-4">
                <h4 className="font-medium mb-2">Document Preview</h4>
                <div className="h-60 border rounded-md p-4 overflow-y-auto bg-gray-50">
                  <p className="text-sm">
                    This is where the selected document would be displayed for
                    review before signing.
                  </p>
                </div>

                <div className="mt-4">
                  <h4 className="font-medium mb-2">Signature</h4>
                  <div className="border h-20 rounded-md bg-gray-50">
                    {/* Signature pad would go here */}
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setSignDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleSign} disabled={!selectedDocument}>
                Sign Document
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default MfnSignDocumentsSection;
