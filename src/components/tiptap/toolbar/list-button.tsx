import { useEditorStore } from "@/store/use-editor-store";
import { ListIcon, ListOrderedIcon } from "lucide-react";
import { ToolbarButton } from "./toolbar-button";

export const ListButton = () => {
  const { editor } = useEditorStore();

  const lists = [
    {
      label: "Bullet List",
      icon: ListIcon,
      isActive: editor?.isActive("bulletList"),
      onClick: () => editor?.chain().focus().toggleBulletList().run(),
    },
    {
      label: "Ordered List",
      icon: ListOrderedIcon,
      isActive: editor?.isActive("orderedList"),
      onClick: () => editor?.chain().focus().toggleOrderedList().run(),
    },
  ];

  return (
    <div className="flex items-center">
      {lists.map(({ label, icon: Icon, onClick, isActive }) => (
        <ToolbarButton
          key={label}
          label={label}
          icon={Icon}
          onClick={onClick}
          isActive={isActive}
        />
      ))}
    </div>
  );
};
