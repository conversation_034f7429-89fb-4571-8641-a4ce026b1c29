import { CompanyDetails } from '@/integrations/legal-concierge/types/Company'
import { QuestionnaireFormData } from '@/components/questions/types'

export const mockServerCompanyData: CompanyDetails = {
  id: 'test-company-id',
  registerMode: 'getting-started',
  userId: 'test-user-id',
  agentId: 'test-agent-id',
  formStateId: 'test-form-state-id',
  name: 'Test Company Inc.',
  isIncorporatedToday: false,
  incorporationDate: '2024-01-15',
  streetAddress: '123 Main St',
  streetAddress2: 'Suite 100',
  city: 'San Francisco',
  state: 'CA',
  zipCode: '94105',
  numberOfAuthorizedShares: 10000000,
  numberOfAuthorizedSharesToBeIssuedInitially: 8000000,
  parValuePerShare: 0.00001,
  includeStockOptionPlan: true,
  stockOptionPlanPercentage: 15,
  isVestingConfirmed: false,
  technologyDescription: 'AI-powered legal technology platform',
  registeredAgent: {
    id: 'agent-1',
    name: 'Test Registered Agent',
    rate: '$299/year',
    description: 'Professional registered agent services',
    rateDescription: 'Annual fee for registered agent services'
  },
  formState: {
    id: 'form-state-1',
    formState: 'company-name'
  },
  isFormConfirmed: false,
  isReviewConfirmed: false,
  isSignatureConfirmed: false,
  isSignatureComplete: false,
  isPostIncorporationConfirmed: false,
  isEinApplyComplete: false,
  isOpeningBankAccountComplete: false,
  isForeignQualificationToDoBusinessComplete: false,
  officers: [
    {
      id: 'officer-1',
      companyId: 'test-company-id',
      name: 'John Doe',
      emailAddress: '<EMAIL>',
      contactAddress: '123 Main St, San Francisco, CA 94105',
      stockOwnership: 70,
      amountOfShares: 5600000,
      isDirector: true,
      isCEO: true,
      isPresident: false,
      isSecretary: false,
      isTreasurer: false,
      notApplicable: false,
      isVester: true,
      intellectualProperties: [
        {
          id: 'ip-1',
          officerId: 'officer-1',
          name: 'Core AI Algorithm',
          isExcluded: false
        }
      ],
      vestingSchedule: 'FOURYEARSONEYEARCLIFF',
      accleration: 'SINGLETRIGGER'
    },
    {
      id: 'officer-2',
      companyId: 'test-company-id',
      name: 'Jane Smith',
      emailAddress: '<EMAIL>',
      contactAddress: '456 Oak Ave, San Francisco, CA 94105',
      stockOwnership: 30,
      amountOfShares: 2400000,
      isDirector: true,
      isCEO: false,
      isPresident: true,
      isSecretary: true,
      isTreasurer: false,
      notApplicable: false,
      isVester: true,
      intellectualProperties: [],
      vestingSchedule: 'FOURYEARSNOCLIFF',
      accleration: 'DOUBLETRIGGER'
    }
  ]
}

export const mockLegacyFormData: QuestionnaireFormData = {
  // Server fields
  ...mockServerCompanyData,
  
  // Legacy client fields
  companyName: 'Test Company Inc.',
  nameAvailabilityChecked: true,
  incorporatingToday: false,
  plannedIncorporationDate: null,
  companyAddress: '123 Main St, Suite 100, San Francisco, CA 94105',
  companyAddressStructured: {
    street1: '123 Main St',
    street2: 'Suite 100',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94105'
  },
  authorizedShares: 10000000,
  issuedShares: 8000000,
  directors: [
    {
      id: 'officer-1',
      companyId: 'test-company-id',
      name: 'John Doe',
      emailAddress: '<EMAIL>',
      contactAddress: '123 Main St, San Francisco, CA 94105',
      stockOwnership: 70,
      amountOfShares: 5600000,
      isDirector: true,
      isCEO: true,
      isPresident: false,
      isSecretary: false,
      isTreasurer: false,
      notApplicable: false,
      isVester: true,
      intellectualProperties: [
        {
          id: 'ip-1',
          officerId: 'officer-1',
          name: 'Core AI Algorithm',
          isExcluded: false
        }
      ],
      vestingSchedule: 'Standard',
      acceleration: 'SingleTrigger',
      officerTitles: ['CEO']
    },
    {
      id: 'officer-2',
      companyId: 'test-company-id',
      name: 'Jane Smith',
      emailAddress: '<EMAIL>',
      contactAddress: '456 Oak Ave, San Francisco, CA 94105',
      stockOwnership: 30,
      amountOfShares: 2400000,
      isDirector: true,
      isCEO: false,
      isPresident: true,
      isSecretary: true,
      isTreasurer: false,
      notApplicable: false,
      isVester: true,
      intellectualProperties: [],
      vestingSchedule: 'Monthly',
      acceleration: 'DoubleTrigger',
      officerTitles: ['President', 'Secretary']
    }
  ],
  vestingInfo: [
    {
      id: 'officer-1',
      isVester: true,
      vestingSchedule: 'Standard',
      acceleration: 'SingleTrigger'
    },
    {
      id: 'officer-2',
      isVester: true,
      vestingSchedule: 'Monthly',
      acceleration: 'DoubleTrigger'
    }
  ],
  companyTech: 'AI-powered legal technology platform',
  founderTechAssignments: [
    {
      founderId: 'officer-1',
      founderName: 'John Doe',
      techItems: [
        {
          id: 'ip-1',
          description: 'Core AI Algorithm',
          isExcluded: false
        }
      ]
    }
  ],
  currentStep: 'company-name'
}
