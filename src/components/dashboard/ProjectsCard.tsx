import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import { File, ArrowRight, FileText, Clipboard, BookOpen } from "lucide-react";
import Button from "@/components/common/Button";
import { Link } from "react-router-dom";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { useRecentProjects } from "@/hooks/useRecentProjects";
import { Progress } from "@/components/ui/progress";
import { useCompanyDetails } from "../questions/hooks/useCompanyDetails";

const ProjectsCard: React.FC = () => {
  const { mostRecentProject, loading } = useRecentProjects();
  const { companyDetails } = useCompanyDetails();

  // Get the appropriate icon based on project type
  const getProjectIcon = (type: string) => {
    switch (type) {
      case "incorporation":
        return <FileText className="h-6 w-6 text-legal-500" />;
      case "questions":
        return <Clipboard className="h-6 w-6 text-indigo-500" />;
      case "financing":
        return <BookOpen className="h-6 w-6 text-green-500" />;
      default:
        return <File className="h-6 w-6 text-gray-500" />;
    }
  };

  // Format the date for display
  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    } else {
      return `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
    }
  };

  const showContent =
    mostRecentProject &&
    mostRecentProject.progress > 0 &&
    companyDetails?.companyName;

  const renderEmptyState = () => (
    console.log(
      "HELLO HFROM THE OTHER SIDE",
      showContent,
      mostRecentProject,
      companyDetails
    ),
    (
      <div className="text-center py-8">
        <div className="bg-gray-50 rounded-full h-16 w-16 mx-auto flex items-center justify-center">
          <File className="h-8 w-8 text-gray-400" />
        </div>
        <h3 className="mt-4 text-gray-900 font-medium">No projects yet</h3>
        <p className="text-gray-500 text-sm mt-1">
          Start by forming your first company
        </p>
        <Button variant="outline" className="mt-4">
          <Link to="/questions">Get Started</Link>
        </Button>
      </div>
    )
  );

  const renderLoadingState = () => (
    <div className="flex justify-center items-center py-8">
      <div className="text-center">
        <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
        <p className="mt-2 text-sm text-gray-500">Loading recent projects...</p>
      </div>
    </div>
  );
  console.log({ mostRecentProject });
  return (
    <AnimatedTransition delay={0.1}>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Recent Projects</CardTitle>
          <CardDescription>Your most recent legal projects</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            renderLoadingState()
          ) : showContent ? (
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="bg-gray-50 rounded-full h-12 w-12 flex items-center justify-center">
                      {getProjectIcon(mostRecentProject.type)}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {mostRecentProject.title}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Last updated {formatDate(mostRecentProject.lastUpdated)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <div className="flex justify-between items-center text-sm mb-1">
                    <span className="text-gray-500">Progress</span>
                    <span className="font-medium">
                      {mostRecentProject.progress}%
                    </span>
                  </div>
                  <Progress
                    value={mostRecentProject.progress}
                    className="h-2"
                  />
                </div>

                <div className="mt-4">
                  <Link to={mostRecentProject.path} className="w-full block">
                    <Button
                      variant="outline"
                      className="w-full justify-between"
                      icon={<ArrowRight size={16} />}
                      iconPosition="right"
                    >
                      {mostRecentProject.progress === 100 ? "View" : "Continue"}
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            renderEmptyState()
          )}
        </CardContent>
      </Card>
    </AnimatedTransition>
  );
};

export default ProjectsCard;
