import { Officer } from "./types/Officer";

export interface QuestionnaireFormData {
  id: string;
  registerMode: string;
  // Company Name
  companyName: string;
  nameAvailabilityChecked: boolean;
  incorporatingToday: boolean | null;
  plannedIncorporationDate: Date | null;
  incorporationDate: Date | null; // Added incorporationDate property

  // Company Address
  companyAddress: string; // Keeping for backward compatibility
  companyAddressStructured: {
    street1: string;
    street2: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // Authorized Shares
  authorizedShares: number;
  issuedShares: number;
  parValuePerShare: number;

  // Directors, Officers, and Shares
  directors: Officer[];
  includeStockOptionPlan: boolean;
  stockOptionPlanPercentage: number;

  // Vesting
  vestingInfo: VestingInfo[];
  isVestingConfirmed: boolean; // Added field for Section 83(b) confirmation

  // Tech
  companyTech: string; // Keeping for backward compatibility
  founderTechAssignments: FounderTechAssignment[];

  // Registered Agent
  registeredAgent: {
    id: string;
    name: string;
    rate?: string;
    description?: string;
    rateDescription?: string;
  } | null;

  // Current Step
  currentStep?: FormStep;

  // Form Status
  isFormConfirmed?: boolean;
  isReviewConfirmed?: boolean;
  isSignatureConfirmed?: boolean;
  isSignatureComplete?: boolean;
  isEinApplyComplete: boolean;
  isOpeningBankAccountComplete: boolean;
  isForeignQualificationToDoBusinessComplete: boolean;
  isPostIncorporationConfirmed?: boolean;
}

export interface VestingInfo {
  id: string; // matching ID from Officer
  isVester: boolean;
  vestingSchedule: VestingSchedule | null;
  acceleration: AccelerationType | null;
}

export interface FounderTechAssignment {
  founderId: string; // matching ID from Officer
  founderName: string;
  techItems: TechItem[];
}

export interface TechItem {
  id?: string; // Server-generated ID when saved to backend
  tempId?: string; // Client-generated temporary ID before saving to backend
  description: string;
  isExcluded?: boolean; // Whether this IP is excluded from assignment
  isSaving?: boolean; // Flag to indicate if the item is currently being saved
  isDeleting?: boolean; // Flag to indicate if the item is currently being deleted
}

export type OfficerTitle =
  | "CEO"
  | "President"
  | "Treasurer"
  | "Secretary"
  | "Not Applicable";
export type VestingSchedule = "Standard" | "Monthly";
export type AccelerationType = "SingleTrigger" | "DoubleTrigger" | "None";

export type FormStep =
  | "company-name"
  | "company-address"
  | "authorized-shares"
  | "directors-and-officers"
  | "stock-plan"
  | "vesting"
  | "technology"
  | "registered-agent"
  | "review";

export const initialFormData: QuestionnaireFormData = {
  companyName: "",
  nameAvailabilityChecked: false,
  incorporatingToday: null,
  plannedIncorporationDate: null,
  incorporationDate: null, // Added initial value for incorporationDate
  companyAddress: "",
  companyAddressStructured: {
    street1: "",
    street2: "",
    city: "",
    state: "",
    zipCode: "",
  },
  authorizedShares: ********,
  issuedShares: 0,
  parValuePerShare: 0.00001,
  directors: [],
  includeStockOptionPlan: false,
  stockOptionPlanPercentage: 0,
  vestingInfo: [],
  isVestingConfirmed: false, // Initialize Section 83(b) confirmation as false
  companyTech: "",
  founderTechAssignments: [],
  registeredAgent: null,
  currentStep: "company-name",
  id: "",
  registerMode: "",
  isFormConfirmed: false,
  isReviewConfirmed: false,
  isSignatureConfirmed: false,
  isEinApplyComplete: false,
  isOpeningBankAccountComplete: false,
  isForeignQualificationToDoBusinessComplete: false,
  isPostIncorporationConfirmed: false,
};
