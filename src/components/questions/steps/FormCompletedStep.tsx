import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, ThumbsUp, <PERSON>Popper } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const FormCompletedStep: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center py-8 space-y-6">
      <div className="flex items-center justify-center w-20 h-20 rounded-full bg-green-100">
        <PartyPopper className="w-10 h-10 text-green-600" />
      </div>
      
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          Form Completed Successfully!
        </h2>
        <p className="text-gray-600 max-w-md">
          Congratulations! You've successfully completed all the required information.
          Your company formation details have been saved and are ready for processing.
        </p>
      </div>

      <div className="bg-blue-50 border border-blue-100 rounded-lg p-6 max-w-md">
        <h3 className="text-lg font-semibold text-blue-800 mb-2 flex items-center">
          <CheckCircle className="w-5 h-5 mr-2" />
          What's Next?
        </h3>
        <p className="text-blue-700">
          Our team will review your information and prepare your incorporation documents.
          You'll be notified when they're ready for your review and signature.
        </p>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mt-4">
        <Button
          onClick={() => navigate("/dashboard")}
          className="bg-green-600 hover:bg-green-700"
        >
          Go to Dashboard
        </Button>
        <Button
          variant="outline"
          onClick={() => navigate("/post-incorporation")}
        >
          View Post-Incorporation Checklist
        </Button>
      </div>
    </div>
  );
};

export default FormCompletedStep;
