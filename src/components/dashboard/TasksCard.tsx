import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/common/Card";
import { Clock } from "lucide-react";
import Button from "@/components/common/Button";
import { Link } from "react-router-dom";
import AnimatedTransition from "@/components/common/AnimatedTransition";

interface TasksCardProps {
  documentsSignedStatus: boolean;
}

const TasksCard: React.FC<TasksCardProps> = ({ documentsSignedStatus }) => {
  return (
    <AnimatedTransition delay={0.3}>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Upcoming Tasks</CardTitle>
          <CardDescription>Tasks requiring your attention</CardDescription>
        </CardHeader>
        <CardContent>
          {documentsSignedStatus ? (
            <div className="space-y-3">
              <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0 bg-amber-100 rounded-full h-10 w-10 flex items-center justify-center mr-3">
                  <Clock className="h-5 w-5 text-amber-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">
                    Apply for an EIN (Tax ID)
                  </h3>
                  <p className="text-sm text-gray-500">
                    Required for taxes, bank accounts, and hiring
                  </p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-shrink-0 mt-1"
                >
                  <Link to="/post-incorporation">Start</Link>
                </Button>
              </div>

              <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0 bg-amber-100 rounded-full h-10 w-10 flex items-center justify-center mr-3">
                  <Clock className="h-5 w-5 text-amber-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">
                    Open a Business Bank Account
                  </h3>
                  <p className="text-sm text-gray-500">
                    Separate your personal and business finances
                  </p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-shrink-0 mt-1"
                >
                  <Link to="/post-incorporation">Start</Link>
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="bg-gray-50 rounded-full h-16 w-16 mx-auto flex items-center justify-center">
                <Clock className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="mt-4 text-gray-900 font-medium">
                No tasks pending
              </h3>
              <p className="text-gray-500 text-sm mt-1">
                You're all caught up!
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </AnimatedTransition>
  );
};

export default TasksCard;
