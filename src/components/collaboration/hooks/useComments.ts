import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Comment } from "../types";
import { parsePositionData } from "../utils/commentUtils";

interface CommentData {
  content: string;
  position?: { x: number; y: number };
}

export const useComments = (
  initialComments?: Comment[],
  addCommentFn?: (data: CommentData) => Promise<void>,
  resolveCommentFn?: (id: string) => Promise<void>
) => {
  const [comments, setComments] = useState<Comment[]>(initialComments || []);
  const [showResolved, setShowResolved] = useState(false);
  const [loading, setLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  const { user } = useAuth();

  const fetchComments = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("document_comments")
        .select("*")
        .order("timestamp", { ascending: false });

      if (error) {
        throw error;
      }

      // Filter out comments that were created on the dashboard
      const filteredData = data.filter(
        (comment) => !comment.document_id.includes("dashboard-comment")
      );

      // Transform the data to match our Comment type
      const formattedComments: Comment[] = filteredData.map((comment) => ({
        id: comment.id,
        userId: comment.user_id,
        userName: comment.user_name,
        documentId: comment.document_id,
        content: comment.content,
        timestamp: new Date(comment.timestamp),
        position: parsePositionData(comment.position),
        resolved: comment.resolved,
      }));

      setComments(formattedComments);
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error("Error in fetchComments:", error);
      if (retryCount < maxRetries) {
        // Exponential backoff for retries
        const delay = Math.pow(2, retryCount) * 1000;
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          fetchComments();
        }, delay);
      } else {
        toast.error("Failed to load comments after multiple attempts");
      }
    } finally {
      setLoading(false);
    }
  }, [user, retryCount]);

  // Fetch comments on mount and when user changes
  useEffect(() => {
    if (!initialComments) {
      fetchComments();
    }
  }, [fetchComments, initialComments]);

  // Update comments when initialComments changes
  useEffect(() => {
    if (initialComments) {
      setComments(initialComments);
      setLoading(false);
    }
  }, [initialComments]);

  const toggleShowResolved = useCallback(() => {
    setShowResolved((prev) => !prev);
  }, []);

  const handleAddComment = useCallback(async (commentData: CommentData) => {
    if (addCommentFn) {
      try {
        await addCommentFn(commentData);
        // Refresh comments after adding
        if (!initialComments) {
          fetchComments();
        }
      } catch (error) {
        console.error("Error adding comment:", error);
        toast.error("Failed to add comment");
      }
    }
  }, [addCommentFn, fetchComments, initialComments]);

  const handleResolveComment = useCallback(async (id: string) => {
    if (resolveCommentFn) {
      try {
        await resolveCommentFn(id);
        // Refresh comments after resolving
        if (!initialComments) {
          fetchComments();
        }
      } catch (error) {
        console.error("Error resolving comment:", error);
        toast.error("Failed to resolve comment");
      }
    }
  }, [resolveCommentFn, fetchComments, initialComments]);

  return {
    comments,
    loading,
    showResolved,
    toggleShowResolved,
    handleAddComment,
    handleResolveComment,
    isUserLoggedIn: !!user,
  };
};
