import React from "react";
import {
  User,
  FileText,
  UserPlus,
  UserX,
  Gift,
  LucideIcon,
} from "lucide-react";
import { MaintenanceItemType } from "./types";

interface ServiceProviderItemsProps {
  onAdvisorClick: () => void;
  onContractorClick: () => void;
  onEmployeeClick: () => void;
  onTerminateClick: () => void;
  onPromisedGrantsClick: () => void;
}

export const getServiceProviderItems = ({
  onAdvisorClick,
  onContractorClick,
  onEmployeeClick,
  onTerminateClick,
  onPromisedGrantsClick,
}: ServiceProviderItemsProps): MaintenanceItemType[] => {
  return [
    {
      icon: User,
      title: "Advisor",
      description: "Onboard advisors to your company.",
      buttonText: "Start",
      onClick: onAdvisorClick,
    },
    {
      icon: FileText,
      title: "Independent Contractor/Consultant",
      description: "Onboard contractors and consultants.",
      buttonText: "Start",
      onClick: onContractorClick,
    },
    {
      icon: UserPlus,
      title: "Employee",
      description: "Onboard new employees to your company.",
      buttonText: "Start",
      onClick: onEmployeeClick,
    },
    {
      icon: UserX,
      title: "Terminate",
      description: "Terminate employees, consultants, or advisors.",
      buttonText: "Start",
      onClick: onTerminateClick,
    },
    {
      icon: Gift,
      title: "Promised Grants",
      description: "Manage and issue promised equity grants.",
      buttonText: "View",
      onClick: onPromisedGrantsClick,
    },
  ];
};
