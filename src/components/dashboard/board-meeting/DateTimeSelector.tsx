import React from "react";
import { format, addDays, isAfter } from "date-fns";
import { Calendar as CalendarIcon, Clock } from "lucide-react";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface DateTimeSelectorProps {
  meetingDate: Date | undefined;
  setMeetingDate: (date: Date | undefined) => void;
  meetingTime: string;
  onTimeChange: (value: string) => void;
  meetingTimezone: string;
  onTimezoneChange: (value: string) => void;
  formErrors: {
    date?: string;
    time?: string;
    purpose?: string;
    agenda?: string;
  };
}

const DateTimeSelector: React.FC<DateTimeSelectorProps> = ({
  meetingDate,
  setMeetingDate,
  meetingTime,
  onTimeChange,
  meetingTimezone,
  onTimezoneChange,
  formErrors,
}) => {
  // Generate time options in 15-minute increments
  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const formattedHour = hour.toString().padStart(2, "0");
        const formattedMinute = minute.toString().padStart(2, "0");
        options.push(`${formattedHour}:${formattedMinute}`);
      }
    }
    return options;
  };

  // Time zones - limited selection for example purposes
  const timeZones = [
    { value: "America/New_York", label: "Eastern Time (ET)" },
    { value: "America/Chicago", label: "Central Time (CT)" },
    { value: "America/Denver", label: "Mountain Time (MT)" },
    { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
    { value: "America/Anchorage", label: "Alaska Time (AKT)" },
    { value: "Pacific/Honolulu", label: "Hawaii Time (HT)" },
  ];

  return (
    <div className="space-y-1.5">
      <Label htmlFor="meeting-date" className="text-sm font-medium">
        Enter Proposed Date and Time of Meeting
      </Label>
      <div className="grid grid-cols-1 gap-2">
        <div className="flex space-x-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "flex-1 h-9 justify-start text-left text-sm font-normal",
                  !meetingDate && "text-muted-foreground",
                  formErrors.date && "border-red-500"
                )}
              >
                <CalendarIcon className="mr-2 h-3.5 w-3.5" />
                {meetingDate ? (
                  format(meetingDate, "PPP")
                ) : (
                  <span>Select a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={meetingDate}
                onSelect={setMeetingDate}
                initialFocus
                className={cn("p-3 pointer-events-auto")}
                disabled={(date) =>
                  date < new Date() || isAfter(new Date(), addDays(date, -2))
                }
              />
            </PopoverContent>
          </Popover>

          {/* Time Selector */}
          <Select value={meetingTime} onValueChange={onTimeChange}>
            <SelectTrigger className="h-9">
              <Clock className="h-3.5 w-3.5 mr-2" />
              <SelectValue placeholder="Time" className="text-sm" />
            </SelectTrigger>
            <SelectContent>
              {generateTimeOptions().map((time) => (
                <SelectItem key={time} value={time} className="text-sm">
                  {format(
                    new Date().setHours(
                      parseInt(time.split(":")[0]),
                      parseInt(time.split(":")[1])
                    ),
                    "h:mm a"
                  )}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Timezone Selector */}
        <Select value={meetingTimezone} onValueChange={onTimezoneChange}>
          <SelectTrigger className="h-9 text-sm">
            <SelectValue placeholder="Select timezone" />
          </SelectTrigger>
          <SelectContent>
            {timeZones.map((tz) => (
              <SelectItem key={tz.value} value={tz.value} className="text-sm">
                {tz.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {formErrors.date && (
          <p className="text-red-500 text-xs mt-1">{formErrors.date}</p>
        )}
      </div>
    </div>
  );
};

export default DateTimeSelector;
