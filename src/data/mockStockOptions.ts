import { SOPSummary } from "@/types/capTable";

// Mock SOP Data
export const mockSOPSummary: SOPSummary = {
  totalPool: 1750000,
  allocated: 1000000,
  remaining: 750000,
  grants: [
    {
      id: "g1",
      recipient: "<PERSON>",
      role: "Employee",
      grantDate: new Date("2023-01-15"),
      optionsGranted: 300000,
      vestingStart: new Date("2023-01-15"),
      vestingPeriod: 48, // 4 years
      cliff: 12, // 1 year
      percentVested: 25,
      currentValue: 375000,
    },
    {
      id: "g2",
      recipient: "<PERSON>",
      role: "Employee",
      grantDate: new Date("2023-03-01"),
      optionsGranted: 250000,
      vestingStart: new Date("2023-03-01"),
      vestingPeriod: 48,
      cliff: 12,
      percentVested: 20,
      currentValue: 312500,
    },
    {
      id: "g3",
      recipient: "<PERSON>",
      role: "Advisor",
      grantDate: new Date("2023-05-15"),
      optionsGranted: 100000,
      vestingStart: new Date("2023-05-15"),
      vestingPeriod: 24, // 2 years
      cliff: 0, // No cliff
      percentVested: 30,
      currentValue: 125000,
    },
    {
      id: "g4",
      recipient: "<PERSON> Smith",
      role: "Employee",
      grantDate: new Date("2023-07-01"),
      optionsGranted: 350000,
      vestingStart: new Date("2023-07-01"),
      vestingPeriod: 48,
      cliff: 12,
      percentVested: 15,
      currentValue: 437500,
    },
  ],
};
