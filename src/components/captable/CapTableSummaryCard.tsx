import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { CapTableSummary } from "@/types/capTable";

interface CapTableSummaryCardProps {
  capTable: CapTableSummary;
}

const CapTableSummaryCard: React.FC<CapTableSummaryCardProps> = ({
  capTable,
}) => {
  return (
    <Card className="mb-6">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Summary</CardTitle>
        <CardDescription>Key metrics about your cap table</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Total Shareholders</div>
            <div className="text-2xl font-bold">
              {capTable.shareholders.length}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Outstanding Shares</div>
            <div className="text-2xl font-bold">
              {capTable.totalShares.toLocaleString()}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">
              Fully Diluted Shares
            </div>
            <div className="text-2xl font-bold">
              {capTable.totalFullyDiluted.toLocaleString()}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Share Classes</div>
            <div className="text-2xl font-bold">
              {capTable.shareClasses.length}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CapTableSummaryCard;
