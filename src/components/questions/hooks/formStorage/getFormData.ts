import { useMemo } from "react";
import {
  QuestionnaireFormData,
  initialFormData,
  FormStep,
  OfficerTitle,
  VestingSchedule,
  AccelerationType,
} from "./types";
import { Officer } from "../../types/Officer";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanyById } from "@/integrations/legal-concierge/hooks/useCompanyInfo";

// Hook to get saved form data from legal-concierge API using react-query
export const useFormData = () => {
  const { user } = useAuth();

  // Fetch company data using react-query for caching
  const { data: companyData, isLoading } = useCompanyById(
    user?.companyId || "",
    !!user?.companyId
  );

  // Transform company data to form data format
  const formData = useMemo(() => {
    if (!companyData) return initialFormData;

    console.log(`Company name from API: "${companyData.name}"`);
    // Start building the form data with company details
    const formattedDetails: QuestionnaireFormData = {
      ...initialFormData,
      registerMode: companyData.registerMode || "",
      companyName: companyData.name || "",
      nameAvailabilityChecked: true,
      incorporationDate: companyData.incorporationDate
        ? new Date(companyData.incorporationDate)
        : null,
      plannedIncorporationDate: null,
      incorporatingToday: companyData.isIncorporatedToday || false,
      companyAddressStructured: {
        street1: companyData.streetAddress || "",
        street2: companyData.streetAddress2 || "",
        city: companyData.city || "",
        state: companyData.state || "",
        zipCode: companyData.zipCode || "",
      },
      authorizedShares:
        companyData.numberOfAuthorizedShares ||
        initialFormData.authorizedShares,
      isVestingConfirmed: companyData.isVestingConfirmed || false,
      registeredAgent: companyData.registeredAgent
        ? {
            id: companyData.registeredAgent.id,
            name: companyData.registeredAgent.name,
            rate: companyData.registeredAgent.rate,
            description: companyData.registeredAgent.description,
            rateDescription: companyData.registeredAgent.rateDescription,
          }
        : null,
      companyTech: companyData.technologyDescription || "",
      issuedShares:
        companyData.numberOfAuthorizedSharesToBeIssuedInitially ||
        initialFormData.issuedShares,
      parValuePerShare:
        companyData.parValuePerShare || initialFormData.parValuePerShare,

      stockOptionPlanPercentage:
        companyData.stockOptionPlanPercentage ||
        initialFormData.stockOptionPlanPercentage,
      includeStockOptionPlan: companyData.includeStockOptionPlan || false,
      currentStep:
        (companyData.formState?.formState as FormStep) || "company-name",
      isFormConfirmed: companyData.isFormConfirmed || false,
      isReviewConfirmed: companyData.isReviewConfirmed || false,
      isSignatureConfirmed: companyData.isSignatureConfirmed || false,
      isSignatureComplete: companyData.isSignatureComplete || false,
      isEinApplyComplete: companyData.isEinApplyComplete || false,
      isOpeningBankAccountComplete:
        companyData.isOpeningBankAccountComplete || false,
      isForeignQualificationToDoBusinessComplete:
        companyData.isForeignQualificationToDoBusinessComplete || false,
      isPostIncorporationConfirmed:
        companyData.isPostIncorporationConfirmed || false,
    };

    // Map officers to directors
    if (companyData.officers && companyData.officers.length > 0) {
      // Use the officers directly from the API with embedded vesting information
      formattedDetails.directors = companyData.officers.map(
        (officer): Officer => {
          // Create officerTitles array for backward compatibility
          const officerTitles: OfficerTitle[] = [];
          if (officer.isCEO) officerTitles.push("CEO");
          if (officer.isPresident) officerTitles.push("President");
          if (officer.isSecretary) officerTitles.push("Secretary");
          if (officer.isTreasurer) officerTitles.push("Treasurer");
          if (officer.notApplicable) officerTitles.push("Not Applicable");

          // Map API vesting schedule to app vesting schedule
          let vestingSchedule: VestingSchedule | null = null;
          if (officer.vestingSchedule === "FOURYEARSONEYEARCLIFF")
            vestingSchedule = "Standard";
          if (officer.vestingSchedule === "FOURYEARSNOCLIFF")
            vestingSchedule = "Monthly";

          // Map API acceleration to app acceleration
          let acceleration: AccelerationType | null = null;
          if (officer.accleration === "SINGLETRIGGER")
            acceleration = "SingleTrigger";
          if (officer.accleration === "DOUBLETRIGGER")
            acceleration = "DoubleTrigger";
          if (officer.accleration === "NONE") acceleration = "None";

          // Return the officer with added officerTitles property and embedded vesting data
          return {
            ...officer,
            officerTitles:
              officerTitles.length > 0
                ? officerTitles
                : (["Not Applicable"] as OfficerTitle[]),
            // Embed vesting information directly in the officer object
            isVester: officer.isVester,
            vestingSchedule,
            acceleration,
          } as Officer;
        }
      );

      // Create vestingInfo array for backward compatibility with existing UI components
      // This maintains the existing interface while the data is now stored directly in officers
      formattedDetails.vestingInfo = formattedDetails.directors.map(
        (director) => ({
          id: director.id,
          isVester: director.isVester || false,
          vestingSchedule: director.vestingSchedule || null,
          acceleration: director.acceleration || null,
        })
      );

      // Map tech assignments - only include officers with intellectual properties
      formattedDetails.founderTechAssignments = companyData.officers.map(
        (officer) => ({
          founderId: officer.id,
          founderName: officer.name,
          techItems: officer.intellectualProperties.map((ip) => ({
            id: ip.id,
            description: ip.name,
            isExcluded: ip.isExcluded,
          })),
        })
      );
    }

    return formattedDetails;
  }, [companyData]);

  return {
    formData,
    isLoading,
    currentStep: formData.currentStep,
  };
};

// Legacy function for backward compatibility - now just returns the current step from form data
export const getSavedStep = (formData: QuestionnaireFormData): FormStep => {
  return formData.currentStep || "company-name";
};

// Legacy function for backward compatibility - now returns the form data directly
export const getSavedFormData = (
  formData: QuestionnaireFormData
): QuestionnaireFormData => {
  return formData;
};
