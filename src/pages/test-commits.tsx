import React from "react";
import { Editor } from "@/components/tiptap/editor";
import { Toolbar } from "@/components/tiptap/toolbar";
import { EditorCommentsProvider } from "@/contexts/EditorCommentsContext";

const TestCommitsPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold mb-6">Test Commits Dropdown</h1>

          <div className="mb-4">
            <h2 className="text-lg font-semibold mb-2">Instructions:</h2>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
              <li>Type some content in the editor below</li>
              <li>
                Click the Save button (or press Ctrl+S) to create a commit
              </li>
              <li>
                Make more changes and save again to create multiple commits
              </li>
              <li>
                Click the Commits dropdown (git icon with arrow) to see the
                commit history
              </li>
              <li>
                Click on any commit to highlight the changes in the editor
              </li>
              <li>
                Click the same commit again or "Clear highlighting" to remove
                highlights
              </li>
            </ol>
          </div>
          <EditorCommentsProvider>
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-100 p-2">
                <Toolbar />
              </div>
              <div className="min-h-[400px]">
                <Editor
                  initialContent="<p>Start typing here to test the commits functionality...</p>"
                  documentId="test-document"
                />
              </div>
            </div>
          </EditorCommentsProvider>

          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">
              Expected Behavior:
            </h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-blue-800">
              <li>
                The commits dropdown should show "No commits yet" initially
              </li>
              <li>
                After saving changes, commits should appear in the dropdown
              </li>
              <li>
                Clicking a commit should highlight the changes with a yellow
                background
              </li>
              <li>
                The selected commit should show "Highlighted" status in the
                dropdown
              </li>
              <li>
                Changes should be visually highlighted in the editor content
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestCommitsPage;
