import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import SignaturePad from "./SignaturePad";
import { Check, ThumbsUp } from "lucide-react";

interface SignatureModalProps {
  isOpen: boolean;
  onClose: () => void;
  documentName: string;
  onComplete: () => void;
}

const SignatureModal: React.FC<SignatureModalProps> = ({
  isOpen,
  onClose,
  documentName,
  onComplete,
}) => {
  const [signature, setSignature] = useState<string | null>(null);
  const [step, setStep] = useState<"draw" | "review" | "complete">("draw");

  const handleSignatureSave = (signatureData: string) => {
    setSignature(signatureData);
    setStep("review");
  };

  const handleConfirm = () => {
    // In a real app, this would send the signature to the backend
    setStep("complete");

    // Simulate a short delay before calling onComplete
    setTimeout(() => {
      onComplete();
      // Reset the state for next time
      setSignature(null);
      setStep("draw");
    }, 2000);
  };

  const handleClose = () => {
    setSignature(null);
    setStep("draw");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {step === "complete"
              ? "Signature Complete"
              : `Sign: ${documentName}`}
          </DialogTitle>
          {step !== "complete" && (
            <DialogDescription>
              Please draw your signature below to electronically sign this
              document.
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="py-4">
          {step === "draw" && (
            <SignaturePad
              onSave={handleSignatureSave}
              width={400}
              height={180}
            />
          )}

          {step === "review" && signature && (
            <div className="flex flex-col items-center space-y-4">
              <div className="border rounded p-4 bg-gray-50">
                <h3 className="text-sm font-medium mb-2">
                  Review your signature:
                </h3>
                <img
                  src={signature}
                  alt="Your signature"
                  className="max-w-full h-auto"
                />
              </div>

              <div className="flex space-x-4 mt-4">
                <Button variant="outline" onClick={() => setStep("draw")}>
                  Redraw
                </Button>
                <Button onClick={handleConfirm}>
                  <Check size={16} className="mr-2" />
                  Confirm Signature
                </Button>
              </div>
            </div>
          )}

          {step === "complete" && (
            <div className="flex flex-col items-center space-y-4">
              <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
                <ThumbsUp className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-center">
                Document signed successfully!
              </h3>
              <p className="text-gray-500 text-center">
                All signatories will be notified when all signatures are
                complete.
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SignatureModal;
