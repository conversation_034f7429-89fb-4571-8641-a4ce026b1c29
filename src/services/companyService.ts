import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface CompanyDetails {
  id?: string;
  user_id?: string;
  company_name: string;
  incorporation_date?: string | null;
  planned_incorporation_date?: string | null;
  incorporating_today?: boolean;
  company_address?: any;
  authorized_shares?: number;
  issued_shares?: number;
  par_value_per_share?: number;
  stock_option_plan_percentage?: number;
}

export const companyService = {
  async getCompanyDetails(userId: string): Promise<CompanyDetails | null> {
    try {
      const { data, error } = await supabase
        .from("company_details")
        .select("*")
        .eq("user_id", userId)
        .maybeSingle();

      if (error) {
        console.error("Error fetching company details:", error);
        return null;
      }

      return data as CompanyDetails;
    } catch (error) {
      console.error("Error in getCompanyDetails:", error);
      return null;
    }
  },

  async saveCompanyDetails(
    details: CompanyDetails
  ): Promise<CompanyDetails | null> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("You must be logged in to save company details");
        return null;
      }

      // Check if company details already exist for this user
      const { data: existingData } = await supabase
        .from("company_details")
        .select("id")
        .eq("user_id", user.id)
        .maybeSingle();

      let response;

      if (existingData?.id) {
        // Update existing record
        response = await supabase
          .from("company_details")
          .update({ ...details, user_id: user.id })
          .eq("id", existingData.id)
          .select();
      } else {
        // Insert new record
        response = await supabase
          .from("company_details")
          .insert({ ...details, user_id: user.id })
          .select();
      }

      const { data, error } = response;

      if (error) {
        console.error("Error saving company details:", error);
        toast.error("Failed to save company details");
        return null;
      }

      console.log("Company details saved successfully:", data);
      return (data && data[0]) as CompanyDetails;
    } catch (error) {
      console.error("Error in saveCompanyDetails:", error);
      toast.error("An error occurred while saving company details");
      return null;
    }
  },
};
