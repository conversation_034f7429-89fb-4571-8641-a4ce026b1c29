import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface BoardMeeting {
  id?: string;
  company_id: string;
  user_id?: string;
  meeting_date: string;
  meeting_timezone: string;
  meeting_purpose: string;
  agenda_file_url?: string | null;
  board_materials?: any;
  is_cancelled?: boolean;
  created_at?: string;
  updated_at?: string;
}

export const boardMeetingService = {
  async getMeetingsForCompany(companyId: string): Promise<BoardMeeting[]> {
    try {
      const { data, error } = await supabase
        .from("board_meetings")
        .select("*")
        .eq("company_id", companyId);

      if (error) {
        console.error("Error fetching board meetings:", error);
        return [];
      }

      return data as BoardMeeting[];
    } catch (error) {
      console.error("Error in getMeetings:", error);
      return [];
    }
  },

  async addMeeting(meeting: BoardMeeting): Promise<BoardMeeting | null> {
    try {
      const userId = (await supabase.auth.getUser()).data.user?.id;

      if (!userId) {
        toast.error("You must be logged in to schedule a meeting");
        return null;
      }

      const { data, error } = await supabase
        .from("board_meetings")
        .insert({ ...meeting, user_id: userId })
        .select();

      if (error) {
        console.error("Error adding board meeting:", error);
        toast.error("Failed to schedule board meeting");
        return null;
      }

      toast.success("Board meeting scheduled successfully");
      return data[0] as BoardMeeting;
    } catch (error) {
      console.error("Error in addMeeting:", error);
      toast.error("An error occurred while scheduling the meeting");
      return null;
    }
  },

  async updateMeeting(
    id: string,
    updates: Partial<BoardMeeting>
  ): Promise<BoardMeeting | null> {
    try {
      const { data, error } = await supabase
        .from("board_meetings")
        .update(updates)
        .eq("id", id)
        .select();

      if (error) {
        console.error("Error updating board meeting:", error);
        toast.error("Failed to update board meeting");
        return null;
      }

      toast.success("Board meeting updated successfully");
      return data[0] as BoardMeeting;
    } catch (error) {
      console.error("Error in updateMeeting:", error);
      toast.error("An error occurred while updating the meeting");
      return null;
    }
  },

  async cancelMeeting(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("board_meetings")
        .update({ is_cancelled: true })
        .eq("id", id);

      if (error) {
        console.error("Error cancelling board meeting:", error);
        toast.error("Failed to cancel board meeting");
        return false;
      }

      toast.success("Board meeting cancelled successfully");
      return true;
    } catch (error) {
      console.error("Error in cancelMeeting:", error);
      toast.error("An error occurred while cancelling the meeting");
      return false;
    }
  },
};
