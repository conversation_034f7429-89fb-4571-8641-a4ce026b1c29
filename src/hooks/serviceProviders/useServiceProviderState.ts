import { useState, useEffect } from "react";
import { ServiceProvider, PromisedGrant } from "@/types/serviceProvider";
import { INITIAL_COMPANY_EQUITY } from "./initialState";
import { UseServiceProvidersState } from "./types";
import { serviceProviderService } from "@/services/serviceProviderService";
import { promisedGrantService } from "@/services/promisedGrantService";
import { supabase } from "@/integrations/supabase/client";

export function useServiceProviderState(): UseServiceProvidersState {
  const [serviceProviders, setServiceProviders] = useState<ServiceProvider[]>(
    []
  );
  const [promisedGrants, setPromisedGrants] = useState<PromisedGrant[]>([]);
  const [companyEquity, setCompanyEquity] = useState(INITIAL_COMPANY_EQUITY);
  const [loading, setLoading] = useState(true);

  // Load data from Supabase
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);

      try {
        // Check if user is authenticated
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          setLoading(false);
          return;
        }

        // Get company details
        const { data: companyData } = await supabase
          .from("company_details")
          .select("*")
          .limit(1)
          .single();

        if (companyData) {
          // Update company equity based on database values
          setCompanyEquity({
            authorizedShares:
              companyData.authorized_shares ||
              INITIAL_COMPANY_EQUITY.authorizedShares,
            issuedShares:
              companyData.issued_shares || INITIAL_COMPANY_EQUITY.issuedShares,
            stockOptionPlan: companyData.stock_option_plan_percentage
              ? {
                  totalShares: Math.floor(
                    companyData.authorized_shares *
                      (companyData.stock_option_plan_percentage / 100)
                  ),
                  allocatedShares: 0, // This will be updated when we load promised grants
                }
              : undefined,
          });
        }

        // Load service providers
        const providers =
          await serviceProviderService.getServiceProvidersForCompany(
            companyData?.id || ""
          );
        setServiceProviders(providers);

        // Load promised grants
        const grants = await promisedGrantService.getPromisedGrantsForUser();
        setPromisedGrants(grants);

        // Update allocated shares based on promised grants
        if (companyData?.stock_option_plan_percentage && grants.length > 0) {
          const optionGrants = grants.filter((g) => g.grantType === "Option");
          const optionShares = optionGrants.reduce(
            (sum, g) => sum + g.shares,
            0
          );

          setCompanyEquity((prev) => ({
            ...prev,
            stockOptionPlan: {
              ...prev.stockOptionPlan!,
              allocatedShares: optionShares,
            },
          }));
        }
      } catch (error) {
        console.error("Error loading service provider data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return {
    serviceProviders,
    promisedGrants,
    companyEquity,
    loading,
  };
}

// Export state setters for actions to use
export function useServiceProviderStateSetters() {
  const [serviceProviders, setServiceProviders] = useState<ServiceProvider[]>(
    []
  );
  const [promisedGrants, setPromisedGrants] = useState<PromisedGrant[]>([]);
  const [companyEquity, setCompanyEquity] = useState(INITIAL_COMPANY_EQUITY);
  const [loading, setLoading] = useState(false);

  return {
    setServiceProviders,
    setPromisedGrants,
    setCompanyEquity,
    setLoading,
  };
}
