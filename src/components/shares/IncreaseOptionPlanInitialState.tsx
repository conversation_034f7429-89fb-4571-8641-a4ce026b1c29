import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { Link } from "react-router-dom";
import {
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface IncreaseOptionPlanInitialStateProps {
  sharesReservedForIssuance: number;
  promisedGrants: number;
  sharesRemaining: number;
  additionalShares: string;
  totalOptionShares: number;
  exceedsAuthorized: boolean;
  requiredAuthorizedShares: number;
  boardApproved: boolean;
  stockholdersApproved: boolean;
  handleAdditionalSharesChange: (
    e: React.ChangeEvent<HTMLInputElement>
  ) => void;
  handleClose: () => void;
  setStep: (step: "initial" | "board" | "stockholders") => void;
}

const IncreaseOptionPlanInitialState: React.FC<
  IncreaseOptionPlanInitialStateProps
> = ({
  sharesReservedForIssuance,
  promisedGrants,
  sharesRemaining,
  additionalShares,
  totalOptionShares,
  exceedsAuthorized,
  requiredAuthorizedShares,
  boardApproved,
  stockholdersApproved,
  handleAdditionalSharesChange,
  handleClose,
  setStep,
}) => {
  return (
    <>
      <DialogHeader>
        <DialogTitle>Increase Stock Option Plan</DialogTitle>
        <DialogDescription>
          Add more shares to your company's stock option plan
        </DialogDescription>
      </DialogHeader>

      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="sharesReserved" className="text-right">
            Shares Reserved:
          </Label>
          <Input
            id="sharesReserved"
            value={sharesReservedForIssuance.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="promisedGrants" className="text-right">
            Promised Grants:
          </Label>
          <Input
            id="promisedGrants"
            value={promisedGrants.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="sharesRemaining" className="text-right">
            Shares Remaining:
          </Label>
          <Input
            id="sharesRemaining"
            value={sharesRemaining.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="additionalShares" className="text-right">
            Additional Shares:
          </Label>
          <Input
            id="additionalShares"
            value={additionalShares}
            onChange={handleAdditionalSharesChange}
            className="col-span-3"
            placeholder="Enter number of shares"
          />
        </div>

        {additionalShares && !exceedsAuthorized && (
          <Alert className="mt-2">
            <AlertDescription className="italic">
              The total amount of shares reserved under the stock option plan
              will be {totalOptionShares.toLocaleString()} shares. To authorize
              the increase to stock option plan, you will need to obtain board
              consent and stockholder consent.
            </AlertDescription>
          </Alert>
        )}

        {exceedsAuthorized && (
          <Alert className="mt-2 border-amber-500">
            <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
            <AlertDescription className="italic">
              The total amount of shares reserved under the stock option plan,{" "}
              {totalOptionShares.toLocaleString()}, exceeds the authorized share
              capital of the company. To increase the stock option plan, please
              first{" "}
              <Link
                to="/dashboard"
                className="text-blue-500 underline"
                onClick={handleClose}
              >
                Increase Authorized Shares
              </Link>{" "}
              to at least {requiredAuthorizedShares.toLocaleString()}.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Board Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">{boardApproved ? "Yes" : "No"}</span>
            <Button
              variant="outline"
              disabled={!additionalShares || boardApproved || exceedsAuthorized}
              onClick={() => setStep("board")}
            >
              Submit for Board Approval
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Stockholders Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">
              {stockholdersApproved ? "Yes" : "No"}
            </span>
            <Button
              variant="outline"
              disabled={
                !additionalShares || stockholdersApproved || exceedsAuthorized
              }
              onClick={() => setStep("stockholders")}
            >
              Submit for Stockholder Approval
            </Button>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={handleClose}>
          Cancel
        </Button>
      </DialogFooter>
    </>
  );
};

export default IncreaseOptionPlanInitialState;
