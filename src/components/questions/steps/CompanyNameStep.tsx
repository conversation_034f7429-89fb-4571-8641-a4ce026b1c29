import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { QuestionnaireFormData } from "../types";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface CompanyNameStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void;
}

// Export the save function so it can be called from the continue button
export const saveCompanyNameData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  updateCompanyName: any
): Promise<boolean> => {
  try {
    const payload = {
      id: companyId,
      companyId: companyId,
      companyName: formData.companyName,
      isIncorporatedToday: formData.incorporatingToday,
      incorporationDate: formData.incorporationDate
        ? formData.incorporationDate.toLocaleDateString("en-CA") // This will format as YYYY-MM-DD in local timezone
        : null,
    };

    console.log("Saving company name with payload:", payload);

    await updateCompanyName.mutateAsync(payload);

    console.log("Company name saved successfully");
    toast.success("Company name information saved successfully");
    return true;
  } catch (error) {
    console.error("Error saving company name:", error);
    toast.error("Failed to save company name information");
    return false;
  }
};

const CompanyNameStep: React.FC<CompanyNameStepProps> = ({
  formData,
  updateFormData,
}) => {
  const [localName, setLocalName] = useState(formData.companyName);
  const [isUserEditing, setIsUserEditing] = useState(false);

  // Only update local state from formData if user is not actively editing
  useEffect(() => {
    if (!isUserEditing) {
      setLocalName(formData.companyName);
    }
  }, [formData.companyName, isUserEditing]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setLocalName(name);
    setIsUserEditing(true);

    // Don't automatically append ", Inc." - just use the name as entered
    updateFormData("companyName", name);
  };

  // Reset editing state when user stops typing (debounced)
  useEffect(() => {
    if (isUserEditing) {
      const timer = setTimeout(() => {
        setIsUserEditing(false);
      }, 1000); // 1 second after user stops typing

      return () => clearTimeout(timer);
    }
  }, [localName, isUserEditing]);

  // When the user selects they're incorporating today, set the incorporation date to today
  const handleIncorporatingTodayChange = (value: string) => {
    const isIncorporatingToday = value === "yes";
    updateFormData("incorporatingToday", isIncorporatingToday);

    if (isIncorporatingToday) {
      // If incorporating today, set the incorporation date to today
      updateFormData("incorporationDate", new Date());
    } else {
      // If not incorporating today, clear the incorporation date
      // We'll let the user select a date
      updateFormData("incorporationDate", null);
    }
  };

  // Handle change to the incorporation date
  const handleIncorporationDateChange = (date: Date | undefined) => {
    if (date) {
      updateFormData("incorporationDate", date);
    } else {
      updateFormData("incorporationDate", null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="company-name" className="text-base font-medium">
          {formData.registerMode == "getting-started"
            ? "What is the preferred name of the Company?"
            : "What is the name of your incorporated company?"}
        </Label>
        <Input
          id="company-name"
          value={localName}
          onChange={handleNameChange}
          placeholder="Enter company name"
          className="w-full"
        />
        <p className="text-sm text-gray-500">
          Your company will be registered as "
          {formData.companyName || "[Company Name]"}"
          {!formData.companyName?.toLowerCase().includes("inc") &&
            !formData.companyName?.toLowerCase().includes("incorporated") &&
            formData.companyName &&
            ". Consider adding 'Inc.' or 'Incorporated' to your company name."}
        </p>
      </div>

      {formData.registerMode == "getting-started" && (
        <div className="space-y-2 pt-4">
          <Label className="text-base font-medium">
            Are you planning to incorporate today?
          </Label>
          <RadioGroup
            value={
              formData.incorporatingToday === null
                ? undefined
                : formData.incorporatingToday
                  ? "yes"
                  : "no"
            }
            onValueChange={handleIncorporatingTodayChange}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yes" id="incorporate-yes" />
              <Label htmlFor="incorporate-yes" className="cursor-pointer">
                Yes
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id="incorporate-no" />
              <Label htmlFor="incorporate-no" className="cursor-pointer">
                No
              </Label>
            </div>
          </RadioGroup>
        </div>
      )}

      {formData.incorporatingToday === false && (
        <div className="space-y-2 pt-2">
          <Label className="text-base font-medium">
            {formData.registerMode == "getting-started"
              ? "When do you plan to incorporate?"
              : "When did you incorporate?"}
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.incorporationDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.incorporationDate
                  ? format(formData.incorporationDate, "PPP")
                  : "Select incorporation date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.incorporationDate || undefined}
                onSelect={handleIncorporationDateChange}
                initialFocus
                disabled={(date) =>
                  formData.registerMode == "getting-started"
                    ? date < new Date()
                    : false
                }
                className={cn("p-3 pointer-events-auto")}
              />
            </PopoverContent>
          </Popover>
          <p className="text-sm text-gray-500">
            {formData.registerMode == "getting-started"
              ? "Please select a future date when you plan to incorporate"
              : "Please select a date when your company was incorporated"}
          </p>
        </div>
      )}

      {/* Display current incorporation date if set */}
      {formData.incorporationDate && (
        <div className="pt-2 text-sm">
          <span className="font-medium">Incorporation Date: </span>
          {format(formData.incorporationDate, "MMMM d, yyyy")}
        </div>
      )}
    </div>
  );
};

export default CompanyNameStep;
