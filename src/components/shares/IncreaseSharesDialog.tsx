import React, { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import BoardApprovalSection from "./BoardApprovalSection";
import StockholderApprovalSection from "./StockholderApprovalSection";
import FileCertificateSection from "./FileCertificateSection";
import IncreaseSharesInitialState from "./IncreaseSharesInitialState";

interface IncreaseSharesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentAuthorizedShares: number;
}

const IncreaseSharesDialog: React.FC<IncreaseSharesDialogProps> = ({
  isOpen,
  onClose,
  currentAuthorizedShares,
}) => {
  const { toast } = useToast();
  const [additionalShares, setAdditionalShares] = useState<string>("");
  const [totalShares, setTotalShares] = useState<number>(
    currentAuthorizedShares
  );
  const [boardApproved, setBoardApproved] = useState<boolean>(false);
  const [stockholdersApproved, setStockholdersApproved] =
    useState<boolean>(false);
  const [step, setStep] = useState<
    "initial" | "board" | "stockholders" | "file"
  >("initial");

  // Calculate total shares when additional shares change
  useEffect(() => {
    const additionalSharesNum = additionalShares
      ? parseInt(additionalShares.replace(/,/g, ""), 10)
      : 0;
    if (!isNaN(additionalSharesNum)) {
      setTotalShares(currentAuthorizedShares + additionalSharesNum);
    } else {
      setTotalShares(currentAuthorizedShares);
    }
  }, [additionalShares, currentAuthorizedShares]);

  const handleAdditionalSharesChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    // Remove commas and ensure only numbers are entered
    const value = e.target.value.replace(/[^0-9]/g, "");
    // Format with commas
    const formattedValue = value ? parseInt(value, 10).toLocaleString() : "";
    setAdditionalShares(formattedValue);
  };

  const handleBoardApprovalComplete = () => {
    setBoardApproved(true);
    setStep("initial");
    toast({
      title: "Board Approval Process Initiated",
      description: "Email notifications have been sent to all board members.",
    });
  };

  const handleStockholderApprovalComplete = () => {
    setStockholdersApproved(true);
    setStep("initial");
    toast({
      title: "Stockholder Approval Process Initiated",
      description: "Email notifications have been sent to all stockholders.",
    });
  };

  const handleFilingComplete = () => {
    toast({
      title: "Certificate of Amendment Filed",
      description:
        "The amendment has been sent to your registered agent for filing.",
    });
    onClose();
  };

  const resetState = () => {
    setAdditionalShares("");
    setBoardApproved(false);
    setStockholdersApproved(false);
    setStep("initial");
  };

  const handleClose = () => {
    resetState();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md sm:max-w-lg">
        {step === "initial" && (
          <IncreaseSharesInitialState
            currentAuthorizedShares={currentAuthorizedShares}
            totalShares={totalShares}
            additionalShares={additionalShares}
            boardApproved={boardApproved}
            stockholdersApproved={stockholdersApproved}
            onAdditionalSharesChange={handleAdditionalSharesChange}
            onClose={handleClose}
            onBoardApprovalClick={() => setStep("board")}
            onStockholderApprovalClick={() => setStep("stockholders")}
            onFileAmendmentClick={() => setStep("file")}
          />
        )}

        {step === "board" && (
          <BoardApprovalSection
            currentShares={currentAuthorizedShares}
            newTotalShares={totalShares}
            onComplete={handleBoardApprovalComplete}
            onCancel={() => setStep("initial")}
          />
        )}

        {step === "stockholders" && (
          <StockholderApprovalSection
            currentShares={currentAuthorizedShares}
            newTotalShares={totalShares}
            onComplete={handleStockholderApprovalComplete}
            onCancel={() => setStep("initial")}
          />
        )}

        {step === "file" && (
          <FileCertificateSection
            currentShares={currentAuthorizedShares}
            newTotalShares={totalShares}
            onComplete={handleFilingComplete}
            onCancel={() => setStep("initial")}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default IncreaseSharesDialog;
