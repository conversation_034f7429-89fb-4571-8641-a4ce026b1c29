import { RoleBasedAccess } from "@/components/auth/RoleBasedAccess";
import DocumentModal from "@/components/documents/DocumentModal";
import DocumentsList from "@/components/documents/DocumentsList";
import Layout from "@/components/Layout";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";
import { useCompanyById } from "@/integrations/legal-concierge/hooks/useCompanyInfo";
import {
  useSendDocumentForSignatureSQS,
  useSignatureStatus,
} from "@/integrations/legal-concierge/hooks/useDocumentReview";
import { useCompanyDocuments } from "@/integrations/legal-concierge/hooks/useDocuments";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { Document } from "@/integrations/legal-concierge/types/Document";
import { CheckCircle, Clock, FileText } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const ReviewDocuments: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { companyDetails } = useCompanyDetails();
  const { isCompanySelected } = useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);
  const companyId = user?.companyId || "";

  // Add company refetch capability for when signature completes
  const { refetch: refetchCompanyDetails } = useCompanyById(
    companyId,
    !!companyId
  );

  const {
    data: documents,
    isLoading,
    error,
    refetch,
  } = useCompanyDocuments(companyId);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null
  );
  const [documentMode, setDocumentMode] = useState<"view" | "edit">("view");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [currentStep, setCurrentStep] = useState<
    "idle" | "processing" | "completed"
  >("idle");
  const [isPollingActive, setIsPollingActive] = useState(false);

  const isReviewConfirmed = companyDetails?.isReviewConfirmed;
  const isSignatureConfirmed = companyDetails?.isSignatureConfirmed;

  // State recovery: Detect if signature process is already in progress
  useEffect(() => {
    if (isReviewConfirmed && !isSignatureConfirmed && currentStep === "idle") {
      console.log(
        `[ReviewDocuments] Detected signature process in progress, starting state recovery`
      );
      setCurrentStep("processing");
      setIsPollingActive(true);
      console.log(`[ReviewDocuments] State recovered: polling activated`);
    }
  }, [isReviewConfirmed, isSignatureConfirmed, currentStep]);

  // Use the new SQS-based hooks
  const sendDocumentForSignatureSQS = useSendDocumentForSignatureSQS();

  // Poll when manually triggered OR when review is confirmed but signature is not confirmed
  const shouldPoll =
    isPollingActive || (isReviewConfirmed && !isSignatureConfirmed);
  const signatureStatus = useSignatureStatus(companyId, shouldPoll);

  // Extract progress data with debugging
  const currentProgress = signatureStatus.data;

  // Enhanced debugging with timestamp
  const timestamp = new Date().toLocaleTimeString();
  console.log(
    `[ReviewDocuments] ${timestamp} - Current progress: ${currentProgress}, shouldPoll: ${shouldPoll}, isPollingActive: ${isPollingActive}`
  );
  console.log(
    `[ReviewDocuments] ${timestamp} - Signature status:`,
    signatureStatus
  );

  // Force re-render when progress changes to ensure UI updates
  useEffect(() => {
    if (currentProgress !== undefined) {
      console.log(
        `[ReviewDocuments] Progress changed to: ${currentProgress}% - forcing UI update`
      );
    }
  }, [currentProgress]);

  // Helper function to get progress-based messages
  const getProgressMessage = (progress: number): string => {
    if (progress < 90) {
      return "Preparing documents for signature...";
    } else if (progress < 95) {
      return "Sending documents to signers...";
    } else if (progress < 100) {
      return "Finalizing review process...";
    } else {
      return "Document review process completed!";
    }
  };

  // Handle completion when polling reaches 100%
  useEffect(() => {
    if (currentProgress === 100 && currentStep === "processing") {
      console.log(
        `[ReviewDocuments] Process completed, stopping polling and refetching company details`
      );

      // Stop polling immediately
      setIsPollingActive(false);
      setCurrentStep("completed");

      // Refetch company details to update signature confirmation status
      refetchCompanyDetails()
        .then(() => {
          console.log(
            `[ReviewDocuments] Company details refetched successfully`
          );
          toast.success("Document review process completed successfully");

          // Navigate to dashboard after successful completion
          setTimeout(() => {
            setShowConfirmDialog(false);
            navigate("/dashboard");
          }, 3000);
        })
        .catch((error) => {
          console.error(
            `[ReviewDocuments] Failed to refetch company details:`,
            error
          );
          toast.success("Document review process completed successfully");

          // Still navigate even if refetch fails
          setTimeout(() => {
            setShowConfirmDialog(false);
            navigate("/dashboard");
          }, 3000);
        });
    }
  }, [currentProgress, currentStep, navigate, refetchCompanyDetails]);

  const handleDocumentOpen = (doc: Document, mode: "view" | "edit") => {
    setSelectedDocument(doc);
    setDocumentMode(mode);
    setIsModalOpen(true);
  };

  const handleConfirmReview = async () => {
    if (!companyId) {
      toast.error("Company ID is required");
      return;
    }

    if (!permissions.canConfirmReview) {
      toast.error("Only owners can confirm document review");
      return;
    }

    try {
      console.log(
        `[ReviewDocuments] Starting SQS workflow for company: ${companyId}`
      );

      // Trigger the SQS-based asynchronous workflow
      setCurrentStep("processing");
      const sqsResponse =
        await sendDocumentForSignatureSQS.mutateAsync(companyId);
      console.log(
        `[ReviewDocuments] SQS workflow triggered successfully:`,
        sqsResponse
      );

      // Start polling immediately after successful SQS trigger
      setIsPollingActive(true);
      console.log(
        `[ReviewDocuments] Polling activated, shouldPoll will be true`
      );

      toast.success("Document review process started successfully");
      await refetch();

      // The polling will handle the progress updates automatically
      // When signatureStatus reaches 100%, the process is complete
    } catch (error) {
      console.error("Error starting document review workflow:", error);
      toast.error("Failed to start document review process");
      setCurrentStep("idle");
      setIsPollingActive(false);
    }
  };

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Review Documents</h1>
          <p className="text-gray-600 mt-1">
            Review your company's legal documents and confirm your review
          </p>
        </div>

        {/* Show processing status or Confirm Review button */}
        <RoleBasedAccess requiredPermission="canConfirmReview">
          {!isReviewConfirmed ? (
            <div className="mb-6">
              <Button
                onClick={() => setShowConfirmDialog(true)}
                disabled={!documents?.length}
              >
                Confirm Review
              </Button>
            </div>
          ) : !isSignatureConfirmed && currentStep === "processing" ? (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-blue-500 animate-spin" />
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-medium text-blue-600">
                      {currentProgress !== undefined
                        ? getProgressMessage(currentProgress)
                        : "Initializing document review process..."}
                    </p>
                    {currentProgress !== undefined && (
                      <span className="text-lg font-bold text-blue-600 bg-white px-3 py-1 rounded-full shadow-sm">
                        {currentProgress}%
                      </span>
                    )}
                  </div>

                  {/* Progress Bar on Main Page */}
                  <div className="mt-2">
                    <div className="w-full bg-blue-100 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
                        style={{
                          width: `${currentProgress || 0}%`,
                          minWidth: currentProgress ? "4px" : "0px",
                        }}
                      />
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      {currentProgress !== undefined
                        ? `Processing... ${currentProgress}% complete`
                        : "Initializing process..."}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </RoleBasedAccess>

        {!isReviewConfirmed && (
          <DocumentsList
            documents={documents}
            isLoading={isLoading}
            error={error}
            handleDocumentOpen={handleDocumentOpen}
            userData={user}
            companyId={companyId}
            onReviewConfirmed={refetch}
          />
        )}

        {selectedDocument && (
          <DocumentModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            ffdocument={selectedDocument}
            mode={documentMode}
          />
        )}

        <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Complete Document Review Process</DialogTitle>
              <DialogDescription>
                This will prepare your documents for signature, send them to all
                signers, and complete the review process. This action cannot be
                undone.
              </DialogDescription>
            </DialogHeader>

            {/* Progress Steps */}
            <div className="space-y-4 py-4">
              {/* Processing Status */}
              <div className="flex items-center space-x-3">
                {currentStep === "processing" ? (
                  <Clock className="h-5 w-5 text-blue-500 animate-spin" />
                ) : currentStep === "completed" || currentProgress === 100 ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <FileText className="h-5 w-5 text-gray-400" />
                )}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <p
                      className={`text-sm font-medium ${
                        currentStep === "processing"
                          ? "text-blue-600"
                          : currentStep === "completed" ||
                              currentProgress === 100
                            ? "text-green-600"
                            : "text-gray-600"
                      }`}
                    >
                      {currentStep === "processing"
                        ? currentProgress !== undefined
                          ? getProgressMessage(currentProgress)
                          : "Initializing document review process..."
                        : currentStep === "completed" || currentProgress === 100
                          ? "Document review process completed!"
                          : "Ready to start document review process"}
                    </p>
                    {/* Progress Percentage - Prominently displayed */}
                    {currentStep === "processing" && (
                      <span className="text-lg font-bold text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                        {currentProgress ?? 0}%
                      </span>
                    )}
                  </div>

                  {/* Enhanced Progress Bar - Always visible during processing */}
                  {currentStep === "processing" && (
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out shadow-sm"
                          style={{
                            width: `${currentProgress || 0}%`,
                            minWidth: currentProgress ? "8px" : "0px", // Ensure visibility even at low percentages
                          }}
                        />
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <p className="text-xs text-gray-500">
                          {currentProgress !== undefined
                            ? `Processing... ${currentProgress}% complete`
                            : "Initializing process..."}
                        </p>
                        <p className="text-xs text-gray-400">
                          {currentProgress !== undefined &&
                          currentProgress < 100
                            ? "Please wait while we process your documents"
                            : ""}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Completion Message */}
              {(currentStep === "completed" || currentProgress === 100) && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg shadow-sm">
                  <div className="flex items-center">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-green-800">
                        Document review process completed successfully!
                      </p>
                      <p className="text-xs text-green-600 mt-1">
                        All documents have been prepared, sent for signature,
                        and the review is confirmed.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowConfirmDialog(false)}
                disabled={
                  sendDocumentForSignatureSQS.isPending ||
                  currentStep === "processing"
                }
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmReview}
                disabled={
                  sendDocumentForSignatureSQS.isPending ||
                  currentStep === "processing" ||
                  currentStep === "completed" ||
                  currentProgress === 100
                }
              >
                {sendDocumentForSignatureSQS.isPending ||
                currentStep === "processing"
                  ? "Processing..."
                  : "Start Process"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default ReviewDocuments;
