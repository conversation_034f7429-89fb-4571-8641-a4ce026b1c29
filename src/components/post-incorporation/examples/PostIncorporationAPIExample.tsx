import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import {
  useCompleteEIN,
  useCompleteBusinessAccount,
  useCompleteForeignQualification,
  useAddForeignQualificationState,
  useRemoveForeignQualificationState,
} from "@/integrations/legal-concierge/hooks";

/**
 * Example component demonstrating how to use the new PostIncorporation API hooks
 * This shows the integration patterns for the legal-concierge client
 */
const PostIncorporationAPIExample: React.FC = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  // State for foreign qualification states
  const [selectedStates, setSelectedStates] = useState<string[]>([]);
  const [newState, setNewState] = useState("");

  // PostIncorporation API hooks
  const completeEIN = useCompleteEIN();
  const completeBusinessAccount = useCompleteBusinessAccount();
  const completeForeignQualification = useCompleteForeignQualification();
  const addForeignQualificationState = useAddForeignQualificationState();
  const removeForeignQualificationState = useRemoveForeignQualificationState();

  // Handler for completing EIN task
  const handleCompleteEIN = async () => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      await completeEIN.mutateAsync(companyId);
      toast.success("EIN task completed successfully!");
    } catch (error) {
      toast.error("Failed to complete EIN task");
      console.error("Error completing EIN:", error);
    }
  };

  // Handler for completing business account task
  const handleCompleteBusinessAccount = async () => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      await completeBusinessAccount.mutateAsync(companyId);
      toast.success("Business account task completed successfully!");
    } catch (error) {
      toast.error("Failed to complete business account task");
      console.error("Error completing business account:", error);
    }
  };

  // Handler for completing foreign qualification task
  const handleCompleteForeignQualification = async () => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      await completeForeignQualification.mutateAsync(companyId);
      toast.success("Foreign qualification task completed successfully!");
    } catch (error) {
      toast.error("Failed to complete foreign qualification task");
      console.error("Error completing foreign qualification:", error);
    }
  };

  // Handler for adding a foreign qualification state
  const handleAddState = async () => {
    if (!companyId || !newState.trim()) {
      toast.error("Company ID or state not provided");
      return;
    }

    try {
      await addForeignQualificationState.mutateAsync({
        companyId,
        state: newState.trim(),
      });
      setSelectedStates([...selectedStates, newState.trim()]);
      setNewState("");
      toast.success(`Added ${newState} to foreign qualification states`);
    } catch (error) {
      toast.error("Failed to add foreign qualification state");
      console.error("Error adding state:", error);
    }
  };

  // Handler for removing a foreign qualification state
  const handleRemoveState = async (stateId: string, stateName: string) => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      await removeForeignQualificationState.mutateAsync({
        id: stateId,
        companyId,
      });
      setSelectedStates(selectedStates.filter((state) => state !== stateName));
      toast.success(`Removed ${stateName} from foreign qualification states`);
    } catch (error) {
      toast.error("Failed to remove foreign qualification state");
      console.error("Error removing state:", error);
    }
  };

  if (!companyId) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-gray-500">Please log in to access post-incorporation features.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>PostIncorporation API Integration Example</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* EIN Task */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Complete EIN Task</h3>
              <p className="text-sm text-gray-500">Mark EIN application as complete</p>
            </div>
            <Button
              onClick={handleCompleteEIN}
              disabled={completeEIN.isPending}
              variant="outline"
            >
              {completeEIN.isPending ? "Completing..." : "Complete EIN"}
            </Button>
          </div>

          {/* Business Account Task */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Complete Business Account Task</h3>
              <p className="text-sm text-gray-500">Mark business account setup as complete</p>
            </div>
            <Button
              onClick={handleCompleteBusinessAccount}
              disabled={completeBusinessAccount.isPending}
              variant="outline"
            >
              {completeBusinessAccount.isPending ? "Completing..." : "Complete Business Account"}
            </Button>
          </div>

          {/* Foreign Qualification Task */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Complete Foreign Qualification Task</h3>
              <p className="text-sm text-gray-500">Mark foreign qualification as complete</p>
            </div>
            <Button
              onClick={handleCompleteForeignQualification}
              disabled={completeForeignQualification.isPending}
              variant="outline"
            >
              {completeForeignQualification.isPending ? "Completing..." : "Complete Foreign Qualification"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Foreign Qualification States Management */}
      <Card>
        <CardHeader>
          <CardTitle>Foreign Qualification States</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add State */}
          <div className="flex gap-2">
            <input
              type="text"
              value={newState}
              onChange={(e) => setNewState(e.target.value)}
              placeholder="Enter state name"
              className="flex-1 px-3 py-2 border rounded-md"
            />
            <Button
              onClick={handleAddState}
              disabled={addForeignQualificationState.isPending || !newState.trim()}
            >
              {addForeignQualificationState.isPending ? "Adding..." : "Add State"}
            </Button>
          </div>

          {/* Selected States List */}
          {selectedStates.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Selected States:</h4>
              {selectedStates.map((state, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span>{state}</span>
                  <Button
                    onClick={() => handleRemoveState(`state-${index}`, state)}
                    disabled={removeForeignQualificationState.isPending}
                    variant="destructive"
                    size="sm"
                  >
                    Remove
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PostIncorporationAPIExample;
