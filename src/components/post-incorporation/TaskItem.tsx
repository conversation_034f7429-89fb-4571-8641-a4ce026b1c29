import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, ExternalLink, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Task } from "./types";

interface TaskItemProps {
  task: Task;
  onComplete: (id: string) => Promise<void>;
  renderDialogContent?: (task: Task) => React.ReactNode;
}

const TaskItem: React.FC<TaskItemProps> = ({
  task,
  onComplete,
  renderDialogContent,
}) => {
  const [isCompleting, setIsCompleting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleComplete = async () => {
    setIsCompleting(true);
    try {
      await onComplete(task.id);
      // Close the dialog after successful completion
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error completing task:", error);
    } finally {
      setIsCompleting(false);
    }
  };

  return (
    <li className="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 transition-colors">
      <div className="flex items-center">
        {task.isComplete ? (
          <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
        ) : (
          <div className="h-5 w-5 rounded-full border border-gray-300 mr-3" />
        )}
        <div>
          <h3 className="font-medium text-gray-900">{task.title}</h3>
          <p className="text-sm text-gray-500">{task.description}</p>
        </div>
      </div>
      <div>
        {!task.isComplete ? (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                Start
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{task.title}</DialogTitle>
              </DialogHeader>
              <DialogDescription>
                {task.dialogContent}
                {renderDialogContent && renderDialogContent(task)}
              </DialogDescription>
              <DialogFooter className="flex justify-between items-center mt-4">
                <a
                  href={task.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex"
                >
                  <Button variant="secondary" size="sm">
                    Visit Website <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </a>
                <Button onClick={handleComplete} disabled={isCompleting}>
                  {isCompleting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Completing...
                    </>
                  ) : (
                    "Mark Complete"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        ) : (
          <a href={task.link} target="_blank" rel="noopener noreferrer">
            <Button variant="secondary" size="sm">
              Learn More <ExternalLink className="h-4 w-4 ml-2" />
            </Button>
          </a>
        )}
      </div>
    </li>
  );
};

export default TaskItem;
