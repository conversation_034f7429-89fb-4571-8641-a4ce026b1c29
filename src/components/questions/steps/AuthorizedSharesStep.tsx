import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { QuestionnaireFormData } from "../types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useUpdateAuthorizedShares } from "@/integrations/legal-concierge/hooks/useCompanyInfo";
import { toast } from "sonner";

interface AuthorizedSharesStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void;
}

// Export the save function so it can be called from the continue button
export const saveAuthorizedSharesData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  updateAuthorizedShares: any
): Promise<boolean> => {
  try {
    const payload = {
      id: companyId,
      companyId: companyId,
      numberOfAuthorizedShares: formData.authorizedShares,
      numberOfAuthorizedSharesToBeIssuedInitially: formData.issuedShares,
      parValuePerShare: formData.parValuePerShare,
    };

    console.log("Saving authorized shares with payload:", payload); // Debug log

    await updateAuthorizedShares.mutateAsync(payload);

    console.log("Authorized shares saved successfully"); // Debug log
    toast.success("Authorized shares information saved successfully");
    return true;
  } catch (error) {
    console.error("Error saving authorized shares:", error);
    toast.error("Failed to save authorized shares information");
    return false;
  }
};

const AuthorizedSharesStep: React.FC<AuthorizedSharesStepProps> = ({
  formData,
  updateFormData,
}) => {
  const [sharesError, setSharesError] = useState<string | null>(null);
  const [parValueInput, setParValueInput] = useState<string>(""); // new

  // Initialize parValueInput with existing form data
  useEffect(() => {
    console.log(
      "Initializing parValueInput with formData.parValuePerShare:",
      formData.parValuePerShare
    ); // Debug log

    if (formData.parValuePerShare && formData.parValuePerShare > 0) {
      // Format the number properly for display
      const value = formData.parValuePerShare;
      if (value >= 1) {
        // For values >= 1, use comma formatting
        const [intPart, decimalPart] = value.toString().split(".");
        const formattedInt = Number(intPart).toLocaleString("en-US");
        const formatted =
          decimalPart !== undefined
            ? `${formattedInt}.${decimalPart}`
            : formattedInt;
        setParValueInput(formatted);
        console.log("Set parValueInput to (formatted):", formatted); // Debug log
      } else {
        // For values < 1, show as-is to preserve decimal precision
        const valueStr = value.toString();
        setParValueInput(valueStr);
        console.log("Set parValueInput to (decimal):", valueStr); // Debug log
      }
    } else if (formData.parValuePerShare === 0) {
      setParValueInput("");
      console.log("Set parValueInput to empty (value is 0)"); // Debug log
    }
  }, [formData.parValuePerShare]);

  // Check for shares error when form data changes
  useEffect(() => {
    if (formData.issuedShares > formData.authorizedShares) {
      setSharesError(
        "The number of shares to be issued initially cannot exceed the number of authorized shares"
      );
    } else {
      setSharesError(null);
    }
  }, [formData.authorizedShares, formData.issuedShares]);

  const handleNumberInput = (
    field: "authorizedShares" | "issuedShares" | "parValuePerShare",
    value: string
  ) => {
    // Remove commas before converting to number
    const numericValue = value.replace(/,/g, "");
    const numValue = numericValue === "" ? 0 : Number(numericValue);
    updateFormData(field, numValue);
  };

  const handleDecimalInput = (value: string) => {
    // Handle empty input
    if (value === "") {
      setParValueInput("");
      updateFormData("parValuePerShare", 0);
      return;
    }

    // Allow dot (in-progress input)
    if (value === ".") {
      setParValueInput(value);
      return;
    }

    // Remove commas for validation
    const normalized = value.replace(/,/g, "");

    // Check if it's a valid decimal (with up to 5 digits after dot)
    const decimalRegex = /^-?\d*(\.\d{0,5})?$/;
    if (!decimalRegex.test(normalized)) {
      return; // Block invalid input
    }

    // Format with commas (keep decimal part intact)
    const [intPart, decimalPart] = normalized.split(".");
    const formattedInt = Number(intPart).toLocaleString("en-US");
    const formatted =
      decimalPart !== undefined
        ? `${formattedInt}.${decimalPart}`
        : formattedInt;

    // Update raw input
    setParValueInput(formatted);

    // Update numeric form data
    const numValue = Number(normalized);
    if (!isNaN(numValue)) {
      updateFormData("parValuePerShare", numValue);
      console.log("Updated parValuePerShare to:", numValue); // Debug log
    }
  };

  // Format number with comma separators
  const formatNumber = (num: number | undefined): string => {
    if (num === undefined || num === 0 || isNaN(num)) return "";
    return num.toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="authorized-shares" className="text-base font-medium">
          Number of Authorized Shares
        </Label>
        <p className="text-sm text-gray-500">
          The total number of shares the company is authorized to issue. This
          number can be higher than what you initially issue.
        </p>
        <Input
          id="authorized-shares"
          type="text"
          value={formatNumber(formData.authorizedShares)}
          onChange={(e) =>
            handleNumberInput("authorizedShares", e.target.value)
          }
          placeholder="10,000,000"
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="issued-shares" className="text-base font-medium">
          {formData.registerMode == "getting-started"
            ? "Number of Shares to be Issued Initially"
            : "Number of Shares Issued Initially"}
        </Label>
        <p className="text-sm text-gray-500">
          {formData.registerMode == "getting-started"
            ? "The number of shares that will be immediately issued to founders and initial investors. This number must be less than or equal to the authorized shares."
            : "The number of shares that was immediately issued to founders and initial investors. This number must be less than or equal to the authorized shares."}
        </p>
        <Input
          id="issued-shares"
          type="text"
          value={formatNumber(formData.issuedShares)}
          onChange={(e) => handleNumberInput("issuedShares", e.target.value)}
          placeholder="Enter number of shares"
          className={`w-full ${sharesError ? "border-red-500 focus-visible:ring-red-500" : ""}`}
        />

        {sharesError && (
          <Alert variant="destructive" className="mt-2 py-2">
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <AlertDescription className="ml-2 text-sm font-medium">
                {sharesError}
              </AlertDescription>
            </div>
          </Alert>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="par-value" className="text-base font-medium">
          Par Value per Share
        </Label>
        <p className="text-sm text-gray-500">
          The nominal value of each share. This is often set very low (e.g.,
          $0.00001) for startups. The par value sets the minimum price at which
          shares can be sold.
        </p>
        <div className="relative">
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
            $
          </span>
          <Input
            id="par-value"
            type="text"
            value={parValueInput}
            onChange={(e) => handleDecimalInput(e.target.value)}
            placeholder="0.00001"
            className="w-full pl-7"
          />
        </div>
      </div>
    </div>
  );
};

export default AuthorizedSharesStep;
