import React from "react";
import { Card, CardContent } from "@/components/common/Card";
import { LucideIcon } from "lucide-react";

interface TimelineItemProps {
  index: number;
  title: string;
  description: string;
  icon: LucideIcon;
}

const TimelineItem: React.FC<TimelineItemProps> = ({
  index,
  title,
  description,
  icon: Icon,
}) => {
  return (
    <div className="relative flex flex-col md:flex-row items-start">
      <div
        className={`flex items-center justify-center w-8 h-8 rounded-full bg-legal-50 border-4 border-white text-legal-600 z-10 ${
          index % 2 === 0
            ? "md:order-1 md:ml-8"
            : "md:order-1 md:ml-8 md:order-none md:mr-8 md:ml-0"
        }`}
      >
        <span className="text-sm font-bold">{index + 1}</span>
      </div>

      <div
        className={`flex-grow ml-12 md:ml-0 ${
          index % 2 === 0 ? "md:text-right md:pr-16" : "md:pl-16"
        }`}
      >
        <Card className="mt-4 md:mt-0">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 bg-legal-100 text-legal-600 rounded-lg flex items-center justify-center mr-4 md:order-last md:ml-4 md:mr-0">
                <Icon size={20} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
            </div>
            <p className="text-gray-600">{description}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TimelineItem;
