import React from "react";
import { Calendar, Check, X } from "lucide-react";
import { format } from "date-fns";
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Director } from "./types";

interface DirectorRowProps {
  director: Director;
  editingId: string | null;
  onEditClick: (director: Director) => void;
  onSaveClick: (id: string) => void;
  onDateChange: (id: string, date: Date | undefined) => void;
  onResignationDateChange: (id: string, date: Date | undefined) => void;
  onResignedChange: (id: string) => void;
  onStockholderApprovalChange?: (id: string) => void;
}

const DirectorRow: React.FC<DirectorRowProps> = ({
  director,
  editingId,
  onEditClick,
  onSaveClick,
  onDateChange,
  onResignationDateChange,
  onResignedChange,
  onStockholderApprovalChange,
}) => {
  const isEditing = editingId === director.id;

  return (
    <TableRow>
      <TableCell>{director.name}</TableCell>
      <TableCell>
        {isEditing ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                {director.appointmentDate
                  ? format(director.appointmentDate, "PPP")
                  : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent
                mode="single"
                selected={director.appointmentDate}
                onSelect={(date) => onDateChange(director.id, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : (
          format(director.appointmentDate, "PPP")
        )}
      </TableCell>
      <TableCell>
        <Switch
          checked={director.resigned}
          onCheckedChange={() => onResignedChange(director.id)}
          disabled={editingId !== director.id}
        />
      </TableCell>
      <TableCell>
        {isEditing ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={!director.resigned}
                className={!director.resigned ? "opacity-50" : ""}
              >
                <Calendar className="h-4 w-4 mr-2" />
                {director.resignationDate
                  ? format(director.resignationDate, "PPP")
                  : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent
                mode="single"
                selected={director.resignationDate || undefined}
                onSelect={(date) => onResignationDateChange(director.id, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : (
          <span className={!director.resigned ? "text-gray-400" : ""}>
            {director.resignationDate
              ? format(director.resignationDate, "PPP")
              : "N/A"}
          </span>
        )}
      </TableCell>
      <TableCell>
        {isEditing && onStockholderApprovalChange ? (
          <Switch
            checked={director.stockholderApproval}
            onCheckedChange={() => onStockholderApprovalChange(director.id)}
          />
        ) : director.stockholderApproval ? (
          <Check className="h-5 w-5 text-green-600" />
        ) : (
          <X className="h-5 w-5 text-red-600" />
        )}
      </TableCell>
      <TableCell>
        {isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onSaveClick(director.id)}
          >
            Save
          </Button>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEditClick(director)}
          >
            Edit
          </Button>
        )}
      </TableCell>
    </TableRow>
  );
};

export default DirectorRow;
