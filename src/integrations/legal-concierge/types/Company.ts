import { RegisteredAgent } from "./RegisteredAgent";

export interface AuthorizedSharesRequest {
  companyId: string | null;
  numberOfAuthorizedShares: number | null;
  numberOfAuthorizedSharesToBeIssuedInitially: number | null;
  parValuePerShare: number | null;
}

export interface CompanyNameRequest {
  companyId: string | null;
  companyName: string | null;
  isIncorporatedToday: boolean | null;
  incorporationDate: string | null; // ISO date string
}

export interface CompanyAddressRequest {
  companyId: string | null;
  streetAddress: string | null;
  streetAddress2: string | null;
  city: string | null;
  state: string | null;
  zipCode: string | null;
}

export interface FormState {
  id: string;
  formState: string;
}

export interface IntellectualProperty {
  id: string;
  officerId: string;
  name: string;
  isExcluded: boolean;
}

export interface Officer {
  id: string;
  companyId: string;
  name: string;
  emailAddress: string;
  contactAddress: string;
  stockOwnership: number;
  amountOfShares: number;
  isDirector: boolean;
  isCEO: boolean;
  isPresident: boolean;
  isSecretary: boolean;
  isTreasurer: boolean;
  notApplicable: boolean;
  isVester: boolean;
  intellectualProperties: IntellectualProperty[];
  vestingSchedule: string;
  accleration: string;
}

export interface CompanyDetails {
  id: string;
  registerMode: string;
  userId: string;
  agentId: string;
  formStateId: string;
  name: string;
  isIncorporatedToday: boolean;
  incorporationDate: string;
  streetAddress: string;
  streetAddress2: string;
  city: string;
  state: string;
  zipCode: string;
  numberOfAuthorizedShares: number;
  numberOfAuthorizedSharesToBeIssuedInitially: number;
  parValuePerShare: number;
  includeStockOptionPlan: boolean;
  stockOptionPlanPercentage: number;
  isVestingConfirmed: boolean;
  technologyDescription: string;
  registeredAgent: RegisteredAgent;
  formState: FormState;
  isFormConfirmed: boolean;
  isReviewConfirmed: boolean;
  isSignatureConfirmed: boolean;
  isSignatureComplete: boolean;
  isPostIncorporationConfirmed: boolean;
  isEinApplyComplete: boolean;
  isOpeningBankAccountComplete: boolean;
  isForeignQualificationToDoBusinessComplete: boolean;
  officers: Officer[];
}
