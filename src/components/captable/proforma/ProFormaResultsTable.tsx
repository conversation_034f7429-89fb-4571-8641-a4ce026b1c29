import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ProFormaMetrics {
  amount: number;
  preMoney: number;
  postMoney: number;
  newShares: number;
  newPercentage: number;
  dilutedPoolPercentage: number;
}

interface ProFormaResultsTableProps {
  proFormaMetrics: ProFormaMetrics;
  formatCurrency: (amount: number) => string;
  isAmountEdited?: boolean;
  isUserEdited?: boolean;
  totalShares?: number;
  pricePerShare?: number;
  dilutedPoolUserDefined?: boolean;
}

const ProFormaResultsTable: React.FC<ProFormaResultsTableProps> = ({
  proFormaMetrics,
  formatCurrency,
  isAmountEdited,
  isUserEdited,
  totalShares,
  pricePerShare,
  dilutedPoolUserDefined,
}) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Metric</TableHead>
          <TableHead>Value</TableHead>
          <TableHead>Notes</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell className="font-medium">Investment Amount</TableCell>
          <TableCell>{formatCurrency(proFormaMetrics.amount)}</TableCell>
          <TableCell className="text-sm text-gray-500">
            {isAmountEdited
              ? "User-defined value"
              : "Original investment amount"}
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">
            Effective Pre-Money Valuation
          </TableCell>
          <TableCell>{formatCurrency(proFormaMetrics.preMoney)}</TableCell>
          <TableCell className="text-sm text-gray-500">
            {isUserEdited
              ? "User-defined value"
              : "Effective Pre-Money Valuation calculated based on conversion of the convertibles and stock plan increase on a pre-money basis"}
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Post-Money Valuation</TableCell>
          <TableCell>{formatCurrency(proFormaMetrics.postMoney)}</TableCell>
          <TableCell className="text-sm text-gray-500">
            {isUserEdited
              ? "User-defined value"
              : "Pre-money valuation + Investment amount"}
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">New Shares Issued</TableCell>
          <TableCell>{proFormaMetrics.newShares.toLocaleString()}</TableCell>
          <TableCell className="text-sm text-gray-500">
            Investment amount ÷ Price per share
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Investor Ownership %</TableCell>
          <TableCell>{proFormaMetrics.newPercentage.toFixed(2)}%</TableCell>
          <TableCell className="text-sm text-gray-500">
            New shares ÷ (Current shares + New shares)
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Diluted Option Pool %</TableCell>
          <TableCell>
            {proFormaMetrics.dilutedPoolPercentage.toFixed(2)}%
          </TableCell>
          <TableCell className="text-sm text-gray-500">
            {dilutedPoolUserDefined
              ? "User-defined value"
              : "Option pool size after dilution from new investment"}
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
};

export default ProFormaResultsTable;
