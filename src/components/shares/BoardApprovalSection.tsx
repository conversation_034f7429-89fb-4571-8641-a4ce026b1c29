import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>ead<PERSON>,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Scroll, ScrollBar } from "@/components/ui/scroll-area";
import SignatureModal from "@/components/signature/SignatureModal";

interface BoardApprovalSectionProps {
  currentShares: number;
  newTotalShares: number;
  onComplete: () => void;
  onCancel: () => void;
}

const BoardApprovalSection: React.FC<BoardApprovalSectionProps> = ({
  currentShares,
  newTotalShares,
  onComplete,
  onCancel,
}) => {
  const [isSignatureModalOpen, setIsSignatureModalOpen] = useState(false);
  const [hasAdminSigned, setHasAdminSigned] = useState(false);

  const handleAdminSignatureComplete = () => {
    setHasAdminSigned(true);
    setIsSignatureModalOpen(false);
  };

  const handleFinish = () => {
    // In a real app, this would send emails to directors
    onComplete();
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>Board Approval</DialogTitle>
        <DialogDescription>
          Review and sign the Certificate of Amendment and Board Consent
        </DialogDescription>
      </DialogHeader>

      <div className="my-4 space-y-4">
        <div className="border rounded-md p-4">
          <h3 className="font-semibold mb-2">Certificate of Amendment</h3>
          <Scroll className="h-32 overflow-auto">
            <div className="p-2 text-sm">
              <p>CERTIFICATE OF AMENDMENT TO THE</p>
              <p>CERTIFICATE OF INCORPORATION OF</p>
              <p>[COMPANY NAME], INC.</p>
              <br />
              <p>
                [COMPANY NAME], INC., a corporation organized and existing under
                and by virtue of the General Corporation Law of the State of
                Delaware (the "Company"), does hereby certify that:
              </p>
              <br />
              <p>
                1. The Board of Directors of the Company has duly adopted
                resolutions proposing to amend the Company's Certificate of
                Incorporation to increase the Company's authorized share capital
                from {currentShares.toLocaleString()} shares of Common Stock to{" "}
                {newTotalShares.toLocaleString()} shares of Common Stock.
              </p>
              <br />
              <p>
                2. This Certificate of Amendment has been duly approved by the
                required vote of stockholders in accordance with Section 242 of
                the Delaware General Corporation Law.
              </p>
              <br />
              <p>
                IN WITNESS WHEREOF, the Company has caused this Certificate of
                Amendment to be signed by a duly authorized officer of the
                Company on [DATE].
              </p>
            </div>
            <ScrollBar />
          </Scroll>
        </div>

        <div className="border rounded-md p-4">
          <h3 className="font-semibold mb-2">Board Consent</h3>
          <Scroll className="h-32 overflow-auto">
            <div className="p-2 text-sm">
              <p>ACTION BY UNANIMOUS WRITTEN CONSENT</p>
              <p>OF THE BOARD OF DIRECTORS OF</p>
              <p>[COMPANY NAME], INC.</p>
              <br />
              <p>
                The undersigned, constituting all of the members of the Board of
                Directors of [COMPANY NAME], INC., a Delaware corporation (the
                "Company"), hereby adopt the following resolutions by unanimous
                written consent:
              </p>
              <br />
              <p>
                RESOLVED, that the Certificate of Amendment to the Certificate
                of Incorporation of the Company, in the form attached hereto as
                Exhibit A, is hereby approved and adopted; and
              </p>
              <br />
              <p>
                RESOLVED FURTHER, that the officers of the Company are
                authorized and directed to execute and deliver the Certificate
                of Amendment to the Certificate of Incorporation and to take all
                such actions as they deem necessary or appropriate to effectuate
                the foregoing resolutions.
              </p>
              <br />
              <p>
                This action by written consent shall be effective as of the date
                the last director signs below.
              </p>
            </div>
            <ScrollBar />
          </Scroll>
        </div>

        <div className="pt-2 text-center">
          <Button
            onClick={() => setIsSignatureModalOpen(true)}
            disabled={hasAdminSigned}
            className="mx-auto"
          >
            {hasAdminSigned ? "Signed" : "Sign Documents"}
          </Button>
        </div>
      </div>

      <DialogFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleFinish} disabled={!hasAdminSigned}>
          Finish
        </Button>
      </DialogFooter>

      <SignatureModal
        isOpen={isSignatureModalOpen}
        onClose={() => setIsSignatureModalOpen(false)}
        documentName="Board Consent and Certificate of Amendment"
        onComplete={handleAdminSignatureComplete}
      />
    </>
  );
};

export default BoardApprovalSection;
