import { APIClient } from "@/integrations/legal-concierge/client";

const api = new APIClient();

export interface UploadUrlResponse {
  uploadUrl: string;
  key: string;
}

export interface DocumentMetadata {
  name: string;
  key: string;
  size: number;
  type: string;
  uploadedAt: string;
  url?: string;
}

export const getPresignedUploadUrl = async (
  companyId: string,
  folderPath: string,
  fileName: string,
  fileType: string
): Promise<UploadUrlResponse> => {
  const response = await api.getDocumentUploadUrl(companyId, {
    key: 'incorporation-documents' + '/' + folderPath + '/' + fileName,
    contentType: fileType,
  });
  console.log('the response', response);
  if ('error' in response) {
    throw new Error(response.error);
  }
  
  return response.data;
};

export const uploadFileToPresignedUrl = async (
  presignedUrl: string,
  file: File
): Promise<void> => {
  await fetch(presignedUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type,
    },
  });
};

// export const saveDocumentMetadata = async (
//   companyId: string,
//   documentMetadata: Omit<DocumentMetadata, 'uploadedAt' | 'url'>
// ): Promise<DocumentMetadata> => {
//   const response = await api.saveDocumentMetadata(companyId, documentMetadata);
//   return response;
// }; 