import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, PlusCircle } from "lucide-react";
import AnimatedTransition from "@/components/common/AnimatedTransition";

interface CapTableEmptyStateProps {
  onImportClick: () => void;
  onCreateNewClick: () => void;
}

const CapTableEmptyState: React.FC<CapTableEmptyStateProps> = ({
  onImportClick,
  onCreateNewClick,
}) => {
  return (
    <AnimatedTransition>
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-4">
          No Cap Table Data Available
        </h2>
        <p className="text-gray-600 mb-6">
          You don't have any cap tables yet. Import a CSV file or create a new
          cap table to get started.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button
            variant="outline"
            onClick={onImportClick}
            className="flex items-center justify-center gap-2"
          >
            <FileText size={16} />
            Import Data
          </Button>
          <Button
            onClick={onCreateNewClick}
            className="flex items-center justify-center gap-2"
          >
            <PlusCircle size={16} />
            Create New Cap Table
          </Button>
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default CapTableEmptyState;
