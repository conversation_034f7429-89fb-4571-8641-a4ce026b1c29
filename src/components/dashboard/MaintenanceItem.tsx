import React from "react";
import { Link } from "react-router-dom";
import { LucideIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface MaintenanceItemProps {
  icon: LucideIcon;
  title: string;
  description: string;
  buttonText: string;
  onClick?: () => void;
  linkTo?: string;
}

const MaintenanceItem: React.FC<MaintenanceItemProps> = ({
  icon: Icon,
  title,
  description,
  buttonText,
  onClick,
  linkTo,
}) => {
  const renderActionButton = () => {
    const buttonClasses = "flex-shrink-0 mt-1";

    return linkTo ? (
      <Button size="sm" variant="outline" className={buttonClasses} asChild>
        <Link to={linkTo}>{buttonText}</Link>
      </Button>
    ) : (
      <Button
        size="sm"
        variant="outline"
        className={buttonClasses}
        onClick={onClick}
      >
        {buttonText}
      </Button>
    );
  };

  return (
    <div className="flex items-start p-4 rounded-lg hover:bg-gray-50 transition-colors">
      <div
        className={cn(
          "flex-shrink-0 bg-amber-100 rounded-full h-10 w-10",
          "flex items-center justify-center mr-4"
        )}
      >
        <Icon className="h-5 w-5 text-amber-600" />
      </div>

      <div className="flex-1">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <p className="text-gray-600 text-sm mt-1">{description}</p>
      </div>

      {renderActionButton()}
    </div>
  );
};

export default MaintenanceItem;
