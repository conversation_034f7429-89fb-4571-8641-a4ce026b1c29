import { useMutation, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { StockOptionPlanRequest, VestingRequest } from "../types/Stocks";
import { QueryKey } from "../keys";

const api = new APIClient();

// Stock and vesting hooks
export const useUpdateStockOptionPlan = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      ...payload
    }: { id: string } & StockOptionPlanRequest) => {
      // Format the payload according to the new API requirements
      const formattedPayload = {
        ...payload,
        // Only include officersOwnership if it's provided
        ...(payload.officersOwnership && {
          officersOwnership: payload.officersOwnership.map((officer) => ({
            officerId: officer.officerId,
            ownershipPercentage: officer.ownershipPercentage,
            amountOfShares: officer.amountOfShares,
          })),
        }),
      };
      return api.updateStockOptionPlan(id, formattedPayload);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.companyId],
      });
    },
  });
};

export const useUpdateVesting = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, ...payload }: { id: string } & VestingRequest) =>
      api.updateVesting(id, payload),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.id],
      });
    },
  });
};
