import React from "react";
import ProjectsCard from "./ProjectsCard";
import DocumentsCard from "./DocumentsCard";
import TasksCard from "./TasksCard";

interface DashboardCardsProps {
  documentsSignedStatus: boolean;
}

const DashboardCards: React.FC<DashboardCardsProps> = ({
  documentsSignedStatus,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <ProjectsCard />
      <DocumentsCard />
      <TasksCard documentsSignedStatus={documentsSignedStatus} />
    </div>
  );
};

export default DashboardCards;
