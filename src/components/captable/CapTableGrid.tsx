import React from "react";
import { Shareholder, ShareClass, ShareClassDetails } from "@/types/capTable";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";

interface CapTableGridProps {
  shareholders: Shareholder[];
  shareClasses: ShareClassDetails[];
  currentView: "standard" | "fullyDiluted";
  totalShares: number;
  totalFullyDiluted: number;
}

// Define a type for the table columns that includes the optional tooltip property
interface ColumnDefinition {
  key: string;
  label: string;
  tooltip?: string;
}

const CapTableGrid: React.FC<CapTableGridProps> = ({
  shareholders,
  shareClasses,
  currentView,
  totalShares,
  totalFullyDiluted,
}) => {
  // Helper function to format numbers
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // Get the right columns based on current view
  const getColumns = (): ColumnDefinition[] => {
    const baseColumns: ColumnDefinition[] = [
      { key: "name", label: "Name" },
      { key: "role", label: "Role" },
    ];

    // Add columns for each share class
    const shareClassColumns: ColumnDefinition[] = shareClasses.map((sc) => ({
      key: sc.name,
      label: sc.name,
      tooltip: `${sc.type} shares${sc.liquidationPreference ? ` with ${sc.liquidationPreference}x liquidation preference` : ""}`,
    }));

    // Add totals and percentages
    const totalColumns: ColumnDefinition[] =
      currentView === "standard"
        ? [
            {
              key: "totalShares",
              label: "Total Shares",
              tooltip: "Sum of all shares held",
            },
            {
              key: "percentage",
              label: "Ownership %",
              tooltip: "Percentage of the total outstanding shares",
            },
          ]
        : [
            {
              key: "totalShares",
              label: "Total Shares",
              tooltip: "Sum of all shares held",
            },
            {
              key: "percentage",
              label: "Ownership %",
              tooltip: "Percentage of the total outstanding shares",
            },
            {
              key: "fullyDilutedPercentage",
              label: "Fully Diluted %",
              tooltip:
                "Percentage ownership if all options and convertible securities are exercised",
            },
          ];

    return [...baseColumns, ...shareClassColumns, ...totalColumns];
  };

  const columns = getColumns();

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Cap Table</CardTitle>
          <div className="text-sm text-gray-500">
            Total Shares:{" "}
            {formatNumber(
              currentView === "standard" ? totalShares : totalFullyDiluted
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column.key}>
                    <div className="flex items-center gap-1">
                      {column.label}
                      {column.tooltip && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <button className="text-gray-400 hover:text-gray-600">
                                <Info size={14} />
                              </button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{column.tooltip}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {shareholders.map((shareholder) => (
                <TableRow key={shareholder.id}>
                  <TableCell className="font-medium">
                    {shareholder.name}
                  </TableCell>
                  <TableCell>{shareholder.role}</TableCell>

                  {/* Share class cells */}
                  {shareClasses.map((sc) => (
                    <TableCell key={sc.name}>
                      {shareholder.holdings[sc.name]
                        ? formatNumber(shareholder.holdings[sc.name] || 0)
                        : "-"}
                    </TableCell>
                  ))}

                  {/* Total shares */}
                  <TableCell className="font-medium">
                    {formatNumber(shareholder.totalShares)}
                  </TableCell>

                  {/* Ownership percentage */}
                  <TableCell className="font-medium">
                    {shareholder.percentage.toFixed(2)}%
                  </TableCell>

                  {/* Fully diluted percentage (only in fully diluted view) */}
                  {currentView === "fullyDiluted" && (
                    <TableCell className="font-medium">
                      {shareholder.fullyDilutedPercentage.toFixed(2)}%
                    </TableCell>
                  )}
                </TableRow>
              ))}

              {/* Totals row */}
              <TableRow className="bg-muted/50">
                <TableCell colSpan={2} className="font-medium">
                  Total
                </TableCell>

                {/* Total shares per class */}
                {shareClasses.map((sc) => (
                  <TableCell key={`total-${sc.name}`} className="font-medium">
                    {formatNumber(
                      shareholders.reduce(
                        (sum, s) => sum + (s.holdings[sc.name] || 0),
                        0
                      )
                    )}
                  </TableCell>
                ))}

                {/* Grand total shares */}
                <TableCell className="font-medium">
                  {formatNumber(
                    currentView === "standard" ? totalShares : totalFullyDiluted
                  )}
                </TableCell>

                {/* Should always add up to 100% */}
                <TableCell className="font-medium">100.00%</TableCell>

                {/* Fully diluted also 100% */}
                {currentView === "fullyDiluted" && (
                  <TableCell className="font-medium">100.00%</TableCell>
                )}
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default CapTableGrid;
