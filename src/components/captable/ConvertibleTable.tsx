import React, { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { differenceInMonths } from "date-fns";
import { CapTableSummary, ConvertibleNote } from "@/types/capTable";

interface ConvertibleTableProps {
  convertibleNotes: ConvertibleNote[];
  capTableData: CapTableSummary;
}

const ConvertibleTable: React.FC<ConvertibleTableProps> = ({
  convertibleNotes,
  capTableData,
}) => {
  // Compute notes with additional metrics
  const notesWithMetrics = useMemo(() => {
    if (!convertibleNotes.length) return [];

    return convertibleNotes.map((note) => {
      // Calculate interest accrued
      const monthsPassed = differenceInMonths(new Date(), note.issueDate);
      const interestAccrued =
        note.principal * (note.interestRate / 100) * (monthsPassed / 12);
      const totalWithInterest = note.principal + interestAccrued;

      // Calculate conversion price
      let conversionPrice = 0;
      if (note.conversionPrice) {
        conversionPrice = note.conversionPrice;
      } else {
        // Assume a default price or calculate based on the latest valuation
        const defaultPrice = 1; // This would be updated based on your logic
        const discountPrice = note.discount
          ? defaultPrice * (1 - note.discount / 100)
          : defaultPrice;
        const capPrice = note.valuationCap
          ? note.valuationCap / capTableData.totalShares
          : Infinity;

        // Use the lower of discount price or cap price
        conversionPrice = Math.min(discountPrice, capPrice);
      }

      // Calculate estimated shares
      const estimatedShares =
        conversionPrice > 0 ? totalWithInterest / conversionPrice : 0;

      return {
        ...note,
        interestAccrued,
        totalWithInterest,
        conversionPrice,
        estimatedShares,
      };
    });
  }, [convertibleNotes, capTableData]);

  // Calculate totals
  const totals = useMemo(() => {
    return notesWithMetrics.reduce(
      (acc, note) => {
        return {
          principal: acc.principal + note.principal,
          interestAccrued: acc.interestAccrued + note.interestAccrued,
          totalWithInterest: acc.totalWithInterest + note.totalWithInterest,
          estimatedShares: acc.estimatedShares + note.estimatedShares,
        };
      },
      {
        principal: 0,
        interestAccrued: 0,
        totalWithInterest: 0,
        estimatedShares: 0,
      }
    );
  }, [notesWithMetrics]);

  // Helper function to format currencies
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (!convertibleNotes.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Convertible Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">
            No convertible notes available. Add convertible notes to see
            conversion metrics.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Convertible Notes</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="text-gray-400 hover:text-gray-600">
                  <Info size={16} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  Convertible notes are typically converted to equity at a
                  qualified financing event. This table shows the estimated
                  conversion metrics.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Investor</TableHead>
                <TableHead>Principal</TableHead>
                <TableHead>Interest Rate</TableHead>
                <TableHead>Issue Date</TableHead>
                <TableHead>Maturity</TableHead>
                <TableHead>Valuation Cap</TableHead>
                <TableHead>Discount</TableHead>
                <TableHead>Interest Accrued</TableHead>
                <TableHead>Total Value</TableHead>
                <TableHead>Conv. Price</TableHead>
                <TableHead>Est. Shares</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {notesWithMetrics.map((note) => (
                <TableRow key={note.id}>
                  <TableCell className="font-medium">{note.investor}</TableCell>
                  <TableCell>{formatCurrency(note.principal)}</TableCell>
                  <TableCell>{note.interestRate}%</TableCell>
                  <TableCell>{note.issueDate.toLocaleDateString()}</TableCell>
                  <TableCell>
                    {note.maturityDate.toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {note.valuationCap
                      ? formatCurrency(note.valuationCap)
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    {note.discount ? `${note.discount}%` : "N/A"}
                  </TableCell>
                  <TableCell>{formatCurrency(note.interestAccrued)}</TableCell>
                  <TableCell>
                    {formatCurrency(note.totalWithInterest)}
                  </TableCell>
                  <TableCell>
                    {note.conversionPrice
                      ? formatCurrency(note.conversionPrice)
                      : "N/A"}
                  </TableCell>
                  <TableCell>{note.estimatedShares.toLocaleString()}</TableCell>
                </TableRow>
              ))}

              {/* Totals row */}
              <TableRow className="bg-muted/50">
                <TableCell colSpan={1} className="font-medium">
                  Total
                </TableCell>
                <TableCell className="font-medium">
                  {formatCurrency(totals.principal)}
                </TableCell>
                <TableCell colSpan={5}></TableCell>
                <TableCell className="font-medium">
                  {formatCurrency(totals.interestAccrued)}
                </TableCell>
                <TableCell className="font-medium">
                  {formatCurrency(totals.totalWithInterest)}
                </TableCell>
                <TableCell></TableCell>
                <TableCell className="font-medium">
                  {totals.estimatedShares.toLocaleString()}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConvertibleTable;
