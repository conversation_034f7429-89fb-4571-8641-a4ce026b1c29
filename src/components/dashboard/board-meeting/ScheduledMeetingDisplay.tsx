import React from "react";
import { format } from "date-fns";
import { X } from "lucide-react";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";

interface ScheduledMeetingDisplayProps {
  scheduledMeeting: Date | null;
  handleCancelMeeting: () => void;
}

const ScheduledMeetingDisplay: React.FC<ScheduledMeetingDisplayProps> = ({
  scheduledMeeting,
  handleCancelMeeting,
}) => {
  const onCancelMeeting = () => {
    handleCancelMeeting();
    toast({
      title: "Meeting Cancelled",
      description: "The board meeting has been cancelled successfully.",
    });
  };

  return (
    <div className="flex flex-col space-y-1">
      <Label className="text-sm font-semibold">Next Scheduled Meeting</Label>
      <div className="flex justify-between items-center text-sm">
        <span>
          {scheduledMeeting
            ? format(scheduledMeeting, "PPP 'at' h:mm a")
            : "None"}
        </span>
        {scheduledMeeting && (
          <button
            onClick={onCancelMeeting}
            className="text-xs italic text-blue-600 hover:text-blue-800 flex items-center"
          >
            <X className="h-3 w-3 mr-1" />
            Cancel Meeting
          </button>
        )}
      </div>
    </div>
  );
};

export default ScheduledMeetingDisplay;
