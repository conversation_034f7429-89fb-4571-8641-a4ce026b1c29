import { Shareholder } from "@/types/capTable";

// Shareholders with their holdings
export const shareholders: Shareholder[] = [
  {
    id: "1",
    name: "<PERSON>",
    role: "Founder",
    holdings: {
      Common: 5000000,
    },
    totalShares: 5000000,
    percentage: 50,
    fullyDilutedPercentage: 42.55,
  },
  {
    id: "2",
    name: "<PERSON>",
    role: "Founder",
    holdings: {
      Common: 3000000,
    },
    totalShares: 3000000,
    percentage: 30,
    fullyDilutedPercentage: 25.53,
  },
  {
    id: "3",
    name: "Acme Ventures",
    role: "Investor",
    holdings: {
      "Series A Preferred": 2000000,
    },
    totalShares: 2000000,
    percentage: 20,
    fullyDilutedPercentage: 17.02,
  },
  {
    id: "4",
    name: "Employee Stock Option Plan",
    role: "Other",
    holdings: {
      "Stock Option Pool": 1750000,
    },
    totalShares: 0, // Not counted in outstanding shares
    percentage: 0,
    fullyDilutedPercentage: 14.89,
  },
];
