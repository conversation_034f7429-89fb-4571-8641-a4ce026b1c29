import React from "react";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Button from "@/components/common/Button";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

const HeroSection: React.FC = () => {
  const { isMfaVerified, loading } = useAuth();
  return (
    <section className="relative overflow-hidden pt-16 md:pt-24 lg:pt-32">
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50 to-white z-0"></div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-legal-100 opacity-30 blur-3xl"></div>
        <div className="absolute top-60 -left-20 w-60 h-60 rounded-full bg-legal-200 opacity-20 blur-3xl"></div>
      </div>

      <div className="container relative z-10 px-4 md:px-8">
        <div className="max-w-3xl mx-auto text-center">
          <AnimatedTransition>
            <div className="inline-block bg-legal-50 px-3 py-1 rounded-full text-legal-700 text-sm font-medium mb-6">
              Streamlined Legal Solutions
            </div>
          </AnimatedTransition>

          <AnimatedTransition delay={0.1}>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 tracking-tight mb-6">
              Your <span className="text-legal-500">Legal Concierge</span> for
              Company Formation
            </h1>
          </AnimatedTransition>

          <AnimatedTransition delay={0.2}>
            <p className="text-xl text-gray-600 mb-8 mx-auto max-w-2xl">
              Effortlessly form your company with our guided process. Generate
              legal documents, collect e-signatures, and stay compliant—all in
              one platform.
            </p>
          </AnimatedTransition>

          <AnimatedTransition delay={0.3}>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              {loading || isMfaVerified ? null : (
                <Button
                  size="lg"
                  className="group"
                  icon={
                    <ArrowRight
                      className="transition-transform group-hover:translate-x-1"
                      size={18}
                    />
                  }
                  iconPosition="right"
                >
                  <Link to="/login">Sign In</Link>
                </Button>
              )}
              <Button size="lg" variant="outline">
                <Link to="/login">Learn More</Link>
              </Button>
            </div>
          </AnimatedTransition>
        </div>

        {/* <div className="mt-24 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.5 }}
          >
            <p className="text-gray-600 mb-8">
              Trusted by innovative founders and startups
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex justify-center">
                  <div className="h-8 bg-gray-200 rounded w-32 animate-pulse"></div>
                </div>
              ))}
            </div>
          </motion.div>
        </div> */}
      </div>
    </section>
  );
};

export default HeroSection;
