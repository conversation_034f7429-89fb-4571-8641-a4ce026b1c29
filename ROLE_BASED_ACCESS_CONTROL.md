# Role-Based Access Control Implementation

This document outlines the implementation of role-based access control (RBAC) in the Legal Concierge application.

## Overview

The system supports three distinct user roles with different permissions:

### OWNER Role

- Full access to all application features
- Can view and edit all questionnaire forms
- Can modify company data and settings
- Can view and interact with all sections (forms, review, documents, data room, post-incorporation tasks)
- Can invite and manage collaborators
- Can confirm document reviews and initiate signature processes

### COLLABORATOR Role

- Limited access with view-only permissions for most features
- Can view all questionnaire forms in read-only mode
- Cannot edit company data or settings
- Can add comments in the "View Documents" section
- Can access the data room ONLY after the signature process is completed
- Cannot view post-incorporation tasks (hidden from navigation/UI)
- Can only see status information for post-incorporation items

### SIGNER Role

- Extremely limited access - essentially data room only
- Can ONLY access the Data Room page (`/data-room`) and ONLY after signature completion
- Cannot access any questionnaire forms (`/questions`) - redirected to signer dashboard
- Cannot access the review documents page (`/review-documents`)
- Cannot access any other pages (post-incorporation, dashboard sections, etc.)
- Has minimal navigation - only Data Room access when available
- Redirected to dedicated signer dashboard with signature status information

## Implementation Details

### 1. Core Permission System

#### `useUserPermissions` Hook

Located in `src/integrations/legal-concierge/hooks/useUserProfile.ts`

```typescript
export const useUserPermissions = (user: User | null) => {
  const isOwner = user?.roles?.includes(USER_ROLES.OWNER) ?? false;
  const isCollaborator =
    user?.roles?.includes(USER_ROLES.COLLABORATOR) ?? false;
  const isSigner = user?.roles?.includes(USER_ROLES.SIGNER) ?? false;

  return {
    // Basic role checks
    isOwner,
    isCollaborator,
    isSigner,

    // Permission checks
    canEditForms: isOwner,
    canViewForms: isOwner || isCollaborator, // SIGNER cannot view forms
    canConfirmReview: isOwner,
    canAccessPostIncorporation: isOwner,
    canManageCollaborators: isOwner,
    canEditCompanyData: isOwner,
    canViewDataRoom: isOwner || isCollaborator || isSigner, // SIGNER can view data room
    canUploadDocuments: isOwner,
    canCommentOnDocuments: isOwner || isCollaborator, // SIGNER cannot comment

    // Navigation permissions
    canAccessQuestionnaire: isOwner || isCollaborator, // SIGNER cannot access questionnaire
    canAccessReviewPage: isOwner || isCollaborator, // SIGNER cannot access review page
    canAccessDataRoomAfterSignature: isOwner || isCollaborator || isSigner, // SIGNER can access after signature
    canAccessDashboard: isOwner || isCollaborator, // SIGNER cannot access dashboard

    // SIGNER-specific permissions
    isSignerOnly: isSigner && !isOwner && !isCollaborator,
    canOnlyAccessDataRoom: isSigner && !isOwner && !isCollaborator,
  };
};
```

### 2. Role-Based Access Components

#### `RoleBasedAccess` Component

Located in `src/components/auth/RoleBasedAccess.tsx`

Provides conditional rendering based on user roles and permissions:

```typescript
<RoleBasedAccess requiredPermission="canEditForms">
  <EditableForm />
</RoleBasedAccess>

<RoleBasedAccess allowedRoles={[USER_ROLES.OWNER]}>
  <OwnerOnlyContent />
</RoleBasedAccess>
```

#### Enhanced `ProtectedRoute` Component

Located in `src/components/auth/ProtectedRoute.tsx`

Supports role-based route protection:

```typescript
<ProtectedRoute allowedRoles={[USER_ROLES.OWNER]}>
  <PostIncorporation />
</ProtectedRoute>
```

### 3. Page-Level Access Control

#### Questions Page (`/questions`)

- **OWNER**: Full access to edit questionnaire forms
- **COLLABORATOR**: Read-only access to view all questionnaire forms and company details
- **SIGNER**: No access - redirected to signer dashboard

#### Review Documents Page (`/review-documents`)

- **OWNER**: Can view documents and confirm review
- **COLLABORATOR**: Can view documents (read-only), cannot confirm review
- **SIGNER**: No access - redirected to signer dashboard

#### Post-Incorporation Page (`/post-incorporation`)

- **OWNER**: Full access to all post-incorporation tasks
- **COLLABORATOR**: No access (route protected)
- **SIGNER**: No access - redirected to signer dashboard

#### Data Room Page (`/data-room`)

- **OWNER**: Full access at any time, can upload documents
- **COLLABORATOR**: Access only after signature completion, view-only
- **SIGNER**: Access only after signature completion, view-only

#### Signer Dashboard Page (`/signer-dashboard`)

- **OWNER**: No access - redirected to main dashboard
- **COLLABORATOR**: No access - redirected to main dashboard
- **SIGNER**: Full access - shows signature status and data room access when available

### 4. Component-Level Access Control

#### Dashboard Getting Started Card

- Post-incorporation sections hidden for collaborators
- Questionnaire section available for both roles (read-only for collaborators)
- Review documents section available for both roles

#### Document Review Workflow

- Confirm Review button only visible to owners
- Signature initiation restricted to owners

### 5. Read-Only Form Implementation

#### QuestionsForm Component

The questionnaire forms support read-only mode for collaborators:

```typescript
interface QuestionsFormProps {
  isEditMode?: boolean;
  readOnly?: boolean;
}

// Usage
<QuestionsForm
  isEditMode={permissions.canEditForms ? isEditMode : false}
  readOnly={!permissions.canEditForms}
/>
```

#### StepRenderer Component

Implements read-only functionality by:

- Disabling form updates with a no-op function
- Hiding navigation buttons in read-only mode
- Showing "Back to Dashboard" button instead

```typescript
const handleUpdateFormData = readOnly
  ? () => {} // No-op function for read-only mode
  : updateFormData;
```

### 6. Navigation Updates

#### App.tsx Route Protection

```typescript
// Mixed access routes (OWNER and COLLABORATOR only)
<Route path="/questions" element={
  <ProtectedRoute
    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
    redirectTo="/signer-dashboard"
  >
    <Questions />
  </ProtectedRoute>
} />

<Route path="/review-documents" element={
  <ProtectedRoute
    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
    redirectTo="/signer-dashboard"
  >
    <ReviewDocuments />
  </ProtectedRoute>
} />

// Data Room access (all roles, but with signature completion check)
<Route path="/data-room" element={
  <ProtectedRoute allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR, USER_ROLES.SIGNER]}>
    <DataRoom />
  </ProtectedRoute>
} />

// Owner-only routes
<Route path="/post-incorporation" element={
  <ProtectedRoute
    allowedRoles={[USER_ROLES.OWNER]}
    redirectTo="/signer-dashboard"
  >
    <PostIncorporation />
  </ProtectedRoute>
} />

// SIGNER-only routes
<Route path="/signer-dashboard" element={
  <ProtectedRoute allowedRoles={[USER_ROLES.SIGNER]}>
    <SignerDashboard />
  </ProtectedRoute>
} />
```

## Usage Examples

### Conditional Rendering

```typescript
import { useAuth } from "@/contexts/AuthContext";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { RoleBasedAccess } from "@/components/auth/RoleBasedAccess";

const MyComponent = () => {
  const { user } = useAuth();
  const permissions = useUserPermissions(user);

  return (
    <div>
      {/* Show edit button only to owners */}
      <RoleBasedAccess requiredPermission="canEditForms">
        <EditButton />
      </RoleBasedAccess>

      {/* Show different content based on role */}
      {permissions.isOwner ? (
        <OwnerDashboard />
      ) : (
        <CollaboratorDashboard />
      )}
    </div>
  );
};
```

### Permission Checks

```typescript
const handleAction = () => {
  if (!permissions.canEditForms) {
    toast.error("You don't have permission to edit forms");
    return;
  }
  // Proceed with action
};
```

## Security Considerations

1. **Client-side only**: This implementation provides UI/UX control but should be backed by server-side authorization
2. **Role validation**: User roles are validated on each permission check
3. **Graceful degradation**: Unauthorized users see appropriate fallback content or are redirected
4. **Toast notifications**: Users are informed when they lack permissions for specific actions

## Future Enhancements

1. **Granular permissions**: Add more specific permissions for different features
2. **Role hierarchy**: Implement role inheritance (e.g., ADMIN > OWNER > COLLABORATOR)
3. **Dynamic permissions**: Allow runtime permission updates
4. **Audit logging**: Track permission-based actions for compliance
