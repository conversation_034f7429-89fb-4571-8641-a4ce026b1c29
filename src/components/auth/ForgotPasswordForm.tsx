import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Button from "@/components/common/Button";
import { ChevronRight, Mail } from "lucide-react";
import { Link } from "react-router-dom";

interface ForgotPasswordFormProps {
  email: string;
  setEmail: (email: string) => void;
  loading: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
}

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  email,
  setEmail,
  loading,
  onSubmit,
}) => {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <div className="relative">
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
            <Mail size={18} />
          </div>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            className="pl-10"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>
      </div>

      <Button
        type="submit"
        className="w-full"
        loading={loading}
        icon={<ChevronRight size={16} />}
        iconPosition="right"
      >
        Send Reset Link
      </Button>

      <div className="text-center mt-4">
        <Link to="/login" className="text-sm text-blue-600 hover:text-blue-800">
          Back to Login
        </Link>
      </div>
    </form>
  );
};

export default ForgotPasswordForm;
