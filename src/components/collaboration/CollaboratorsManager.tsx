import React, { useState } from 'react';
import { 
  useCompanyCollaborators, 
  useInviteCollaborator, 
  useUpdateCollaboratorRole, 
  useDeleteCollaborator 
} from '@/integrations/legal-concierge/hooks';
import { Collaborator, CollaboratorRole } from '@/integrations/legal-concierge/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

const CollaboratorsManager: React.FC = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<CollaboratorRole>('collaborator');

  // Fetch collaborators
  const { data: collaborators, isLoading, error } = useCompanyCollaborators(companyId || '');
  
  // Mutations
  const inviteCollaborator = useInviteCollaborator();
  const updateRole = useUpdateCollaboratorRole();
  const deleteCollaborator = useDeleteCollaborator();

  // Handle invite
  const handleInvite = async () => {
    if (!companyId) {
      toast.error('No company selected');
      return;
    }
    
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }
    
    try {
      await inviteCollaborator.mutateAsync({
        companyId,
        email,
        role,
      });
      
      toast.success(`Invitation sent to ${email}`);
      setEmail('');
    } catch (error) {
      toast.error(`Failed to invite collaborator: ${error.message}`);
    }
  };

  // Handle role change
  const handleRoleChange = async (collaboratorId: string, newRole: CollaboratorRole) => {
    if (!companyId) return;
    
    try {
      await updateRole.mutateAsync({
        collaboratorId,
        companyId,
        role: newRole,
      });
      
      toast.success('Role updated successfully');
    } catch (error) {
      toast.error(`Failed to update role: ${error.message}`);
    }
  };

  // Handle delete
  const handleDelete = async (collaboratorId: string) => {
    if (!companyId) return;
    
    try {
      await deleteCollaborator.mutateAsync({
        collaboratorId,
        companyId,
      });
      
      toast.success('Collaborator removed successfully');
    } catch (error) {
      toast.error(`Failed to remove collaborator: ${error.message}`);
    }
  };

  if (!companyId) {
    return <div>Please select a company first</div>;
  }

  if (isLoading) {
    return <div>Loading collaborators...</div>;
  }

  if (error) {
    return <div>Error loading collaborators: {error.message}</div>;
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Manage Collaborators</h2>
      
      {/* Invite form */}
      <div className="p-4 border rounded-md space-y-4">
        <h3 className="text-lg font-medium">Invite a Collaborator</h3>
        <div className="flex gap-4">
          <Input
            placeholder="Email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="flex-1"
          />
          <Select value={role} onValueChange={(value) => setRole(value as CollaboratorRole)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="collaborator">Collaborator</SelectItem>
              <SelectItem value="viewer">Viewer</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            onClick={handleInvite} 
            disabled={inviteCollaborator.isPending}
          >
            {inviteCollaborator.isPending ? 'Sending...' : 'Invite'}
          </Button>
        </div>
      </div>
      
      {/* Collaborators list */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Current Collaborators</h3>
        
        {collaborators && collaborators.length > 0 ? (
          <div className="space-y-2">
            {collaborators.map((collaborator) => (
              <div 
                key={collaborator.id} 
                className="p-4 border rounded-md flex items-center justify-between"
              >
                <div>
                  <p className="font-medium">{collaborator.name}</p>
                  <p className="text-sm text-gray-500">{collaborator.email}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      collaborator.status === 'accepted' ? 'bg-green-100 text-green-800' :
                      collaborator.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {collaborator.status}
                    </span>
                    <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                      {collaborator.role}
                    </span>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Select 
                    defaultValue={collaborator.role}
                    onValueChange={(value) => handleRoleChange(collaborator.id, value as CollaboratorRole)}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Change role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="collaborator">Collaborator</SelectItem>
                      <SelectItem value="viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button 
                    variant="destructive" 
                    onClick={() => handleDelete(collaborator.id)}
                    disabled={deleteCollaborator.isPending}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-8 border rounded-md text-center">
            <p className="text-gray-500">No collaborators yet</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollaboratorsManager;
