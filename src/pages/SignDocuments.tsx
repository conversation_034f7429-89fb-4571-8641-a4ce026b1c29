import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyDocuments } from '@/integrations/legal-concierge/hooks/useDocuments';
import DocumentsList from '@/components/documents/DocumentsList';
import DocumentModal from '@/components/documents/DocumentModal';
import { Document } from '@/integrations/legal-concierge/types/Document';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { APIClient } from '@/integrations/legal-concierge/client';

const SignDocuments: React.FC = () => {
  const { user } = useAuth();
  const companyId = user?.companyId || '';
  const { data: documents, isLoading, error, refetch } = useCompanyDocuments(companyId);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [documentMode, setDocumentMode] = useState<'view' | 'edit'>('view');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const isOwner = user?.roles?.includes('OWNER');

  const handleDocumentOpen = (doc: Document, mode: 'view' | 'edit') => {
    setSelectedDocument(doc);
    setDocumentMode(mode);
    setIsModalOpen(true);
  };

  const handleConfirmSigning = async () => {
    if (!companyId) {
      toast.error('Company ID is required');
      return;
    }

    if (!isOwner) {
      toast.error('Only owners can sign documents');
      return;
    }

    try {
      const api = new APIClient();
      const response = await api.signCompanyDocuments(companyId);
      if ('error' in response && response.error) {
        throw new Error(response.error);
      }
      localStorage.setItem('documentsSignedStatus', 'completed');
      toast.success('Documents signed successfully');
      setShowConfirmDialog(false);
      refetch();
    } catch (error) {
      console.error('Error signing documents:', error);
      toast.error('Failed to sign documents');
    }
  };

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Sign Documents</h1>
          <p className="text-gray-600 mt-1">
            Sign your company's legal documents electronically
          </p>
        </div>

        {isOwner && (
          <div className="mb-6">
            <Button
              onClick={() => setShowConfirmDialog(true)}
              disabled={!documents?.length}
            >
              Sign All Documents
            </Button>
          </div>
        )}

        <DocumentsList
          documents={documents}
          isLoading={isLoading}
          error={error}
          handleDocumentOpen={handleDocumentOpen}
          userData={user}
          companyId={companyId}
          onReviewConfirmed={refetch}
        />

        {selectedDocument && (
          <DocumentModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            ffdocument={selectedDocument}
            mode={documentMode}
          />
        )}

        <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm Document Signing</DialogTitle>
              <DialogDescription>
                Are you sure you want to sign all the documents? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleConfirmSigning}>
                Sign Documents
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default SignDocuments;
