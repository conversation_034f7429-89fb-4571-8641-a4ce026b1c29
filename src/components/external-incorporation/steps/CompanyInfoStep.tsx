import React from "react";
import { ExternalIncorporationFormData } from "../types";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface CompanyInfoStepProps {
  formData: ExternalIncorporationFormData;
  updateFormData: (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => void;
}

const CompanyInfoStep: React.FC<CompanyInfoStepProps> = ({
  formData,
  updateFormData,
}) => {
  const handleAddressChange = (
    field: keyof typeof formData.companyAddress,
    value: string
  ) => {
    updateFormData("companyAddress", {
      ...formData.companyAddress,
      [field]: value,
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Please enter the basic information about your company exactly as it
          appears in your incorporation documents.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="company-name">
            Company Name (including "Inc." or "LLC")
          </Label>
          <Input
            id="company-name"
            value={formData.companyName}
            onChange={(e) => updateFormData("companyName", e.target.value)}
            placeholder="e.g., Acme Corporation, Inc."
            className="mt-1"
          />
        </div>

        <div>
          <Label>Company Address</Label>
          <div className="grid grid-cols-1 gap-4 mt-1">
            <Input
              placeholder="Street Address"
              value={formData.companyAddress.street}
              onChange={(e) => handleAddressChange("street", e.target.value)}
            />
            <div className="grid grid-cols-2 gap-4">
              <Input
                placeholder="City"
                value={formData.companyAddress.city}
                onChange={(e) => handleAddressChange("city", e.target.value)}
              />
              <Input
                placeholder="State"
                value={formData.companyAddress.state}
                onChange={(e) => handleAddressChange("state", e.target.value)}
              />
            </div>
            <Input
              placeholder="ZIP Code"
              value={formData.companyAddress.zipCode}
              onChange={(e) => handleAddressChange("zipCode", e.target.value)}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="incorporation-date">Incorporation Date</Label>
          <Input
            id="incorporation-date"
            type="date"
            value={formData.incorporationDate}
            onChange={(e) =>
              updateFormData("incorporationDate", e.target.value)
            }
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="authorized-shares">Authorized Shares</Label>
          <Input
            id="authorized-shares"
            type="number"
            value={formData.authorizedShares}
            onChange={(e) =>
              updateFormData("authorizedShares", parseInt(e.target.value) || 0)
            }
            className="mt-1"
          />
          <p className="text-xs text-gray-500 mt-1">
            The maximum number of shares your company is authorized to issue.
          </p>
        </div>

        <div>
          <Label htmlFor="issued-shares">Initially Issued Shares</Label>
          <Input
            id="issued-shares"
            type="number"
            value={formData.issuedShares}
            onChange={(e) =>
              updateFormData("issuedShares", parseInt(e.target.value) || 0)
            }
            className="mt-1"
          />
          <p className="text-xs text-gray-500 mt-1">
            The number of shares already issued to founders and others.
          </p>
        </div>

        <div>
          <Label htmlFor="par-value">Par Value per Share</Label>
          <Input
            id="par-value"
            type="number"
            step="0.00001"
            min="0"
            value={formData.parValuePerShare}
            onChange={(e) =>
              updateFormData(
                "parValuePerShare",
                parseFloat(e.target.value) || 0
              )
            }
            className="mt-1"
          />
          <p className="text-xs text-gray-500 mt-1">
            The nominal value assigned to each share (e.g., 0.00001).
          </p>
        </div>
      </div>
    </div>
  );
};

export default CompanyInfoStep;
