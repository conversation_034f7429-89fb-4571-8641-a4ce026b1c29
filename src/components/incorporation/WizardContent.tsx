import React from "react";
import { CardContent } from "@/components/common/Card";
import { motion, AnimatePresence } from "framer-motion";
import CompanyNameStep from "./steps/CompanyNameStep";
import CompanyAddressStep from "./steps/CompanyAddressStep";
import ReviewStep from "./steps/ReviewStep";
import { WizardStep, FormData } from "./types";

interface WizardContentProps {
  currentStep: WizardStep;
  formData: FormData;
  updateFormData: (field: string, value: string) => void;
  nameAvailable: boolean | null;
  checkingName: boolean;
  checkCompanyName: () => void;
}

const WizardContent: React.FC<WizardContentProps> = ({
  currentStep,
  formData,
  updateFormData,
  nameAvailable,
  checkingName,
  checkCompanyName,
}) => {
  const getStepContent = () => {
    switch (currentStep) {
      case "company-name":
        return (
          <CompanyNameStep
            formData={formData}
            updateFormData={updateFormData}
            nameAvailable={nameAvailable}
            checkingName={checkingName}
            checkCompanyName={checkCompanyName}
          />
        );

      case "company-address":
        return (
          <CompanyAddressStep
            formData={formData}
            updateFormData={updateFormData}
          />
        );

      case "review":
        return <ReviewStep formData={formData} />;
    }
  };

  return (
    <CardContent>
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {getStepContent()}
        </motion.div>
      </AnimatePresence>
    </CardContent>
  );
};

export default WizardContent;
