import React from "react";
import Button from "@/components/common/Button";
import { LogIn } from "lucide-react";

interface SocialAuthProps {
  loading: boolean;
  onDemoLogin: () => Promise<void>;
}

const SocialAuth: React.FC<SocialAuthProps> = ({ loading, onDemoLogin }) => {
  return (
    <>
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-200" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="bg-white px-2 text-gray-500">Or continue with</span>
        </div>
      </div>

      <div className="flex flex-col space-y-3">
        <Button
          variant="outline"
          className="w-full"
          onClick={onDemoLogin}
          type="button"
          disabled={loading}
          icon={<LogIn size={16} />}
          iconPosition="left"
        >
          Try Demo Access
        </Button>
      </div>
    </>
  );
};

export default SocialAuth;
