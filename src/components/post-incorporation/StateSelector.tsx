import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { X, Plus, ExternalLink } from "lucide-react";
import { StateLink } from "./types";

interface StateSelectorProps {
  selectedStates: StateLink[];
  stateLinks: Record<string, string>;
  onAddState: (state: StateLink) => Promise<void>;
  onRemoveState: (stateName: string) => Promise<void>;
}

const StateSelector: React.FC<StateSelectorProps> = ({
  selectedStates,
  stateLinks,
  onAddState,
  onRemoveState,
}) => {
  const [selectedState, setSelectedState] = useState<string>("");
  const [isAdding, setIsAdding] = useState(false);
  const [removingStates, setRemovingStates] = useState<Set<string>>(new Set());

  const handleAddState = async () => {
    if (!selectedState) return;

    const stateLink = stateLinks[selectedState];
    if (
      stateLink &&
      !selectedStates.some((item) => item.state === selectedState)
    ) {
      setIsAdding(true);
      try {
        await onAddState({ state: selectedState, link: stateLink });
        setSelectedState("");
      } catch (error) {
        console.error("Error adding state:", error);
      } finally {
        setIsAdding(false);
      }
    }
  };

  const handleRemoveState = async (stateName: string) => {
    setRemovingStates((prev) => new Set(prev).add(stateName));
    try {
      await onRemoveState(stateName);
    } catch (error) {
      console.error("Error removing state:", error);
    } finally {
      setRemovingStates((prev) => {
        const newSet = new Set(prev);
        newSet.delete(stateName);
        return newSet;
      });
    }
  };

  // Create an array of state abbreviations from the stateLinks object
  const stateOptions = Object.keys(stateLinks).sort();

  return (
    <div className="space-y-4 mt-4">
      <div>
        <Label htmlFor="state-select">
          Select States for Foreign Qualification
        </Label>
        <div className="flex mt-1">
          <select
            id="state-select"
            value={selectedState}
            onChange={(e) => setSelectedState(e.target.value)}
            className="flex-1 border border-gray-300 rounded-l px-3 py-2"
          >
            <option value="">Select a state...</option>
            {stateOptions.map((state) => (
              <option key={state} value={state}>
                {state}
              </option>
            ))}
          </select>
          <Button
            onClick={handleAddState}
            className="rounded-l-none"
            disabled={!selectedState || isAdding}
          >
            <Plus className="h-4 w-4 mr-2" />
            {isAdding ? "Adding..." : "Add"}
          </Button>
        </div>
      </div>

      {selectedStates.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Selected States:</h4>
          <div className="space-y-2">
            {selectedStates.map((item) => (
              <div
                key={item.state}
                className="flex items-center justify-between bg-gray-50 p-2 rounded"
              >
                <div className="flex items-center">
                  <span className="font-medium mr-2">{item.state}</span>
                  <a
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline text-sm flex items-center"
                  >
                    Visit site <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveState(item.state)}
                  disabled={removingStates.has(item.state)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default StateSelector;
