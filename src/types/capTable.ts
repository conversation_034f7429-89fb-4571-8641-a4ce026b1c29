export type ShareholderRole =
  | "Founder"
  | "Investor"
  | "Employee"
  | "Advisor"
  | "Other";

export type ShareClass =
  | "Common"
  | "Series A Preferred"
  | "Series B Preferred"
  | "Stock Option Pool";

export type ShareClassDetails = {
  name: ShareClass;
  type: "Common" | "Preferred" | "Option";
  conversionRatio?: number;
  liquidationPreference?: number;
};

export type Shareholder = {
  id: string;
  name: string;
  role: ShareholderRole;
  holdings: {
    [key in ShareClass]?: number;
  };
  totalShares: number;
  percentage: number;
  fullyDilutedPercentage: number;
};

export type InvestmentRound = {
  roundName: string;
  investmentAmount: number;
  preMoneyValuation: number;
  postMoneyValuation?: number;
  sharesIssued?: number;
  pricePerShare?: number;
  date?: Date;
};

export type Convertible = {
  instrument: "SAFE" | "Note";
  valuationCap?: number;
  discount?: number;
  principal: number;
  interestRate?: number;
  issueDate?: Date;
  maturityDate?: Date;
};

export type StockGrant = {
  recipient: string;
  optionsGranted: number;
  vestingSchedule: string;
  grantDate?: Date;
};

export type StockOptionPlan = {
  poolSizePercentage: number;
  totalShares: number;
  grants: StockGrant[];
};

export type CapTableSummary = {
  id?: string;
  name?: string;
  totalShares: number;
  totalFullyDiluted: number;
  shareClasses: ShareClassDetails[];
  shareholders: Shareholder[];
  lastUpdated: Date;
  snapshots: {
    id: string;
    name: string;
    date: Date;
  }[];
  companyName?: string;
  isUserProvided?: boolean;
  investmentRounds?: InvestmentRound[];
  convertibles?: Convertible[];
  stockOptionPlan?: StockOptionPlan;
};

// Updated ProFormaInvestment type with user-editable flag for investment amount
export type ProFormaInvestment = {
  id: string;
  name: string;
  amount: number;
  pricePerShare: number;
  shareClass: ShareClass;
  date?: Date;
  preMoneyValuation: number;
  postMoneyValuation?: number;
  dilutedPoolPercentage?: number;
  isUserEdited?: boolean;
  isAmountEdited?: boolean; // New flag for editing investment amount
};

export type ProFormaSummary = {
  preMoney: number;
  postMoney: number;
  newShares: number;
  newPercentage: number;
  dilutedPoolPercentage: number;
};

// Convertible Note types
export type ConvertibleNote = {
  id: string;
  investor: string;
  principal: number;
  interestRate: number;
  issueDate: Date;
  maturityDate: Date;
  valuationCap?: number;
  discount?: number;
  conversionPrice?: number;
  estimatedShares?: number;
};

// Stock Option Pool types
export type OptionGrant = {
  id: string;
  recipient: string;
  role: ShareholderRole;
  grantDate: Date;
  optionsGranted: number;
  vestingStart: Date;
  vestingPeriod: number; // months
  cliff: number; // months
  percentVested: number;
  currentValue: number;
  isEditing?: boolean; // New property to track edit state
};

export type SOPSummary = {
  totalPool: number;
  allocated: number;
  remaining: number;
  grants: OptionGrant[];
};

// Pro Forma Voting rights
export type VotingRights = {
  shareholder: string;
  votingShares: number;
  votingPercentage: number;
  votingPower: "High" | "Medium" | "Low";
};

export type ProFormaVoting = {
  votingRights: VotingRights[];
  threshold: {
    majority: number;
    supermajority: number;
  };
};

// New types for user data handling
export type UserCapTableData = {
  capTables: CapTableSummary[];
  currentCapTableId?: string;
};

export interface CapTableDataService {
  loadUserData: () => Promise<UserCapTableData>;
  saveCapTable: (capTable: CapTableSummary) => Promise<CapTableSummary>;
  deleteCapTable: (id: string) => Promise<boolean>;
  getCapTableById: (id: string) => Promise<CapTableSummary | null>;
}

// Document types for the Data Room
export type Document = {
  id: string;
  name: string;
  path: string;
  type: string;
  size: number;
  createdAt: Date;
  updatedAt: Date;
  url?: string;
};

export type Subfolder = {
  documents: Document[];
  subfolders?: Record<string, Subfolder>;
};

export type Folder = {
  documents: Document[];
  subfolders?: Record<string, Subfolder>;
};

export interface DocumentFile {
  fileName: string;
  url: string;
  thumbnailUrl: string;
}

export interface FolderStructure {
  [key: string]: {
    documents: DocumentFile[];
    subfolders?: Record<string, {
      documents: DocumentFile[];
      isCompleted?: boolean;
    }>;
    isCompleted?: boolean;
  };
}
