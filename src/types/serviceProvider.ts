export type ServiceProviderType = "Advisor" | "Contractor" | "Employee";

export type GrantType = "Option" | "Restricted Stock" | "None";

export type OptionType = "Statutory" | "Non-Statutory";

export type VestingScheduleType = "Standard" | "Custom";

export type ServiceProvider = {
  id: string;
  name: string;
  email: string;
  address: string;
  services?: string; // For Advisor and Consultant only
  grantType: GrantType;
  optionType?: OptionType;
  shares?: number;
  startDate: Date;
  vestingSchedule: VestingScheduleType;
  vestingPeriod?: number; // In months
  cliff?: number; // In months
  compensation?: number;
  compensationPeriod?: "Annually" | "Monthly" | "Weekly" | "Hourly";
  type: ServiceProviderType;
  status: "Pending" | "Active" | "Terminated" | "Promised" | "Issued";
  documents?: string[];
  company_id?: string; // Add this property to match database schema
};

export type PromisedGrant = {
  id: string;
  serviceProviderId: string;
  serviceProviderName: string;
  serviceProviderType: ServiceProviderType;
  vestingCommencementDate: Date;
  grantType: GrantType;
  optionType?: OptionType;
  shares: number;
  vestingSchedule: string;
  status: "Promised" | "Issued" | "Board Approved" | "Completed";
};

export type CompanyEquityInfo = {
  authorizedShares: number;
  issuedShares: number;
  stockOptionPlan?: {
    totalShares: number;
    allocatedShares: number;
  };
};
