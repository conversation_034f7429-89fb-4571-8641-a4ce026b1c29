import React from "react";
import Layout from "@/components/Layout";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/common/Card";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { FileText, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { Spinner } from "@/components/ui/spinner";

const SignerDashboard: React.FC = () => {
  const { user } = useAuth();
  const { companyDetails, loading } = useCompanyDetails();
  const permissions = useUserPermissions(user);

  // Show loading state while company data is being fetched
  if (loading) {
    return (
      <Layout>
        <div className="container py-8 px-4 md:px-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center justify-center">
              <Spinner size="lg" />
              <p className="text-gray-600 mt-4">
                Loading signature status...
              </p>
              </div>
          </div>
        </div>
      </Layout>
    );
  }

  const isSignatureComplete = companyDetails?.isSignatureComplete;
  const isSignatureConfirmed = companyDetails?.isSignatureConfirmed;
  const username = user?.fullName || user?.email?.split("@")[0] || "User";

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome, {username}
            </h1>
            <p className="text-gray-600 mt-1">
              Document Signature Portal
            </p>
          </div>

          {/* Signature Status Card */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Signature Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Signature Process Status */}
                <div className="flex items-start space-x-3">
                  {isSignatureConfirmed ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  ) : (
                    <Clock className="h-5 w-5 text-yellow-500 mt-0.5" />
                  )}
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">
                      {isSignatureConfirmed 
                        ? "Documents Sent for Signature" 
                        : "Preparing Documents"
                      }
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {isSignatureConfirmed
                        ? "Documents have been prepared and sent to all signers."
                        : "Documents are being prepared for signature."
                      }
                    </p>
                  </div>
                </div>

                {/* Completion Status */}
                {isSignatureConfirmed && (
                <div className="flex items-start space-x-3">
                  {isSignatureComplete ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  ) : (
                    <Clock className="h-5 w-5 text-yellow-500 mt-0.5" />
                  )}
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">
                      {isSignatureComplete 
                        ? "Signature Process Complete" 
                        : "Awaiting Signatures"
                      }
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {isSignatureComplete
                        ? "All required signatures have been collected."
                        : "Waiting for all parties to complete their signatures."
                      }
                    </p>
                  </div>
                </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Data Room Access Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Access
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isSignatureComplete ? (
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">
                        Data Room Access Available
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        You can now access the data room to view all signed documents.
                      </p>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Button asChild>
                      <Link to="/data-room">
                        Access Data Room
                      </Link>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">
                        Data Room Access Pending
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        Access to the data room will be available once all signatures are completed.
                      </p>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Button disabled variant="outline">
                      Data Room (Available After Signing)
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Limited Access Notice */}
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900">
                  Limited Access Account
                </h3>
                <p className="text-sm text-blue-700 mt-1">
                  Your account has been configured with document access only. 
                  You can view signed documents in the data room once the signature process is complete.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SignerDashboard;
