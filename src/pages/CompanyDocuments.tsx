import React, { useState } from "react";
import Layout from "@/components/Layout";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanyDocuments } from "@/integrations/legal-concierge/hooks/useDocuments";
import { Document } from "@/integrations/legal-concierge/types/Document";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import DocumentsList from "@/components/documents/DocumentsList";
import DocumentModal from "@/components/documents/DocumentModal";

type TabValue = "all" | "incorporation" | "bylaws" | "other";

const CompanyDocuments: React.FC = () => {
  const { user } = useAuth();
  const companyId = user?.companyId || "";
  const { data: documents, isLoading, error, refetch } = useCompanyDocuments(companyId);
  const [activeTab, setActiveTab] = useState<TabValue>("all");
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [documentMode, setDocumentMode] = useState<'view' | 'edit'>('view');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const filterDocuments = (documents: Document[] = []) => {
    if (activeTab === "all") return documents;

    return documents.filter((doc) => {
      switch (activeTab) {
        case "incorporation":
          return doc.category === "INCORPORATION";
        case "bylaws":
          return doc.category === "BYLAWS";
        case "other":
          return doc.category !== "INCORPORATION" && doc.category !== "BYLAWS";
        default:
          return true;
      }
    });
  };

  const handleDocumentOpen = (doc: Document, mode: 'view' | 'edit') => {
    setSelectedDocument(doc);
    setDocumentMode(mode);
    setIsModalOpen(true);
  };

  const handleReviewConfirmed = () => {
    refetch();
  };

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Company Documents
          </h1>
          <p className="text-gray-600 mt-1">
            View and edit your company's legal documents
          </p>
        </div>

        <Tabs
          defaultValue="all"
          className="mb-8"
          onValueChange={(value) => setActiveTab(value as TabValue)}
        >
          <TabsList>
            <TabsTrigger value="all">All Documents</TabsTrigger>
            {/* <TabsTrigger value="incorporation">Incorporation</TabsTrigger>
            <TabsTrigger value="bylaws">Bylaws</TabsTrigger>
            <TabsTrigger value="other">Other</TabsTrigger> */}
          </TabsList>

          <TabsContent value="all" className="mt-6">
            <DocumentsList
              documents={filterDocuments(documents)}
              isLoading={isLoading}
              error={error}
              handleDocumentOpen={handleDocumentOpen}
              userData={user}
              companyId={companyId}
              onReviewConfirmed={handleReviewConfirmed}
            />
          </TabsContent>

          <TabsContent value="incorporation" className="mt-6">
            <DocumentsList
              documents={filterDocuments(documents)}
              isLoading={isLoading}
              error={error}
              handleDocumentOpen={handleDocumentOpen}
              userData={user}
              companyId={companyId}
              onReviewConfirmed={handleReviewConfirmed}
            />
          </TabsContent>

          <TabsContent value="bylaws" className="mt-6">
            <DocumentsList
              documents={filterDocuments(documents)}
              isLoading={isLoading}
              error={error}
              handleDocumentOpen={handleDocumentOpen}
              userData={user}
              companyId={companyId}
              onReviewConfirmed={handleReviewConfirmed}
            />
          </TabsContent>

          <TabsContent value="other" className="mt-6">
            <DocumentsList
              documents={filterDocuments(documents)}
              isLoading={isLoading}
              error={error}
              handleDocumentOpen={handleDocumentOpen}
              userData={user}
              companyId={companyId}
              onReviewConfirmed={handleReviewConfirmed}
            />
          </TabsContent>
        </Tabs>

        {selectedDocument && (
          <DocumentModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            ffdocument={selectedDocument}
            mode={documentMode}
          />
        )}
      </div>
    </Layout>
  );
};

export default CompanyDocuments;
