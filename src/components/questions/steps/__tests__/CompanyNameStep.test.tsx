import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor, act } from "@/test/utils";
import userEvent from "@testing-library/user-event";
import CompanyNameStep from "../CompanyNameStep";
import { mockLegacyFormData } from "@/test/mocks/companyData";

describe("CompanyNameStep", () => {
  const mockUpdateFormData = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render company name input", () => {
    render(
      <CompanyNameStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByLabelText(/preferred name of the Company/i)
    ).toBeInTheDocument();
    expect(screen.getByDisplayValue("Test Company Inc.")).toBeInTheDocument();
  });

  it("should update company name when user types", async () => {
    const user = userEvent.setup();

    render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "" }}
        updateFormData={mockUpdateFormData}
      />
    );

    const input = screen.getByLabelText(/preferred name of the Company/i);
    await user.type(input, "New Company Name");

    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalledWith(
        "companyName",
        "New Company Name"
      );
    });
  });

  it("should show incorporation date picker when not incorporating today", () => {
    render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, incorporatingToday: false }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByText(/When do you plan to incorporate/i)
    ).toBeInTheDocument();
  });

  it("should not show incorporation date picker when incorporating today", () => {
    render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, incorporatingToday: true }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.queryByText(/When do you plan to incorporate/i)
    ).not.toBeInTheDocument();
  });

  it("should handle incorporation timing selection", async () => {
    const user = userEvent.setup();

    render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, incorporatingToday: null }}
        updateFormData={mockUpdateFormData}
      />
    );

    // Click "Yes" for incorporating today
    const yesOption = screen.getByLabelText(/^Yes$/i);
    await user.click(yesOption);

    expect(mockUpdateFormData).toHaveBeenCalledWith("incorporatingToday", true);
  });

  it("should show company name suggestion for Inc. suffix", () => {
    render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Test Company" }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByText(/Consider adding 'Inc.' or 'Incorporated'/i)
    ).toBeInTheDocument();
  });

  it("should not show Inc. suggestion when already included", () => {
    render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Test Company Inc." }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.queryByText(/Consider adding 'Inc.' or 'Incorporated'/i)
    ).not.toBeInTheDocument();
  });

  it("should display different question text for external incorporation mode", () => {
    render(
      <CompanyNameStep
        formData={{
          ...mockLegacyFormData,
          registerMode: "external-incorporation",
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByText(/What is the name of your incorporated company/i)
    ).toBeInTheDocument();
  });

  it("should maintain local state when formData changes during editing", async () => {
    const user = userEvent.setup();
    const { rerender } = render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Initial Name" }}
        updateFormData={mockUpdateFormData}
      />
    );

    const input = screen.getByLabelText(/preferred name of the Company/i);

    // Start typing
    await user.clear(input);
    await user.type(input, "User Typed Name");

    // Verify the user's input is displayed
    expect(screen.getByDisplayValue("User Typed Name")).toBeInTheDocument();

    // Simulate formData change from parent (like during a save operation)
    rerender(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Server Updated Name" }}
        updateFormData={mockUpdateFormData}
      />
    );

    // The user's input should still be visible (not overridden by server data)
    expect(screen.getByDisplayValue("User Typed Name")).toBeInTheDocument();
  });

  it("should update local state from formData when user is not editing", async () => {
    const { rerender } = render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Initial Name" }}
        updateFormData={mockUpdateFormData}
      />
    );

    // Verify initial value
    expect(screen.getByDisplayValue("Initial Name")).toBeInTheDocument();

    // Update formData when user is not editing
    rerender(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Updated Name" }}
        updateFormData={mockUpdateFormData}
      />
    );

    // Should show the updated value from formData
    expect(screen.getByDisplayValue("Updated Name")).toBeInTheDocument();
  });

  it("should reset editing state after user stops typing", async () => {
    const user = userEvent.setup();
    vi.useFakeTimers();

    const { rerender } = render(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Initial Name" }}
        updateFormData={mockUpdateFormData}
      />
    );

    const input = screen.getByLabelText(/preferred name of the Company/i);

    // Start typing
    await user.clear(input);
    await user.type(input, "User Input");

    // Verify user input is shown
    expect(screen.getByDisplayValue("User Input")).toBeInTheDocument();

    // Fast forward time to trigger the debounced reset
    act(() => {
      vi.advanceTimersByTime(1100); // More than 1 second
    });

    // Now formData changes should be reflected
    rerender(
      <CompanyNameStep
        formData={{ ...mockLegacyFormData, companyName: "Server Data" }}
        updateFormData={mockUpdateFormData}
      />
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue("Server Data")).toBeInTheDocument();
    });

    vi.useRealTimers();
  });
});
