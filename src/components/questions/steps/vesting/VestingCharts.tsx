import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { QuestionnaireFormData } from "../../types";
import { Officer } from "../../types/Officer";
import {
  generateVestingTimeline,
  createChartData,
  findMaxSharesValue,
} from "./utils/VestingDataUtils";
import VestingChart from "./components/VestingChart";
import VestingLegend from "./components/VestingLegend";

interface VestingChartsProps {
  formData: QuestionnaireFormData;
  directorsWithVesting: Officer[];
  calculateShares: (percentage: number) => number;
  getScheduleLabel: (schedule: any) => string;
  getAccelerationLabel: (acceleration: any) => string;
}

const VestingCharts: React.FC<VestingChartsProps> = ({
  formData,
  directorsWithVesting,
  calculateShares,
  getScheduleLabel,
  getAccelerationLabel,
}) => {
  const [chartData, setChartData] = useState<any[]>([]);
  const [vestingTimelines, setVestingTimelines] = useState<any[]>([]);
  const [maxShares, setMaxShares] = useState(0);

  // Generate vesting data when directors or form data changes
  useEffect(() => {
    // If no directors have vesting, don't process chart data
    if (directorsWithVesting.length === 0) {
      setVestingTimelines([]);
      setChartData([]);
      setMaxShares(0);
      return;
    }

    // Set the vesting start date to incorporation date if available, otherwise today
    const vestingStartDate = formData.incorporationDate || new Date();

    // Generate all directors' vesting timelines
    const timelines = directorsWithVesting
      .map((director) =>
        generateVestingTimeline(
          director,
          formData,
          calculateShares,
          vestingStartDate
        )
      )
      .filter(Boolean);

    // Create a unified dataset for the chart
    const significantMonths = [0, 6, 12, 18, 24, 30, 36, 42, 48];
    const data = createChartData(
      timelines,
      vestingStartDate,
      significantMonths
    );

    // Find maximum shares for Y-axis scale
    const maxSharesValue = findMaxSharesValue(timelines);

    setVestingTimelines(timelines);
    setChartData(data);
    setMaxShares(maxSharesValue);
  }, [formData, directorsWithVesting, calculateShares]);

  // Generate a unique color for each director
  const colors = [
    "#4ade80",
    "#3b82f6",
    "#f97316",
    "#ec4899",
    "#8b5cf6",
    "#14b8a6",
  ];

  // If no directors have vesting, don't show charts
  if (directorsWithVesting.length === 0) {
    return null;
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="text-lg">
          Vesting Schedule Visualization
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <p className="text-sm text-gray-600">
            This chart shows the number of shares vested over time based on each
            person's vesting schedule.
            {formData.incorporationDate
              ? ` Starting from the incorporation date (${formData.incorporationDate.toLocaleDateString()}).`
              : " Starting from today."}
          </p>
        </div>

        <VestingChart
          chartData={chartData}
          maxShares={maxShares}
          vestingTimelines={vestingTimelines}
          colors={colors}
        />

        <VestingLegend />
      </CardContent>
    </Card>
  );
};

export default VestingCharts;
