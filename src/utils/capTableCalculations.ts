import {
  Shareholder,
  ConvertibleNote,
  ProFormaInvestment,
  CapTableSummary,
} from "@/types/capTable";

// Calculate Total Shares Outstanding
export function calculateTotalShares(shareholders: Shareholder[]): number {
  return shareholders.reduce((total, holder) => total + holder.totalShares, 0);
}

// Calculate Ownership Percentages
export function calculateOwnership(
  shareholders: Shareholder[],
  totalShares: number
): Shareholder[] {
  return shareholders.map((holder) => ({
    ...holder,
    percentage: (holder.totalShares / totalShares) * 100,
    fullyDilutedPercentage: (holder.totalShares / totalShares) * 100, // This would be adjusted for fully diluted
  }));
}

// Calculate Post-Money Valuation
export function calculatePostMoneyValuation(
  investment: ProFormaInvestment
): number {
  return investment.amount + (investment.preMoneyValuation || 0);
}

// Calculate Convertible Security Conversion
export function calculateConvertibleShares(
  convertible: ConvertibleNote,
  currentSharePrice: number,
  totalPreInvestmentShares: number
): number {
  // If there's a specific conversion price, use that
  if (convertible.conversionPrice) {
    return convertible.principal / convertible.conversionPrice;
  }

  // Calculate based on discount
  const discountedPrice = convertible.discount
    ? currentSharePrice * (1 - convertible.discount / 100)
    : currentSharePrice;

  // Calculate based on valuation cap
  const capPrice = convertible.valuationCap
    ? convertible.valuationCap / totalPreInvestmentShares
    : Number.POSITIVE_INFINITY; // No cap = infinite price

  // Use the lower of discounted price or cap price
  const conversionPrice = Math.min(discountedPrice, capPrice);

  return conversionPrice > 0 ? convertible.principal / conversionPrice : 0;
}

// Process cap table data from JSON format
export function processCapTableFromJson(data: any): CapTableSummary {
  // Extract shareholders
  const shareholders: Shareholder[] = data.shareholders.map(
    (s: any, index: number) => {
      const totalShares =
        (s.commonShares || 0) + (s.preferredShares || 0) + (s.options || 0);

      return {
        id: `sh-${index}`,
        name: s.name,
        role: s.role,
        holdings: {
          Common: s.commonShares || 0,
          "Series A Preferred": s.preferredShares || 0,
          "Stock Option Pool": s.options || 0,
        },
        totalShares,
        percentage: 0, // Will be calculated later
        fullyDilutedPercentage: 0, // Will be calculated later
      };
    }
  );

  // Calculate total shares
  const totalShares = calculateTotalShares(shareholders);

  // Calculate percentages
  const shareholdersWithPercentages = calculateOwnership(
    shareholders,
    totalShares
  );

  return {
    id: `ct-${Date.now()}`,
    name: "Imported Cap Table",
    totalShares,
    totalFullyDiluted: totalShares, // Adjust if you have convertibles or options data
    shareClasses: [
      { name: "Common", type: "Common" },
      {
        name: "Series A Preferred",
        type: "Preferred",
        conversionRatio: 1,
        liquidationPreference: 1,
      },
      { name: "Stock Option Pool", type: "Option" },
    ],
    shareholders: shareholdersWithPercentages,
    lastUpdated: new Date(),
    snapshots: [{ id: `snap-${Date.now()}`, name: "Import", date: new Date() }],
    investmentRounds: data.investmentRounds || [],
    convertibles: data.convertibles || [],
    stockOptionPlan: data.stockOptionPlan || {
      poolSizePercentage: 0,
      totalShares: 0,
      grants: [],
    },
    isUserProvided: true,
  };
}
