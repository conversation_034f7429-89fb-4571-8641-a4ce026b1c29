import React from "react";
import { cn } from "@/lib/utils";
import Logo from "./common/Logo";
import { motion } from "framer-motion";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import UserProfileDropdown from "./dashboard/UserProfileDropdown";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
  transparent?: boolean;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  className,
  showHeader = true,
  showFooter = true,
  transparent = false,
}) => {
  const location = useLocation();
  const isHome = location.pathname === "/home" || location.pathname === "/";
  const isDashboard = location.pathname === "/dashboard";
  const isAbout = location.pathname === "/about";
  const { loading, isAuthenticated, user } = useAuth();
  const { isCompanySelected } = useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);

  return (
    <div className="min-h-screen flex flex-col">
      {showHeader && (
        <motion.header
          className={cn(
            "sticky top-0 z-40 w-full border-b",
            transparent
              ? "bg-white/80 backdrop-blur-md backdrop-saturate-150 border-transparent"
              : "bg-white/80 backdrop-blur-md backdrop-saturate-150 border-gray-100/50"
          )}
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
        >
          <div className="container flex h-16 items-center justify-between py-4">
            <Logo />
            <nav className="hidden md:flex items-center space-x-6 bg-white/90 px-4 py-2 rounded-full">
              <Link
                to="/home"
                className={cn(
                  "text-sm font-medium transition-colors",
                  isHome
                    ? "text-legal-600"
                    : "text-gray-600 hover:text-legal-600"
                )}
              >
                Home
              </Link>
              {/* Show different dashboard link based on user role */}
              {permissions.isSigner ? (
                <Link
                  to="/signer-dashboard"
                  className={cn(
                    "text-sm font-medium transition-colors",
                    location.pathname === "/signer-dashboard"
                      ? "text-legal-600"
                      : "text-gray-600 hover:text-legal-600"
                  )}
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/dashboard"
                  className={cn(
                    "text-sm font-medium transition-colors",
                    isDashboard
                      ? "text-legal-600"
                      : "text-gray-600 hover:text-legal-600"
                  )}
                >
                  Dashboard
                </Link>
              )}
              <Link
                to="/about"
                className={cn(
                  "text-sm font-medium transition-colors",
                  isAbout
                    ? "text-legal-600"
                    : "text-gray-600 hover:text-legal-600"
                )}
              >
                About
              </Link>
            </nav>
            {
              <div className="flex items-center space-x-4 bg-white/90 px-4 py-2 rounded-full">
                {loading ? null : isAuthenticated ? (
                  <UserProfileDropdown />
                ) : (
                  <>
                    <Link to="/login">
                      <Button
                        variant="outline"
                        size="sm"
                        className="hidden md:inline-flex"
                      >
                        Sign In
                      </Button>
                    </Link>
                    <Link to="/login">
                      <Button size="sm">Get Started</Button>
                    </Link>
                  </>
                )}
              </div>
            }
          </div>
        </motion.header>
      )}
      <main className={cn("flex-1", className)}>{children}</main>
      {showFooter && (
        <footer className="border-t border-gray-100 bg-gray-50/50">
          <div className="container py-12 px-4 md:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <Logo size="sm" />
                <p className="mt-4 text-sm text-gray-600 max-w-xs">
                  Streamlining the incorporation process with precision and
                  clarity.
                </p>
              </div>
              <div className="grid grid-cols-2 gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-900">
                    Platform
                  </h3>
                  <ul className="mt-4 space-y-3">
                    <li>
                      <Link
                        to="/features"
                        className="text-sm text-gray-600 hover:text-legal-600 transition-colors"
                      >
                        Features
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/pricing"
                        className="text-sm text-gray-600 hover:text-legal-600 transition-colors"
                      >
                        Pricing
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/testimonials"
                        className="text-sm text-gray-600 hover:text-legal-600 transition-colors"
                      >
                        Testimonials
                      </Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-gray-900">
                    Company
                  </h3>
                  <ul className="mt-4 space-y-3">
                    <li>
                      <Link
                        to="/about"
                        className="text-sm text-gray-600 hover:text-legal-600 transition-colors"
                      >
                        About
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/contact"
                        className="text-sm text-gray-600 hover:text-legal-600 transition-colors"
                      >
                        Contact
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/privacy"
                        className="text-sm text-gray-600 hover:text-legal-600 transition-colors"
                      >
                        Privacy
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-semibold text-gray-900">
                  Subscribe to our newsletter
                </h3>
                <p className="mt-4 text-sm text-gray-600">
                  Get the latest legal tech updates and incorporation tips.
                </p>
                <form className="mt-4 flex">
                  <input
                    type="email"
                    placeholder="Email address"
                    className="min-w-0 flex-auto rounded-l-md border border-gray-200 bg-white px-3.5 py-2 text-sm text-gray-700 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-legal-500"
                  />
                  <Button className="rounded-l-none">Subscribe</Button>
                </form>
              </div>
            </div>
            <div className="mt-12 border-t border-gray-100 pt-8 flex flex-col items-center justify-between sm:flex-row">
              <p className="text-xs text-gray-500">
                © {new Date().getFullYear()} Legal Concierge. All rights
                reserved.
              </p>
              <div className="mt-4 flex space-x-6 sm:mt-0">
                <Link
                  to="/terms"
                  className="text-xs text-gray-500 hover:text-gray-600"
                >
                  Terms
                </Link>
                <Link
                  to="/privacy"
                  className="text-xs text-gray-500 hover:text-gray-600"
                >
                  Privacy
                </Link>
                <Link
                  to="/cookies"
                  className="text-xs text-gray-500 hover:text-gray-600"
                >
                  Cookies
                </Link>
              </div>
            </div>
          </div>
        </footer>
      )}
    </div>
  );
};

export default Layout;
