import React from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useOptionPlanDialog } from "@/hooks/useOptionPlanDialog";
import { useCapTableData } from "@/hooks/useCapTableData";
import BoardApprovalOptionPlanSection from "./BoardApprovalOptionPlanSection";
import StockholderApprovalOptionPlanSection from "./StockholderApprovalOptionPlanSection";
import IncreaseOptionPlanInitialState from "./IncreaseOptionPlanInitialState";
import NoStockOptionPlanState from "./NoStockOptionPlanState";

interface IncreaseOptionPlanDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const IncreaseOptionPlanDialog: React.FC<IncreaseOptionPlanDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { currentCapTable, sopSummary } = useCapTableData();

  // Current values
  const sharesReservedForIssuance = sopSummary?.totalPool || 0;
  const promisedGrants = 0; // This would be calculated from cap table data in a real implementation
  const sharesRemaining = Math.max(
    0,
    (sopSummary?.remaining || 0) - promisedGrants
  );
  const issuedAndOutstanding = currentCapTable?.totalShares || 0;
  const authorizedShares = 10000000; // In a real app, this would come from the cap table data

  const {
    additionalShares,
    totalOptionShares,
    boardApproved,
    stockholdersApproved,
    step,
    exceedsAuthorized,
    requiredAuthorizedShares,
    handleAdditionalSharesChange,
    handleBoardApprovalComplete,
    handleStockholderApprovalComplete,
    resetState,
    setStep,
  } = useOptionPlanDialog(
    sharesReservedForIssuance,
    issuedAndOutstanding,
    authorizedShares
  );

  const handleClose = () => {
    resetState();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md sm:max-w-lg">
        {sharesReservedForIssuance === 0 ? (
          <NoStockOptionPlanState handleClose={handleClose} />
        ) : (
          <>
            {step === "initial" && (
              <IncreaseOptionPlanInitialState
                sharesReservedForIssuance={sharesReservedForIssuance}
                promisedGrants={promisedGrants}
                sharesRemaining={sharesRemaining}
                additionalShares={additionalShares}
                totalOptionShares={totalOptionShares}
                exceedsAuthorized={exceedsAuthorized}
                requiredAuthorizedShares={requiredAuthorizedShares}
                boardApproved={boardApproved}
                stockholdersApproved={stockholdersApproved}
                handleAdditionalSharesChange={handleAdditionalSharesChange}
                handleClose={handleClose}
                setStep={setStep}
              />
            )}

            {step === "board" && (
              <BoardApprovalOptionPlanSection
                currentShares={sharesReservedForIssuance}
                newTotalShares={totalOptionShares}
                onComplete={handleBoardApprovalComplete}
                onCancel={() => setStep("initial")}
              />
            )}

            {step === "stockholders" && (
              <StockholderApprovalOptionPlanSection
                currentShares={sharesReservedForIssuance}
                newTotalShares={totalOptionShares}
                onComplete={handleStockholderApprovalComplete}
                onCancel={() => setStep("initial")}
              />
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default IncreaseOptionPlanDialog;
