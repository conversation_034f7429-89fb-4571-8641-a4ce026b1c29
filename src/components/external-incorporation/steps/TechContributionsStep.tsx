import React from "react";
import { ExternalIncorporationFormData, TechContribution } from "../types";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import Button from "@/components/common/Button";
import { PlusCircle, Trash2 } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TechContributionsStepProps {
  formData: ExternalIncorporationFormData;
  updateFormData: (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => void;
}

const TechContributionsStep: React.FC<TechContributionsStepProps> = ({
  formData,
  updateFormData,
}) => {
  const addTechContribution = () => {
    // Default to first stakeholder if available
    const defaultStakeholderId =
      formData.stakeholders.length > 0 ? formData.stakeholders[0].id : "";

    const newContribution: TechContribution = {
      id: uuidv4(),
      stakeholderId: defaultStakeholderId,
      description: "",
    };

    updateFormData("techContributions", [
      ...formData.techContributions,
      newContribution,
    ]);
  };

  const removeTechContribution = (id: string) => {
    updateFormData(
      "techContributions",
      formData.techContributions.filter((c) => c.id !== id)
    );
  };

  const updateTechContribution = (
    id: string,
    field: keyof TechContribution,
    value: any
  ) => {
    updateFormData(
      "techContributions",
      formData.techContributions.map((c) =>
        c.id === id ? { ...c, [field]: value } : c
      )
    );
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Please describe any technology or intellectual property that has been
          assigned to the company.
        </p>
      </div>

      {formData.stakeholders.length === 0 ? (
        <div className="text-center p-6 bg-gray-50 rounded-lg">
          <p className="text-gray-500">
            Please add stakeholders first before describing technology
            contributions.
          </p>
        </div>
      ) : (
        <>
          <div className="space-y-6">
            {formData.techContributions.length === 0 ? (
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <p className="text-gray-500 mb-4">
                  No technology contributions added yet
                </p>
                <Button
                  onClick={addTechContribution}
                  icon={<PlusCircle size={16} />}
                >
                  Add Technology Contribution
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {formData.techContributions.map((contribution) => (
                  <div
                    key={contribution.id}
                    className="p-4 border rounded-lg bg-gray-50"
                  >
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">Technology Contribution</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeTechContribution(contribution.id)}
                        icon={<Trash2 size={16} />}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        Remove
                      </Button>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor={`contributor-${contribution.id}`}>
                          Contributor
                        </Label>
                        <Select
                          value={contribution.stakeholderId}
                          onValueChange={(value) =>
                            updateTechContribution(
                              contribution.id,
                              "stakeholderId",
                              value
                            )
                          }
                        >
                          <SelectTrigger id={`contributor-${contribution.id}`}>
                            <SelectValue placeholder="Select a contributor" />
                          </SelectTrigger>
                          <SelectContent>
                            {formData.stakeholders.map((stakeholder) => (
                              <SelectItem
                                key={stakeholder.id}
                                value={stakeholder.id}
                              >
                                {stakeholder.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`description-${contribution.id}`}>
                          Description
                        </Label>
                        <Textarea
                          id={`description-${contribution.id}`}
                          value={contribution.description}
                          onChange={(e) =>
                            updateTechContribution(
                              contribution.id,
                              "description",
                              e.target.value
                            )
                          }
                          placeholder="Describe the technology or IP contributed..."
                          rows={4}
                        />
                      </div>
                    </div>
                  </div>
                ))}

                <Button
                  onClick={addTechContribution}
                  icon={<PlusCircle size={16} />}
                  variant="outline"
                  className="mt-4"
                >
                  Add Another Contribution
                </Button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default TechContributionsStep;
