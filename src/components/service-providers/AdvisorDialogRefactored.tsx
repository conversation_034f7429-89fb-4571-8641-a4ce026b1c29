import React from "react";
import BaseServiceProviderDialog from "./BaseServiceProviderDialog";

interface AdvisorDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdvisorDialog: React.FC<AdvisorDialogProps> = ({ isOpen, onClose }) => {
  return (
    <BaseServiceProviderDialog
      isOpen={isOpen}
      onClose={onClose}
      type="Advisor"
      title="Onboard Advisor"
      description="Add a new advisor to your company and set up their equity grant."
      confirmTitle="Confirm Advisor Details"
      confirmDescription="Review the information before finalizing."
      successTitle="Advisor Onboarding Initiated"
      successDescription="The advisor has been added successfully and will receive onboarding documentation."
    />
  );
};

export default AdvisorDialog;
