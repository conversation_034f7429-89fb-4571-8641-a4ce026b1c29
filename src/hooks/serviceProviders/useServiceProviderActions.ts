import { UseServiceProvidersActions } from "./types";
import { useAddServiceProvider } from "./actions/useAddServiceProvider";
import { useUpdateGrantStatus } from "./actions/useUpdateGrantStatus";
import { useIssueEquityGrant } from "./actions/useIssueEquityGrant";
import { useTerminateServiceProvider } from "./actions/useTerminateServiceProvider";

export function useServiceProviderActions(
  serviceProviders: any[],
  setServiceProviders: React.Dispatch<React.SetStateAction<any[]>>,
  promisedGrants: any[],
  setPromisedGrants: React.Dispatch<React.SetStateAction<any[]>>,
  companyEquity: any,
  setCompanyEquity: React.Dispatch<React.SetStateAction<any>>,
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
): UseServiceProvidersActions {
  // Use the individual hooks
  const addServiceProvider = useAddServiceProvider(
    serviceProviders,
    setServiceProviders,
    promisedGrants,
    setPromisedGrants,
    companyEquity,
    setCompanyEquity,
    setLoading
  );

  const updateGrantStatus = useUpdateGrantStatus(
    promisedGrants,
    setPromisedGrants
  );

  const issueEquityGrant = useIssueEquityGrant(
    promisedGrants,
    setPromisedGrants,
    setLoading
  );

  const terminateServiceProvider = useTerminateServiceProvider(
    serviceProviders,
    setServiceProviders
  );

  return {
    addServiceProvider,
    updateGrantStatus,
    issueEquityGrant,
    terminateServiceProvider,
  };
}
