import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { getMaintenanceItems } from "./maintenance/MaintenanceItems";
import MaintenanceItemsList from "./maintenance/MaintenanceItemsList";
import MaintenanceDialogs from "./maintenance/MaintenanceDialogs";
import { useMaintenanceDialogs } from "./maintenance/useMaintenanceDialogs";

const MaintenanceCard: React.FC = () => {
  const {
    isSharesDialogOpen,
    setIsSharesDialogOpen,
    isOptionPlanDialogOpen,
    setIsOptionPlanDialogOpen,
    isBoardMeetingDialogOpen,
    setIsBoardMeetingDialogOpen,
    isAdvisorDialogOpen,
    setIsAdvisorDialogOpen,
    isContractorDialogOpen,
    setIsContractorDialogOpen,
    isEmployeeDialogOpen,
    setIsEmployeeDialogOpen,
    isTerminateDialogOpen,
    setIsTerminateDialogOpen,
    isPromisedGrantsDialogOpen,
    setIsPromisedGrantsDialogOpen,
  } = useMaintenanceDialogs();

  // Mock data for authorized shares - in a real app this would come from an API
  const currentAuthorizedShares = 10000000;

  const maintenanceItems = getMaintenanceItems({
    onSharesDialogClick: () => setIsSharesDialogOpen(true),
    onOptionPlanDialogClick: () => setIsOptionPlanDialogOpen(true),
    onBoardMeetingDialogClick: () => setIsBoardMeetingDialogOpen(true),
    onAdvisorClick: () => setIsAdvisorDialogOpen(true),
    onContractorClick: () => setIsContractorDialogOpen(true),
    onEmployeeClick: () => setIsEmployeeDialogOpen(true),
    onTerminateClick: () => setIsTerminateDialogOpen(true),
    onPromisedGrantsClick: () => setIsPromisedGrantsDialogOpen(true),
  });

  return (
    <AnimatedTransition delay={0.5} className="mb-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Maintenance</CardTitle>
          <CardDescription>
            Ongoing tasks to keep your company in good standing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MaintenanceItemsList items={maintenanceItems} />
        </CardContent>
      </Card>

      <MaintenanceDialogs
        isSharesDialogOpen={isSharesDialogOpen}
        setIsSharesDialogOpen={setIsSharesDialogOpen}
        isOptionPlanDialogOpen={isOptionPlanDialogOpen}
        setIsOptionPlanDialogOpen={setIsOptionPlanDialogOpen}
        isBoardMeetingDialogOpen={isBoardMeetingDialogOpen}
        setIsBoardMeetingDialogOpen={setIsBoardMeetingDialogOpen}
        isAdvisorDialogOpen={isAdvisorDialogOpen}
        setIsAdvisorDialogOpen={setIsAdvisorDialogOpen}
        isContractorDialogOpen={isContractorDialogOpen}
        setIsContractorDialogOpen={setIsContractorDialogOpen}
        isEmployeeDialogOpen={isEmployeeDialogOpen}
        setIsEmployeeDialogOpen={setIsEmployeeDialogOpen}
        isTerminateDialogOpen={isTerminateDialogOpen}
        setIsTerminateDialogOpen={setIsTerminateDialogOpen}
        isPromisedGrantsDialogOpen={isPromisedGrantsDialogOpen}
        setIsPromisedGrantsDialogOpen={setIsPromisedGrantsDialogOpen}
        currentAuthorizedShares={currentAuthorizedShares}
      />
    </AnimatedTransition>
  );
};

export default MaintenanceCard;
