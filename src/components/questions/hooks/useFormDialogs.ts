import { useState } from "react";

export const useFormDialogs = () => {
  const [showNameDialog, setShowNameDialog] = useState(false);
  const [showIncorporationDialog, setShowIncorporationDialog] = useState(false);
  const [showSharesDialog, setShowSharesDialog] = useState(false);

  const handleNameDialogConfirm = (
    updateNameAvailability: (checked: boolean) => void,
    isIncorporatingToday: boolean | null,
    navigateNext: () => void,
    showIncorporationDialog: () => void
  ) => {
    console.log("intel inside");
    updateNameAvailability(true);
    setShowNameDialog(false);

    if (isIncorporatingToday !== false) {
      navigateNext();
    } else {
      showIncorporationDialog();
    }
  };

  const handleIncorporationDialogConfirm = (continueFormSubmission: () => void) => {
    setShowIncorporationDialog(false);
    // Call the callback to continue with form submission
    continueFormSubmission();
  };

  return {
    showNameDialog,
    showIncorporationDialog,
    showSharesDialog,
    setShowNameDialog,
    setShowIncorporationDialog,
    setShowSharesDialog,
    handleNameDialogConfirm,
    handleIncorporationDialogConfirm,
  };
};
