import React, { useEffect, useState } from "react";
import Layout from "@/components/Layout";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import QuestionsForm from "@/components/questions/QuestionsForm";
import { toast } from "sonner";
import { useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";
import { useQuestionsForm } from "@/components/questions/hooks/useQuestionsForm";
import QuestionsFormSkeleton from "@/components/questions/QuestionsFormSkeleton";

const Questions: React.FC = () => {
  const [isEditMode, setIsEditMode] = useState(false);
  const location = useLocation();
  const { user } = useAuth();
  const { isCompanySelected } = useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);
  const { isLoading } = useQuestionsForm(isEditMode);
  // Check if we're in edit mode from URL params
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const editMode = searchParams.get("edit") === "true";
    setIsEditMode(editMode);
  }, [location]);

  // Show appropriate message for edit mode
  useEffect(() => {
    if (isEditMode) {
      toast.info(
        "You are editing your company details. Changes will update throughout the system."
      );
    }
  }, [isEditMode]);

  return (
    <Layout>
      <div className="container py-12 px-4 md:px-8">
        <AnimatedTransition className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {permissions.canEditForms
                ? isEditMode
                  ? "Edit Company Details"
                  : "Company Incorporation"
                : "Company Details (View Only)"}
            </h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {permissions.canEditForms
                ? isEditMode
                  ? "Update your company information below. Changes will be reflected throughout the system."
                  : "Please provide the following information to complete your company formation process."
                : "You can view the company details below. Only owners can make changes to this information."}
            </p>
            {!permissions.canEditForms && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg max-w-md mx-auto">
                <p className="text-sm text-blue-700">
                  <strong>Read-Only Access:</strong> You can view all company
                  information but cannot make changes.
                </p>
              </div>
            )}
          </div>

          {isLoading ? (
            <QuestionsFormSkeleton />
          ) : (
            <QuestionsForm
              isEditMode={permissions.canEditForms ? isEditMode : false}
              readOnly={!permissions.canEditForms}
            />
          )}
        </AnimatedTransition>
      </div>
    </Layout>
  );
};

export default Questions;
