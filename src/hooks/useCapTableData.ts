import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { capTableService } from "@/services/capTableService";
import {
  CapTableSummary,
  UserCapTableData,
  ProFormaInvestment,
  ConvertibleNote,
  SOPSummary,
  ProFormaVoting,
} from "@/types/capTable";
import {
  mockProFormaInvestment,
  mockConvertibleNotes,
  mockSOPSummary,
  mockProFormaVoting,
} from "@/data/index";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";

export const useCapTableData = () => {
  const { toast } = useToast();
  const { companyDetails } = useCompanyDetails();
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState<UserCapTableData>({ capTables: [] });
  const [currentCapTable, setCurrentCapTable] =
    useState<CapTableSummary | null>(null);
  const [proFormaInvestment, setProFormaInvestment] = useState<
    ProFormaInvestment | undefined
  >(undefined);
  const [convertibleNotes, setConvertibleNotes] = useState<
    ConvertibleNote[] | undefined
  >(undefined);
  const [sopSummary, setSopSummary] = useState<SOPSummary | undefined>(
    undefined
  );
  const [proFormaVoting, setProFormaVoting] = useState<
    ProFormaVoting | undefined
  >(undefined);

  // Load user data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const data = await capTableService.loadUserData();

        // If we have company details but no cap tables, create a basic one
        if (companyDetails?.companyName && data.capTables.length === 0) {
          // This will be handled in the CapTable component
        }

        setUserData(data);

        // Set current cap table
        if (data.currentCapTableId && data.capTables.length > 0) {
          const current = data.capTables.find(
            (ct) => ct.id === data.currentCapTableId
          );
          if (current) {
            setCurrentCapTable(current);
          } else {
            setCurrentCapTable(data.capTables[0]);
          }
        } else if (data.capTables.length > 0) {
          setCurrentCapTable(data.capTables[0]);
        }

        // Set mock data for other components for now
        // In a real app, these would be loaded from the backend
        setProFormaInvestment(mockProFormaInvestment);
        setConvertibleNotes(mockConvertibleNotes);
        setSopSummary(mockSOPSummary);
        setProFormaVoting(mockProFormaVoting);
      } catch (error) {
        console.error("Error loading cap table data:", error);
        toast({
          title: "Error loading data",
          description:
            "We couldn't load your cap table data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [companyDetails]);

  // Save cap table
  const saveCapTable = async (capTable: CapTableSummary) => {
    try {
      setLoading(true);
      const saved = await capTableService.saveCapTable(capTable);
      setCurrentCapTable(saved);

      // Update user data
      const updatedUserData = { ...userData };
      const existingIndex = updatedUserData.capTables.findIndex(
        (ct) => ct.id === saved.id
      );

      if (existingIndex >= 0) {
        updatedUserData.capTables[existingIndex] = saved;
      } else {
        updatedUserData.capTables.push(saved);
      }

      updatedUserData.currentCapTableId = saved.id;
      setUserData(updatedUserData);

      toast({
        title: "Cap table saved",
        description: "Your cap table has been successfully saved.",
      });

      return saved;
    } catch (error) {
      console.error("Error saving cap table:", error);
      toast({
        title: "Error saving cap table",
        description: "We couldn't save your cap table. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Delete cap table
  const deleteCapTable = async (id: string) => {
    try {
      setLoading(true);
      const success = await capTableService.deleteCapTable(id);

      if (success) {
        const updatedUserData = {
          ...userData,
          capTables: userData.capTables.filter((ct) => ct.id !== id),
        };

        if (updatedUserData.capTables.length > 0) {
          updatedUserData.currentCapTableId = updatedUserData.capTables[0].id;
          setCurrentCapTable(updatedUserData.capTables[0]);
        } else {
          updatedUserData.currentCapTableId = undefined;
          setCurrentCapTable(null);
        }

        setUserData(updatedUserData);

        toast({
          title: "Cap table deleted",
          description: "Your cap table has been successfully deleted.",
        });
      } else {
        throw new Error("Failed to delete cap table");
      }
    } catch (error) {
      console.error("Error deleting cap table:", error);
      toast({
        title: "Error deleting cap table",
        description: "We couldn't delete your cap table. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Switch to a different cap table
  const switchCapTable = async (id: string) => {
    try {
      setLoading(true);
      const capTable = await capTableService.getCapTableById(id);

      if (capTable) {
        setCurrentCapTable(capTable);

        // Update current cap table ID in user data
        const updatedUserData = { ...userData, currentCapTableId: id };
        setUserData(updatedUserData);
        await capTableService.loadUserData();

        return capTable;
      } else {
        throw new Error("Cap table not found");
      }
    } catch (error) {
      console.error("Error switching cap table:", error);
      toast({
        title: "Error switching cap table",
        description:
          "We couldn't load the selected cap table. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    userData,
    currentCapTable,
    proFormaInvestment,
    convertibleNotes,
    sopSummary,
    proFormaVoting,
    saveCapTable,
    deleteCapTable,
    switchCapTable,
  };
};
