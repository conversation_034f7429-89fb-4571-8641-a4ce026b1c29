export interface DocumentCommentRequest {
  comment: string;
  position?: {
    x: number;
    y: number;
  };
  resolved?: boolean;
}

export interface DocumentCommentResponse {
  id: string;
  userId: string;
  companyId: string;
  documentIdentifier: string;
  comment: string;
  statusText: string;
  createdAt: string;
  updatedAt: string;
  position?: {
    x: number;
    y: number;
  };
  resolved: boolean;
  userName?: string;
} 