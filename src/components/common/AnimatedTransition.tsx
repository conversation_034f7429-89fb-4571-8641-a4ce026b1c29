import React from "react";
import { motion } from "framer-motion";

interface AnimatedTransitionProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}

const variants = {
  hidden: {
    opacity: 0,
    y: 10,
  },
  visible: (delay: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: delay,
      duration: 0.5,
      ease: [0.22, 1, 0.36, 1],
    },
  }),
};

const AnimatedTransition: React.FC<AnimatedTransitionProps> = ({
  children,
  className = "",
  delay = 0,
}) => {
  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      custom={delay}
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedTransition;
