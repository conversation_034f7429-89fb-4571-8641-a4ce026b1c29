import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ServiceProviderType } from "@/types/serviceProvider";
import DialogForm from "./dialog/DialogForm";
import DialogConfirmation from "./dialog/DialogConfirmation";
import DialogSuccess from "./dialog/DialogSuccess";
import { useServiceProviderDialog } from "./dialog/useServiceProviderDialog";

interface BaseServiceProviderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  type: ServiceProviderType;
  title: string;
  description: string;
  confirmTitle: string;
  confirmDescription: string;
  successTitle: string;
  successDescription: string;
  renderConfirmation?: (formData: any) => React.ReactNode;
}

const BaseServiceProviderDialog: React.FC<BaseServiceProviderDialogProps> = ({
  isOpen,
  onClose,
  type,
  title,
  description,
  confirmTitle,
  confirmDescription,
  successTitle,
  successDescription,
  renderConfirmation,
}) => {
  const {
    step,
    formData,
    isSubmitting,
    companyEquity,
    handleSubmit,
    handleFinish,
    handleBack,
    resetAndClose,
  } = useServiceProviderDialog(onClose);

  const renderDialogContent = () => {
    switch (step) {
      case "form":
        return (
          <DialogForm
            type={type}
            onSubmit={handleSubmit}
            hasStockOptionPlan={!!companyEquity?.stockOptionPlan}
          />
        );
      case "confirmation":
        return (
          <DialogConfirmation
            formData={formData}
            type={type}
            isSubmitting={isSubmitting}
            onBack={handleBack}
            // Convert the Promise<boolean> to Promise<void> by ignoring the result
            onFinish={async () => {
              await handleFinish();
            }}
            renderConfirmation={renderConfirmation}
          />
        );
      case "success":
        return (
          <DialogSuccess
            title={successTitle}
            description={successDescription}
            hasEquityGrant={formData?.grantType !== "None"}
          />
        );
    }
  };

  const getDialogTitle = () => {
    switch (step) {
      case "form":
        return title;
      case "confirmation":
        return confirmTitle;
      case "success":
        return successTitle;
    }
  };

  const getDialogDescription = () => {
    switch (step) {
      case "form":
        return description;
      case "confirmation":
        return confirmDescription;
      case "success":
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && resetAndClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          {getDialogDescription() && (
            <DialogDescription>{getDialogDescription()}</DialogDescription>
          )}
        </DialogHeader>

        {renderDialogContent()}
      </DialogContent>
    </Dialog>
  );
};

export default BaseServiceProviderDialog;
