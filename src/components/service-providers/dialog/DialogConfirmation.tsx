import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ServiceProviderType } from "@/types/serviceProvider";

interface DialogConfirmationProps {
  formData: any;
  type: ServiceProviderType;
  isSubmitting: boolean;
  onBack: () => void;
  onFinish: () => Promise<void>;
  renderConfirmation?: (formData: any) => React.ReactNode;
}

const DialogConfirmation: React.FC<DialogConfirmationProps> = ({
  formData,
  type,
  isSubmitting,
  onBack,
  onFinish,
  renderConfirmation,
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {renderConfirmation ? (
          renderConfirmation(formData)
        ) : (
          <>
            <h3 className="text-lg font-medium">Confirm {type} Details</h3>
            <div className="border rounded-md p-4 space-y-4">
              <p>
                <strong>Name:</strong> {formData.name}
              </p>
              <p>
                <strong>Email:</strong> {formData.email}
              </p>
              <p>
                <strong>Start Date:</strong>{" "}
                {new Date(formData.startDate).toLocaleDateString()}
              </p>

              {formData.grantType !== "None" && (
                <div className="mt-4">
                  <p className="font-medium">Equity Details:</p>
                  <p>
                    <strong>Type:</strong> {formData.grantType}
                  </p>
                  {formData.optionType && (
                    <p>
                      <strong>Option Type:</strong> {formData.optionType}
                    </p>
                  )}
                  <p>
                    <strong>Shares:</strong> {formData.shares}
                  </p>
                  <p>
                    <strong>Vesting:</strong> {formData.vestingSchedule}
                  </p>
                  {formData.vestingPeriod && (
                    <p>
                      <strong>Vesting Period:</strong> {formData.vestingPeriod}{" "}
                      months
                    </p>
                  )}
                  {formData.cliff && (
                    <p>
                      <strong>Cliff:</strong> {formData.cliff} months
                    </p>
                  )}
                </div>
              )}

              {formData.compensation && (
                <div className="mt-4">
                  <p className="font-medium">Compensation Details:</p>
                  <p>
                    <strong>Amount:</strong> ${formData.compensation}{" "}
                    {formData.compensationPeriod}
                  </p>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      <div className="flex gap-4 justify-end pt-4">
        <Button variant="outline" onClick={onBack} disabled={isSubmitting}>
          Back
        </Button>
        <Button onClick={onFinish} disabled={isSubmitting}>
          {isSubmitting ? "Processing..." : "Finish"}
        </Button>
      </div>
    </div>
  );
};

export default DialogConfirmation;
