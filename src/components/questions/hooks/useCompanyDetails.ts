import { useMemo } from "react";
import {
  QuestionnaireFormData,
  initialFormData,
  OfficerTitle,
  VestingSchedule,
  AccelerationType,
} from "../types";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanyById } from "@/integrations/legal-concierge/hooks/useCompanyInfo";
import { Officer } from "@/integrations/legal-concierge/types/Company";

// Hook to access the company details across the app
export const useCompanyDetails = () => {
  const { user } = useAuth();

  // Fetch company data using react-query
  const { data: companyData, isLoading: loading } = useCompanyById(
    user?.companyId || "",
    !!user?.companyId
  );

  // Transform company data to form data format
  const companyDetails = useMemo(() => {
    if (!companyData) return null;

    const formattedDetails: QuestionnaireFormData = {
      ...initialFormData,
      id: companyData.id || "",
      registerMode: companyData.registerMode || "",
      companyName: companyData.name || "",
      nameAvailabilityChecked: true,
      isFormConfirmed: companyData.isFormConfirmed || false,
      isReviewConfirmed: companyData.isReviewConfirmed || false,
      isSignatureConfirmed: companyData.isSignatureConfirmed || false,
      isSignatureComplete: companyData.isSignatureComplete || false,
      isEinApplyComplete: companyData.isEinApplyComplete || false,
      isOpeningBankAccountComplete:
        companyData.isOpeningBankAccountComplete || false,
      isForeignQualificationToDoBusinessComplete:
        companyData.isForeignQualificationToDoBusinessComplete || false,
      isPostIncorporationConfirmed:
        companyData.isPostIncorporationConfirmed || false,
      incorporationDate: companyData.incorporationDate
        ? new Date(companyData.incorporationDate)
        : null,
      plannedIncorporationDate: null,
      incorporatingToday: companyData.isIncorporatedToday || false,
      companyAddressStructured: {
        street1: companyData.streetAddress || "",
        street2: companyData.streetAddress2 || "",
        city: companyData.city || "",
        state: companyData.state || "",
        zipCode: companyData.zipCode || "",
      },
      authorizedShares:
        companyData.numberOfAuthorizedShares ||
        initialFormData.authorizedShares,
      issuedShares:
        companyData.numberOfAuthorizedSharesToBeIssuedInitially ||
        initialFormData.issuedShares,
      parValuePerShare:
        companyData.parValuePerShare || initialFormData.parValuePerShare,
      stockOptionPlanPercentage:
        companyData.stockOptionPlanPercentage ||
        initialFormData.stockOptionPlanPercentage,
      includeStockOptionPlan: companyData.includeStockOptionPlan || false,
      registeredAgent: companyData.registeredAgent
        ? {
            id: companyData.registeredAgent.id,
            name: companyData.registeredAgent.name,
            rate: companyData.registeredAgent.rate,
            description: companyData.registeredAgent.description,
            rateDescription: companyData.registeredAgent.rateDescription,
          }
        : null,
    };

    // Add officers as directors if available
    if (companyData.officers && companyData.officers.length > 0) {
      formattedDetails.directors = companyData.officers.map((officer) => {
        const officerTitles: OfficerTitle[] = [];
        if (officer.isCEO) officerTitles.push("CEO");
        if (officer.isPresident) officerTitles.push("President");
        if (officer.isSecretary) officerTitles.push("Secretary");
        if (officer.isTreasurer) officerTitles.push("Treasurer");
        if (officer.notApplicable) officerTitles.push("Not Applicable");

        return {
          ...officer,
          officerTitles:
            officerTitles.length > 0
              ? officerTitles
              : (["Not Applicable"] as OfficerTitle[]),
        };
      }) as Officer[];

      // Map vesting info
      formattedDetails.vestingInfo = companyData.officers
        .filter((officer) => officer.isVester)
        .map((officer) => {
          let vestingSchedule: VestingSchedule | null = null;
          if (officer.vestingSchedule === "FOURYEARSONEYEARCLIFF")
            vestingSchedule = "Standard";
          if (officer.vestingSchedule === "FOURYEARSNOCLIFF")
            vestingSchedule = "Monthly";

          let acceleration: AccelerationType | null = null;
          if (officer.accleration === "SINGLETRIGGER")
            acceleration = "SingleTrigger";
          if (officer.accleration === "DOUBLETRIGGER")
            acceleration = "DoubleTrigger";
          if (officer.accleration === "NONE") acceleration = "None";

          return {
            id: officer.id,
            isVester: officer.isVester,
            vestingSchedule,
            acceleration,
          };
        });

      // Map tech assignments
      formattedDetails.founderTechAssignments = companyData.officers.map(
        (officer) => ({
          founderId: officer.id,
          founderName: officer.name,
          techItems: officer.intellectualProperties.map((ip) => ({
            id: ip.id,
            description: ip.name,
            isExcluded: ip.isExcluded,
          })),
        })
      );
    }

    return formattedDetails;
  }, [companyData]);

  return { companyDetails, loading };
};
