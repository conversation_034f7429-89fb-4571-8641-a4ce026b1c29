import { useEffect } from "react";
import { QuestionnaireFormData } from "../types";

export const useVestingSync = (
  formData: QuestionnaireFormData,
  setFormData: (
    updater: (prev: QuestionnaireFormData) => QuestionnaireFormData
  ) => void
) => {
  // Update vesting info when directors change
  useEffect(() => {
    // Create vesting entries for each director if they don't exist
    const updatedVestingInfo = [...formData.vestingInfo];
    formData.directors.forEach((director) => {
      const existingVestingEntry = formData.vestingInfo.find(
        (v) => v.id === director.id
      );
      if (!existingVestingEntry) {
        // Use embedded vesting data from director if available, otherwise use defaults
        updatedVestingInfo.push({
          id: director.id,
          isVester: director.isVester || false,
          vestingSchedule: director.vestingSchedule || null,
          acceleration: director.acceleration || null,
        });
      }
    });

    // Remove vesting entries that no longer have corresponding directors
    const filteredVestingInfo = updatedVestingInfo.filter((vestingEntry) =>
      formData.directors.some((director) => director.id === vestingEntry.id)
    );

    if (
      JSON.stringify(filteredVestingInfo) !==
      JSON.stringify(formData.vestingInfo)
    ) {
      setFormData((prev) => ({
        ...prev,
        vestingInfo: filteredVestingInfo,
      }));
    }
  }, [formData.directors, formData.vestingInfo, setFormData]);

  return {};
};
