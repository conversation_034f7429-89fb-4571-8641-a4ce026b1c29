import React, { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";
import { useSelectCompany } from "@/integrations/legal-concierge/hooks/useCompanies";
import { CompanySwitcherDialog } from "@/components/common/CompanySwitcherDialog";
import { Spinner } from "@/components/ui/spinner";
import Layout from "@/components/Layout";

interface CompanySelectionGuardProps {
  children: React.ReactNode;
}

/**
 * Component that ensures a company is selected before allowing access to protected content.
 * This component handles the company selection flow and blocks role-based access control
 * until a company context is established.
 */
const CompanySelectionGuard: React.FC<CompanySelectionGuardProps> = ({ children }) => {
  const { user, handleAuthChange } = useAuth();
  const {
    isCompanySelected,
    isCompanySelectionLoading,
    needsCompanySelection,
    hasMultipleCompanies,
    companies,
    markCompanySelected,
  } = useCompanySelection();
  const { mutate: selectCompany } = useSelectCompany();
  const [showCompanyDialog, setShowCompanyDialog] = useState(false);
  const [isSelecting, setIsSelecting] = useState(false);

  // Handle automatic company selection for single company users
  useEffect(() => {
    if (isCompanySelectionLoading || !needsCompanySelection) return;

    if (companies.length === 1 && !hasMultipleCompanies) {
      // Auto-select the only company
      setIsSelecting(true);
      selectCompany(companies[0].companyId, {
        onSuccess: async () => {
          await handleAuthChange(true);
          markCompanySelected();
          setIsSelecting(false);
        },
        onError: () => {
          setIsSelecting(false);
        },
      });
    } else if (companies.length > 1) {
      // Show company selection dialog for multiple companies
      setShowCompanyDialog(true);
    }
  }, [
    isCompanySelectionLoading,
    needsCompanySelection,
    companies,
    hasMultipleCompanies,
    selectCompany,
    handleAuthChange,
    markCompanySelected,
  ]);

  // Handle manual company selection
  const handleCompanySelect = (companyId: string) => {
    setIsSelecting(true);
    selectCompany(companyId, {
      onSuccess: async () => {
        await handleAuthChange(true);
        markCompanySelected();
        setShowCompanyDialog(false);
        setIsSelecting(false);
      },
      onError: () => {
        setIsSelecting(false);
      },
    });
  };

  // Show loading state while company selection is in progress
  if (isCompanySelectionLoading || isSelecting) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col justify-center items-center">
            <Spinner size="lg" />
            <p className="text-gray-600 mt-4">
              {isSelecting ? "Selecting company..." : "Loading company information..."}
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  // Show company selection dialog if needed
  if (needsCompanySelection && !isCompanySelected) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-6">
            <h1 className="text-2xl font-bold text-gray-900">
              Select Company
            </h1>
            <p className="text-gray-600 max-w-md">
              Please select a company to continue. Your role and permissions will be determined based on your access level within the selected company.
            </p>
            <CompanySwitcherDialog
              open={showCompanyDialog || companies.length > 1}
              companies={companies}
              onSelect={handleCompanySelect}
              isClosable={false}
              onClose={null}
            />
          </div>
        </div>
      </Layout>
    );
  }

  // Company is selected, render protected content
  return <>{children}</>;
};

export default CompanySelectionGuard;
