import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import {
  AuthorizedSharesRequest,
  CompanyAddressRequest,
  CompanyNameRequest,
} from "../types/Company";
import { QueryKey } from "../keys";

const api = new APIClient();

// Company information hooks
export const useCompanyById = (id: string, enabled = true) => {
  return useQuery({
    queryKey: [QueryKey().company, id],
    queryFn: () => api.getCompanyById(id),
    enabled: !!id && enabled,
  });
};

export const useUpdateCompanyName = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, ...payload }: { id: string } & CompanyNameRequest) =>
      api.updateCompanyName(id, payload),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.id],
      });
      queryClient.invalidateQueries({ queryKey: [QueryKey().companies] });
    },
  });
};

export const useUpdateCompanyAddress = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, ...payload }: { id: string } & CompanyAddressRequest) =>
      api.updateCompanyAddress(id, payload),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.id],
      });
    },
  });
};

export const useUpdateAuthorizedShares = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      ...payload
    }: { id: string } & AuthorizedSharesRequest) =>
      api.updateAuthorizedShares(id, payload),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.id],
      });
    },
  });
};

export const useConfirmCompanyForm = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => api.confirmCompanyForm(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, id],
      });
    },
  });
};
