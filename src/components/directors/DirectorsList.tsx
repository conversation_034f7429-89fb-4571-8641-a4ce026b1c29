import React, { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { Director } from "./types";
import DirectorsTable from "./DirectorsTable";
import ResignationDialog from "./ResignationDialog";
import EmptyDirectorsState from "./EmptyDirectorsState";

interface DirectorsListProps {
  directors: Director[];
  onUpdateDirector: (id: string, updates: Partial<Director>) => void;
  onUploadResignation: (id: string, file: File) => Promise<boolean>;
}

const DirectorsList: React.FC<DirectorsListProps> = ({
  directors,
  onUpdateDirector,
  onUploadResignation,
}) => {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [resignDialogOpen, setResignDialogOpen] = useState(false);
  const [currentDirectorId, setCurrentDirectorId] = useState<string | null>(
    null
  );
  const [resignationFile, setResignationFile] = useState<File | null>(null);

  const handleEditClick = (director: Director) => {
    setEditingId(director.id);
  };

  const handleSaveClick = (id: string) => {
    setEditingId(null);
    toast({
      title: "Changes Saved",
      description: "Director information has been updated.",
    });
  };

  const handleDateChange = (id: string, date: Date | undefined) => {
    if (date) {
      onUpdateDirector(id, { appointmentDate: date });
    }
  };

  const handleResignationDateChange = (id: string, date: Date | undefined) => {
    if (date) {
      onUpdateDirector(id, { resignationDate: date });
    }
  };

  const handleResignedChange = (id: string) => {
    const director = directors.find((d) => d.id === id);
    if (director) {
      if (!director.resigned) {
        // If changing to resigned, open dialog for letter upload
        setCurrentDirectorId(id);
        setResignDialogOpen(true);
      } else {
        // If changing from resigned to not resigned
        onUpdateDirector(id, {
          resigned: false,
          resignationDate: null,
        });
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setResignationFile(e.target.files[0]);
    }
  };

  const handleUploadResignation = async () => {
    if (currentDirectorId && resignationFile) {
      const success = await onUploadResignation(
        currentDirectorId,
        resignationFile
      );

      if (success) {
        onUpdateDirector(currentDirectorId, {
          resigned: true,
          resignationDate: new Date(),
          resignationLetter: resignationFile,
        });

        setResignDialogOpen(false);
        setResignationFile(null);

        toast({
          title: "Resignation Letter Uploaded",
          description: "Director has been marked as resigned.",
        });
      }
    }
  };

  if (directors.length === 0) {
    return <EmptyDirectorsState />;
  }

  return (
    <>
      <DirectorsTable
        directors={directors}
        editingId={editingId}
        onEditClick={handleEditClick}
        onSaveClick={handleSaveClick}
        onDateChange={handleDateChange}
        onResignationDateChange={handleResignationDateChange}
        onResignedChange={handleResignedChange}
      />

      <ResignationDialog
        open={resignDialogOpen}
        onOpenChange={setResignDialogOpen}
        onUploadResignation={handleUploadResignation}
        onFileChange={handleFileChange}
        resignationFile={resignationFile}
      />
    </>
  );
};

export default DirectorsList;
