import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collaborator } from "./types";
import { formatDistanceToNow } from "date-fns";
import { User, Clock, MoreVertical } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/AuthContext";

interface CollaboratorCardProps {
  collaborator: Collaborator;
  onRemove: (id: string) => void;
  onChangeRole: (id: string, role: Collaborator["role"]) => void;
}

const CollaboratorCard: React.FC<CollaboratorCardProps> = ({
  collaborator,
  onRemove,
  onChangeRole,
}) => {
  // Extract properties with fallbacks
  const {
    name,
    fullName = null,
    email,
    role,
    status,
    dateAdded,
    lastActive,
    activity,
  } = collaborator;

  // Use fullName if available, fallback to name, or "N/A" if both are null
  const displayName = fullName || name || "N/A";
  const { user } = useAuth();

  const getRoleBadgeColor = (role: Collaborator["role"]) => {
    const normalizedRole = role?.toLowerCase();
    switch (normalizedRole) {
      case "admin":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "collaborator":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "viewer":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusBadgeColor = (status: Collaborator["status"]) => {
    const normalizedStatus = status?.toLowerCase();
    switch (normalizedStatus) {
      case "accepted":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "declined":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-100 rounded-full p-2">
              <User className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{displayName}</h3>
              <p className="text-sm text-gray-500">{email}</p>
            </div>
          </div>

          
              {
                user?.roles?.includes('OWNER') &&  
                <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="p-1 rounded-full hover:bg-gray-100">
                <MoreVertical className="h-4 w-4 text-gray-500" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              {/* Commenting out role change options for now
               */}
              {/* <DropdownMenuItem
                onClick={() => onChangeRole(collaborator.id, "admin")}
              >
                Make Admin
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onChangeRole(collaborator.id, "collaborator")}
              >
                Make Collaborator
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onChangeRole(collaborator.id, "viewer")}
              >
                Make Viewer
              </DropdownMenuItem> */}
                <DropdownMenuItem
                onClick={() => onRemove(collaborator.id)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                Remove
              </DropdownMenuItem>
              </DropdownMenuContent>
              </DropdownMenu>
              }
             
            

        </div>

        <div className="flex mt-3 space-x-2">
          <Badge variant="outline" className={getRoleBadgeColor(role)}>
            {role
              ? typeof role === "string"
                ? role.charAt(0).toUpperCase() + role.toLowerCase().slice(1)
                : "Unknown"
              : "Unknown"}
          </Badge>
          <Badge variant="outline" className={getStatusBadgeColor(status)}>
            {status
              ? typeof status === "string"
                ? status.charAt(0).toUpperCase() + status.toLowerCase().slice(1)
                : "Unknown"
              : "Unknown"}
          </Badge>
        </div>

        <div className="flex items-center mt-3 text-xs text-gray-500">
          <Clock className="h-3 w-3 mr-1" />
          {activity
            ? activity
            : lastActive
              ? `Last active ${formatDistanceToNow(lastActive, { addSuffix: true })}`
              : dateAdded
                ? `Added ${formatDistanceToNow(dateAdded, { addSuffix: true })}`
                : "Recently added"}
        </div>
      </CardContent>
    </Card>
  );
};

export default CollaboratorCard;
