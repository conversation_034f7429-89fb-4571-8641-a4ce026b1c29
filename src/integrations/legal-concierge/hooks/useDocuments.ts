import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { QueryKey } from "../keys";
import { Document } from "../types/Document";

const api = new APIClient();

// Type guard to check if response has an error
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// Get all documents for a company
export const useCompanyDocuments = (companyId: string, enabled = true) => {
  return useQuery({
    queryKey: [QueryKey().company, companyId, QueryKey().documents],
    queryFn: async () => {
      const response = await api.getCompanyDocuments(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data as Document[];
    },
    enabled: !!companyId && enabled,
  });
};

// Generate document for a company
export const useGenerateCompanyDocument = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (companyId: string) => {
      const response = await api.generateCompanyDocument(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (_, companyId) => {
      // Invalidate company documents query
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId, QueryKey().documents],
      });
    },
  });
};

// Get document token for viewing
export const useDocumentToken = () => {
  return useMutation({
    mutationFn: async (payload: { documentId: string, companyId: string }) => {
      const response = await api.getDocumentToken(payload);
      console.log("Document token response from mutation", response);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
  });
};

// Document server callback
export const useDocumentCallback = () => {
  return useMutation({
    mutationFn: async (payload: Record<string, unknown>) => {
      const response = await api.documentCallback(payload);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
  });
};
