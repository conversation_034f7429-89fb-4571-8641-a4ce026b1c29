import { <PERSON>lugin, Plugin<PERSON><PERSON> } from "@tiptap/pm/state";
import { Decoration, DecorationSet } from "@tiptap/pm/view";
import { Extension, Mark, mergeAttributes } from "@tiptap/core";

// Constants
const MARK_INSERTION = "insertion";
const MARK_DELETION = "deletion";
const EXTENSION_NAME = "trackchange";

// Data structures for tracking changes
class Span {
  constructor(
    public from: number,
    public to: number,
    public commit: number | null
  ) {}
}

class Commit {
  constructor(
    public message: string,
    public time: Date,
    public steps: any[],
    public maps: any[],
    public hidden?: boolean,
    public deletions?: Array<{ pos: number; content: string }>
  ) {}
}

class TrackState {
  constructor(
    public blameMap: Span[],
    public commits: Commit[],
    public uncommittedSteps: any[],
    public uncommittedMaps: any[]
  ) {}

  // Apply a transform to this state
  applyTransform(transform: any): TrackState {
    // Invert the steps in the transaction, to be able to save them in the next commit
    const inverted = transform.steps.map((step: any, i: number) =>
      step.invert(transform.docs[i])
    );
    const newBlame = updateBlameMap(
      this.blameMap,
      transform,
      this.commits.length
    );
    // Create a new state—since these are part of the editor state, a
    // persistent data structure, they must not be mutated.
    return new TrackState(
      newBlame,
      this.commits,
      this.uncommittedSteps.concat(inverted),
      this.uncommittedMaps.concat(transform.mapping.maps)
    );
  }

  // When a transaction is marked as a commit, this is used to put any
  // uncommitted steps into a new commit.
  applyCommit(message: string, time: Date): TrackState {
    if (this.uncommittedSteps.length == 0) return this;

    // Extract deletion information from the steps
    const deletions: Array<{ pos: number; content: string }> = [];
    this.uncommittedSteps.forEach((step) => {
      if (step.jsonID === "replace" && step.slice && step.slice.content) {
        // This step represents a replacement that includes deletions
        const deletedContent = step.slice.content;
        if (deletedContent.size > 0) {
          // Extract text content from the deleted slice
          let textContent = "";
          deletedContent.forEach((node: any) => {
            if (node.textContent) {
              textContent += node.textContent;
            } else if (node.text) {
              textContent += node.text;
            }
          });
          if (textContent) {
            deletions.push({
              pos: step.from,
              content: textContent,
            });
          }
        }
      }
    });

    const commit = new Commit(
      message,
      time,
      this.uncommittedSteps,
      this.uncommittedMaps,
      false,
      deletions
    );
    return new TrackState(this.blameMap, this.commits.concat(commit), [], []);
  }
}

function updateBlameMap(map: Span[], transform: any, id: number): Span[] {
  const result: Span[] = [];
  const mapping = transform.mapping;
  for (let i = 0; i < map.length; i++) {
    const span = map[i];
    const from = mapping.map(span.from, 1),
      to = mapping.map(span.to, -1);
    if (from < to) result.push(new Span(from, to, span.commit));
  }

  for (let i = 0; i < mapping.maps.length; i++) {
    const map = mapping.maps[i],
      after = mapping.slice(i + 1);
    map.forEach((_s: any, _e: any, start: number, end: number) => {
      insertIntoBlameMap(result, after.map(start, 1), after.map(end, -1), id);
    });
  }

  return result;
}

function insertIntoBlameMap(
  map: Span[],
  from: number,
  to: number,
  commit: number
): void {
  if (from >= to) return;
  let pos = 0;
  let next: Span;
  for (; pos < map.length; pos++) {
    next = map[pos];
    if (next.commit == commit) {
      if (next.to >= from) break;
    } else if (next.to > from) {
      // Different commit, not before
      if (next.from < from) {
        // Sticks out to the left (loop below will handle right side)
        const left = new Span(next.from, from, next.commit);
        if (next.to > to) map.splice(pos++, 0, left);
        else map[pos++] = left;
      }
      break;
    }
  }

  while ((next = map[pos])) {
    if (next.commit == commit) {
      if (next.from > to) break;
      from = Math.min(from, next.from);
      to = Math.max(to, next.to);
      map.splice(pos, 1);
    } else {
      if (next.from >= to) break;
      if (next.to > to) {
        map[pos] = new Span(to, next.to, next.commit);
        break;
      } else {
        map.splice(pos, 1);
      }
    }
  }

  map.splice(pos, 0, new Span(from, to, commit));
}

// Plugin key for the track changes plugin
const trackPluginKey = new PluginKey("trackChanges");

// Main tracking plugin
const trackPlugin = new Plugin({
  key: trackPluginKey,
  state: {
    init(_, instance) {
      return new TrackState(
        [new Span(0, instance.doc.content.size, null)],
        [],
        [],
        []
      );
    },
    apply(tr, tracked) {
      if (tr.docChanged) tracked = tracked.applyTransform(tr);
      const commitMessage = tr.getMeta(trackPluginKey);
      if (commitMessage)
        tracked = tracked.applyCommit(commitMessage, new Date(tr.time));
      return tracked;
    },
  },
});

// Plugin for highlighting changes
const highlightPluginKey = new PluginKey("highlightChanges");

// Helper function to calculate diff between two commits
function calculateCommitDiff(trackState: TrackState, currentCommit: Commit) {
  const commits = trackState.commits;
  const currentIndex = commits.indexOf(currentCommit);

  if (currentIndex === -1) return [];

  const decorations: any[] = [];

  // Show only additions for the selected commit (content that belongs to this commit only)
  const currentSpans = trackState.blameMap.filter(
    (span) => span.commit === currentIndex
  );

  // Show additions (content that belongs to current commit)
  currentSpans.forEach((span) => {
    decorations.push(
      Decoration.inline(span.from, span.to, {
        class: "commit-diff-addition",
        "data-commit-message": currentCommit.message,
        "data-commit-time": currentCommit.time.toISOString(),
      })
    );
  });

  // Show deletions for this commit
  if (currentCommit.deletions && currentCommit.deletions.length > 0) {
    currentCommit.deletions.forEach((deletion) => {
      decorations.push(
        Decoration.widget(deletion.pos, () => {
          const span = document.createElement("span");
          span.className = "commit-diff-deletion";
          span.setAttribute("data-commit-message", currentCommit.message);
          span.setAttribute(
            "data-commit-time",
            currentCommit.time.toISOString()
          );
          span.textContent = deletion.content;
          span.title = `Deleted: "${deletion.content}"`;
          return span;
        })
      );
    });
  }

  return decorations;
}

const highlightPlugin = new Plugin({
  key: highlightPluginKey,
  state: {
    init() {
      return { deco: DecorationSet.empty, commit: null };
    },
    apply(tr, prev, oldState, state) {
      const highlight = tr.getMeta(highlightPluginKey);
      if (highlight && highlight.add != null && prev.commit != highlight.add) {
        const tState = trackPlugin.getState(oldState);
        const decos = calculateCommitDiff(tState, highlight.add);
        return {
          deco: DecorationSet.create(state.doc, decos),
          commit: highlight.add,
        };
      } else if (
        highlight &&
        highlight.clear != null &&
        prev.commit == highlight.clear
      ) {
        return { deco: DecorationSet.empty, commit: null };
      } else if (tr.docChanged && prev.commit) {
        return { deco: prev.deco.map(tr.mapping, tr.doc), commit: prev.commit };
      } else {
        return prev;
      }
    },
  },
  props: {
    decorations(state) {
      return this.getState(state).deco;
    },
  },
});

// Insertion mark for tracking additions
export const InsertionMark = Mark.create({
  name: MARK_INSERTION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "ins" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "ins",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #d4edda; text-decoration: none;",
      }),
      0,
    ];
  },
});

// Deletion mark for tracking deletions
export const DeletionMark = Mark.create({
  name: MARK_DELETION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "del" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "del",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #f8d7da; text-decoration: line-through;",
      }),
      0,
    ];
  },
});

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    trackchange: {
      /**
       * Commit current changes with a message
       */
      commitChanges: (message: string) => ReturnType;
      /**
       * Check if there are uncommitted changes
       */
      hasUncommittedChanges: () => ReturnType;
      /**
       * Get the current track state
       */
      getTrackState: () => ReturnType;
      /**
       * Highlight a specific commit
       */
      highlightCommit: (commit: Commit | null) => ReturnType;
      /**
       * Save document (alias for commitChanges)
       */
      saveDocument: (message?: string) => ReturnType;
    };
  }
}

// Main extension
export const TrackChangeExtension = Extension.create<{
  enabled: boolean;
  userId?: string;
  userName?: string;
}>({
  name: EXTENSION_NAME,

  addOptions() {
    return {
      enabled: true,
      userId: "",
      userName: "",
    };
  },

  addExtensions() {
    return [InsertionMark, DeletionMark];
  },

  addProseMirrorPlugins() {
    return [trackPlugin, highlightPlugin];
  },

  addCommands() {
    return {
      commitChanges:
        (message: string) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(trackPluginKey, message);
            dispatch(tr);
          }
          return true;
        },

      hasUncommittedChanges:
        () =>
        ({ state }) => {
          const trackState = trackPlugin.getState(state);
          return trackState?.uncommittedSteps.length > 0;
        },

      getTrackState:
        () =>
        ({ state, editor }) => {
          const trackState = trackPlugin.getState(state);
          // Store the track state in the editor's storage for external access
          if (editor && trackState) {
            editor.storage.trackchange.currentTrackState = trackState;
          }
          return true;
        },

      highlightCommit:
        (commit: Commit | null) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            if (commit) {
              tr.setMeta(highlightPluginKey, { add: commit });
            } else {
              tr.setMeta(highlightPluginKey, { clear: true });
            }
            dispatch(tr);
          }
          return true;
        },

      saveDocument:
        (message?: string) =>
        ({ tr, dispatch, state }) => {
          const trackState = trackPlugin.getState(state);
          if (trackState?.uncommittedSteps.length > 0) {
            if (dispatch) {
              tr.setMeta(trackPluginKey, message || "Document saved");
              dispatch(tr);
            }
            return true;
          }
          return false;
        },
    };
  },

  addStorage() {
    return {
      hasUncommittedChanges: false,
      currentTrackState: null,
    };
  },

  onTransaction({ editor }) {
    // Update storage to track if there are uncommitted changes
    const trackState = trackPlugin.getState(editor.state);
    this.storage.hasUncommittedChanges =
      trackState?.uncommittedSteps.length > 0;
  },
});

// Export types and classes for external use
export { Commit, TrackState, Span };

// Export utility functions
export * from "./utils";

export default TrackChangeExtension;
