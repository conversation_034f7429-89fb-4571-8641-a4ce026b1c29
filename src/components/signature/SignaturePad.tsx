import React, { useRef, useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

interface SignaturePadProps {
  onSave: (signatureData: string) => void;
  width?: number;
  height?: number;
}

const SignaturePad: React.FC<SignaturePadProps> = ({
  onSave,
  width = 500,
  height = 200,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null);
  const [hasSignature, setHasSignature] = useState(false);

  useEffect(() => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const context = canvas.getContext("2d");

      if (context) {
        context.lineWidth = 2;
        context.lineCap = "round";
        context.strokeStyle = "#000000";
        setCtx(context);

        // Set canvas to be responsive
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        // Clear the canvas
        context.fillStyle = "#ffffff";
        context.fillRect(0, 0, canvas.width, canvas.height);
      }
    }
  }, []);

  const handleStartDrawing = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ) => {
    setIsDrawing(true);
    if (ctx && canvasRef.current) {
      ctx.beginPath();

      // Get position based on event type
      const pos = getEventPosition(e);
      if (pos) {
        ctx.moveTo(pos.x, pos.y);
      }
    }
  };

  const handleDraw = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ) => {
    if (!isDrawing || !ctx) return;

    // Prevent scrolling on touch devices
    e.preventDefault();

    const pos = getEventPosition(e);
    if (pos) {
      ctx.lineTo(pos.x, pos.y);
      ctx.stroke();
      setHasSignature(true);
    }
  };

  const handleEndDrawing = () => {
    if (ctx) {
      ctx.closePath();
    }
    setIsDrawing(false);
  };

  const getEventPosition = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ) => {
    if (!canvasRef.current) return null;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();

    // Handle mouse event
    if ("clientX" in e) {
      return {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      };
    }

    // Handle touch event
    if (e.touches && e.touches.length > 0) {
      return {
        x: e.touches[0].clientX - rect.left,
        y: e.touches[0].clientY - rect.top,
      };
    }

    return null;
  };

  const clearSignature = () => {
    if (ctx && canvasRef.current) {
      ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
      ctx.fillStyle = "#ffffff";
      ctx.fillRect(0, 0, canvasRef.current.width, canvasRef.current.height);
      setHasSignature(false);
    }
  };

  const saveSignature = () => {
    if (canvasRef.current && hasSignature) {
      const signatureData = canvasRef.current.toDataURL("image/png");
      onSave(signatureData);
    }
  };

  return (
    <div className="flex flex-col items-center">
      <div className="border border-gray-300 rounded-md bg-white mb-4 touch-none">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          className="cursor-crosshair touch-none"
          onMouseDown={handleStartDrawing}
          onMouseMove={handleDraw}
          onMouseUp={handleEndDrawing}
          onMouseLeave={handleEndDrawing}
          onTouchStart={handleStartDrawing}
          onTouchMove={handleDraw}
          onTouchEnd={handleEndDrawing}
        />
      </div>
      <div className="flex space-x-4">
        <Button
          variant="outline"
          type="button"
          onClick={clearSignature}
          className="w-32"
          size="sm"
        >
          <RefreshCw size={16} className="mr-2" />
          Clear
        </Button>
        <Button
          type="button"
          onClick={saveSignature}
          className="w-32"
          size="sm"
          disabled={!hasSignature}
        >
          Save Signature
        </Button>
      </div>
      <p className="text-xs text-gray-500 mt-2">
        Draw your signature using your mouse or finger if on a touchscreen
      </p>
    </div>
  );
};

export default SignaturePad;
