import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip } from "recharts";
import { Shareholder } from "@/types/capTable";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Info } from "lucide-react";
import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";

interface CapTableChartProps {
  shareholders: Shareholder[];
  currentView: "standard" | "fullyDiluted";
}

const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

const CapTableChart: React.FC<CapTableChartProps> = ({
  shareholders,
  currentView,
}) => {
  // Prepare data for chart based on the current view
  const chartData = shareholders
    .filter((s) => {
      // For standard view, only include shareholders with actual shares
      if (currentView === "standard") {
        return s.percentage > 0;
      }
      // For fully diluted view, include all with any ownership
      return s.fullyDilutedPercentage > 0;
    })
    .map((shareholder, index) => ({
      name: shareholder.name,
      value:
        currentView === "standard"
          ? shareholder.percentage
          : shareholder.fullyDilutedPercentage,
      color: COLORS[index % COLORS.length],
      role: shareholder.role,
    }));

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 shadow-lg rounded-lg border">
          <p className="font-medium text-gray-800">{payload[0].payload.name}</p>
          <p className="text-sm text-gray-600">{payload[0].payload.role}</p>
          <p className="text-sm font-medium">{`Ownership: ${payload[0].value.toFixed(2)}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            {currentView === "standard"
              ? "Ownership Distribution"
              : "Fully Diluted Ownership"}
          </CardTitle>
          <TooltipProvider>
            <UITooltip>
              <TooltipTrigger asChild>
                <button className="text-gray-500 hover:text-gray-700">
                  <Info size={18} />
                </button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>
                  {currentView === "standard"
                    ? "Shows the current ownership based on issued shares only."
                    : "Shows ownership if all options and convertible securities are exercised."}
                </p>
              </TooltipContent>
            </UITooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                fill="#8884d8"
                paddingAngle={1}
                dataKey="value"
                labelLine={false}
                label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
          {chartData.map((entry, index) => (
            <div key={`legend-${index}`} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm truncate">
                {entry.name}: {entry.value.toFixed(1)}%
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default CapTableChart;
