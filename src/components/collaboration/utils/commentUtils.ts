/**
 * Safely parse position data from Supabase JSON field into the expected format
 */
export const parsePositionData = (
  positionData: any
): { x: number; y: number } | undefined => {
  try {
    // Handle case where position is null or undefined
    if (!positionData) return undefined;

    // Handle case where position is already an object with x and y properties
    if (
      typeof positionData === "object" &&
      positionData !== null &&
      typeof positionData.x === "number" &&
      typeof positionData.y === "number"
    ) {
      return { x: positionData.x, y: positionData.y };
    }

    // Handle JSON string case
    if (typeof positionData === "string") {
      try {
        const parsed = JSON.parse(positionData);
        if (typeof parsed.x === "number" && typeof parsed.y === "number") {
          return { x: parsed.x, y: parsed.y };
        }
      } catch (e) {
        // Invalid JSON string, ignore
      }
    }

    // Default fallback if we can't parse as expected
    return undefined;
  } catch (error) {
    console.error("Error parsing position data:", error);
    return undefined;
  }
};
