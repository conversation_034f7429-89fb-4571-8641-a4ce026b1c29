import React, { useEffect, useState } from "react";
import {
  QuestionnaireFormData,
  VestingInfo,
  VestingSchedule,
  AccelerationType,
} from "../types";
import {
  VestingRequest,
  VestingUnit,
  VestingSchedule as ApiVestingSchedule,
  VestingAcceleration,
} from "@/integrations/legal-concierge/types/Stocks";
import VestingIntro from "./vesting/VestingIntro";
import VestingTable from "./vesting/VestingTable";
import VestingCharts from "./vesting/VestingCharts";
import Section83bInfo from "./vesting/Section83bInfo";
import {
  calculateShares,
  getScheduleLabel,
  getAccelerationLabel,
} from "./vesting/VestingHelpers";
import { toast } from "sonner";

interface VestingStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: unknown) => void;
}

const mapVestingSchedule = (schedule: string | null): ApiVestingSchedule => {
  if (schedule === "Standard") return "FOURYEARSONEYEARCLIFF";
  if (schedule === "Monthly") return "FOURYEARSNOCLIFF";
  return "FOURYEARSONEYEARCLIFF"; // Default
};
// Map from app acceleration type to API acceleration type
const mapAcceleration = (acceleration: string | null): VestingAcceleration => {
  if (acceleration === "SingleTrigger") return "SINGLETRIGGER";
  if (acceleration === "DoubleTrigger") return "DOUBLETRIGGER";
  if (acceleration === "None") return "NONE";
  return "NONE"; // Default
};

// Export the save function so it can be called from the continue button
export const saveVestingData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  updateVesting: any
): Promise<boolean> => {
  try {
    // Prepare vesting data for officers
    const vestingUnits = formData.directors.map((director) => {
      const vestingInfo = formData.vestingInfo.find(
        (v) => v.id === director.id
      );
      return {
        officerId: director.id,
        isVester: vestingInfo?.isVester || false,
        vestingSchedule: mapVestingSchedule(vestingInfo?.vestingSchedule || null),
        acceleration: mapAcceleration(vestingInfo?.acceleration || null),
      };
    });

    const payload = {
      id: companyId,
      companyId: companyId,
      isVestingConfirmed: formData.isVestingConfirmed,
      vestingUnits,
    };

    console.log("Saving vesting data with payload:", payload);

    await updateVesting.mutateAsync(payload);

    console.log("Vesting data saved successfully");
    toast.success("Vesting information saved successfully");
    return true;
  } catch (error) {
    console.error("Error saving vesting data:", error);
    toast.error("Failed to save vesting information");
    return false;
  }
};

const VestingStep: React.FC<VestingStepProps> = ({
  formData,
  updateFormData,
}) => {
  const [showSection83bInfo, setShowSection83bInfo] = useState(false);

  // Update vesting field for a specific person
  const updateVestingField = (id: string, field: string, value: unknown) => {
    console.log(
      `updateVestingField called with id: ${id}, field: ${field}, value: ${value}`
    );

    // First, check if the vesting info exists for this director
    const existingInfo = formData.vestingInfo.find((info) => info.id === id);
    console.log(`Existing info found: ${!!existingInfo}`);

    let updatedVestingInfo: typeof formData.vestingInfo;

    if (existingInfo) {
      // If it exists, update it
      updatedVestingInfo = formData.vestingInfo.map((info) => {
        if (info.id === id) {
          // Special case for checkbox: if this is a isVester field and it's being set to true,
          // also set default values for vestingSchedule and acceleration if they're not already set
          if (field === "isVester" && value === true) {
            const updatedInfo = {
              ...info,
              isVester: true,
              vestingSchedule: info.vestingSchedule || "Standard",
              acceleration: info.acceleration || "None",
            };
            console.log(`Updated info for ${id} with defaults:`, updatedInfo);
            return updatedInfo;
          } else {
            // Normal case: just update the specified field
            const updatedInfo = {
              ...info,
              [field]: value,
            };
            console.log(`Updated info for ${id}:`, updatedInfo);
            return updatedInfo;
          }
        }
        return info;
      });
    } else {
      // If it doesn't exist, create a new entry
      // Special case for checkbox: if this is a isVester field and it's being set to true,
      // also set default values for vestingSchedule and acceleration
      if (field === "isVester" && value === true) {
        const newVestingInfo: VestingInfo = {
          id: id,
          isVester: true,
          vestingSchedule: "Standard" as VestingSchedule,
          acceleration: "None" as AccelerationType,
        };
        console.log(
          `Created new vesting info for ${id} with defaults:`,
          newVestingInfo
        );
        updatedVestingInfo = [...formData.vestingInfo, newVestingInfo];
      } else {
        // Normal case: just set the specified field
        const newVestingInfo: VestingInfo = {
          id: id,
          isVester: field === "isVester" ? Boolean(value) : false,
          vestingSchedule:
            field === "vestingSchedule" ? (value as VestingSchedule) : null,
          acceleration:
            field === "acceleration" ? (value as AccelerationType) : null,
        };
        console.log(`Created new vesting info for ${id}:`, newVestingInfo);
        updatedVestingInfo = [...formData.vestingInfo, newVestingInfo];
      }
    }

    // Update the form data with the new vesting info
    console.log(`Updating formData.vestingInfo with:`, updatedVestingInfo);
    updateFormData("vestingInfo", updatedVestingInfo);

    // Log for debugging
    console.log(
      `Updated vesting field: ${field} to ${value} for director ${id}`
    );
  };

  // Calculate shares for a specific person
  const calculateSharesForPerson = (percentage: number): number => {
    return calculateShares(percentage, formData.issuedShares);
  };

  // Get directors with vesting enabled
  const directorsWithVesting = formData.directors.filter((director) => {
    const vestingInfo = formData.vestingInfo.find((v) => v.id === director.id);
    return vestingInfo?.isVester && vestingInfo?.vestingSchedule;
  });

  // Check if any directors have vesting enabled
  useEffect(() => {
    if (directorsWithVesting.length > 0) {
      setShowSection83bInfo(true);
    } else {
      setShowSection83bInfo(false);
      updateFormData("isVestingConfirmed", false);
    }
  }, [directorsWithVesting.length, updateFormData]);

  console.log({ direction: formData.directors });
  return (
    <div className="space-y-6">
      <div>
        <VestingIntro formData={formData} />

        {formData.directors.length === 0 ? (
          <div className="text-center p-6 bg-gray-50 rounded-lg">
            <p className="text-gray-500">
              Please add directors/officers/shareholders first
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <VestingTable
              formData={formData}
              updateVestingField={updateVestingField}
              calculateShares={calculateSharesForPerson}
            />

            {showSection83bInfo && (
              <Section83bInfo
                confirmed={formData.isVestingConfirmed}
                onConfirmationChange={(confirmed) =>
                  updateFormData("isVestingConfirmed", confirmed)
                }
              />
            )}

            {directorsWithVesting.length > 0 && (
              <VestingCharts
                formData={formData}
                directorsWithVesting={directorsWithVesting}
                calculateShares={calculateSharesForPerson}
                getScheduleLabel={getScheduleLabel}
                getAccelerationLabel={getAccelerationLabel}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default VestingStep;
