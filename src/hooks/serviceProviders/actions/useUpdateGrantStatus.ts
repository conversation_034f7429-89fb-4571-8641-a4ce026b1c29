import { PromisedGrant } from "@/types/serviceProvider";
import { promisedGrantService } from "@/services/promisedGrantService";
import { toast } from "sonner";

export function useUpdateGrantStatus(
  promisedGrants: PromisedGrant[],
  setPromisedGrants: React.Dispatch<React.SetStateAction<PromisedGrant[]>>
) {
  const updateGrantStatus = async (
    grantId: string,
    status: PromisedGrant["status"]
  ): Promise<boolean> => {
    try {
      const success = await promisedGrantService.updateGrantStatus(
        grantId,
        status
      );

      if (success) {
        setPromisedGrants((prev) =>
          prev.map((grant) =>
            grant.id === grantId ? { ...grant, status } : grant
          )
        );

        toast(
          `Grant Status Updated - The status has been updated to ${status}.`
        );
      } else {
        toast("Error - Failed to update grant status. Please try again.");
      }

      return success;
    } catch (error) {
      console.error("Error updating grant status:", error);
      toast("Error - Failed to update grant status. Please try again.");
      return false;
    }
  };

  return updateGrantStatus;
}
