import React, { useState, useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Import, File, AlertCircle, Upload } from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import {
  Shareholder,
  ShareClass,
  ShareClassDetails,
  CapTableSummary,
  ShareholderRole,
} from "@/types/capTable";
import { v4 as uuidv4 } from "uuid";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { processCapTableFromJson } from "@/utils/capTableCalculations";

interface CapTableUploaderProps {
  onDataUploaded: (capTable: CapTableSummary) => void;
}

const CapTableUploader: React.FC<CapTableUploaderProps> = ({
  onDataUploaded,
}) => {
  const { toast } = useToast();
  const csvFileInputRef = useRef<HTMLInputElement>(null);
  const jsonFileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);

  const handleCsvUploadClick = () => {
    if (csvFileInputRef.current) {
      csvFileInputRef.current.click();
    }
  };

  const handleJsonUploadClick = () => {
    if (jsonFileInputRef.current) {
      jsonFileInputRef.current.click();
    }
  };

  const processCSVData = (csvContent: string): CapTableSummary | null => {
    try {
      // Basic CSV parsing
      const lines = csvContent.split("\n");
      if (lines.length < 3) {
        throw new Error("CSV file doesn't have enough data");
      }

      // Extract header row
      const headers = lines[0].split(",").map((h) => h.trim());

      // Identify column indexes
      const nameIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("name")
      );
      const roleIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("role")
      );

      // Find share class columns
      const shareClassIndexes: { [key: string]: number } = {};
      headers.forEach((header, index) => {
        if (
          header.includes("Common") ||
          header.includes("Preferred") ||
          header.includes("Option") ||
          header.includes("Shares")
        ) {
          shareClassIndexes[header] = index;
        }
      });

      // Build share classes
      const shareClasses: ShareClassDetails[] = Object.keys(
        shareClassIndexes
      ).map((name) => {
        let type: "Common" | "Preferred" | "Option" = "Common";
        if (name.includes("Preferred")) type = "Preferred";
        if (name.includes("Option")) type = "Option";

        return {
          name: name as ShareClass,
          type,
        };
      });

      // Process shareholders
      const shareholders: Shareholder[] = [];
      let totalShares = 0;
      let totalFullyDiluted = 0;

      for (let i = 1; i < lines.length; i++) {
        if (!lines[i].trim()) continue;

        const values = lines[i].split(",").map((v) => v.trim());

        if (values.length < 3) continue;

        const holdings: { [key in ShareClass]?: number } = {};
        let shareholderTotalShares = 0;

        // Process holdings for each share class
        Object.entries(shareClassIndexes).forEach(([className, index]) => {
          const shares = parseInt(values[index] || "0", 10);
          if (!isNaN(shares) && shares > 0) {
            holdings[className as ShareClass] = shares;
            shareholderTotalShares += shares;
          }
        });

        // Only add if they have shares
        if (shareholderTotalShares > 0) {
          totalShares += shareholderTotalShares;
          totalFullyDiluted += shareholderTotalShares;

          shareholders.push({
            id: uuidv4(),
            name: values[nameIndex] || `Shareholder ${i}`,
            role: (values[roleIndex] || "Other") as ShareholderRole,
            holdings,
            totalShares: shareholderTotalShares,
            percentage: 0, // Will calculate after all rows are processed
            fullyDilutedPercentage: 0, // Will calculate after all rows are processed
          });
        }
      }

      // Calculate percentages
      shareholders.forEach((shareholder) => {
        shareholder.percentage = Number(
          ((shareholder.totalShares / totalShares) * 100).toFixed(2)
        );
        shareholder.fullyDilutedPercentage = Number(
          ((shareholder.totalShares / totalFullyDiluted) * 100).toFixed(2)
        );
      });

      // Create CapTableSummary
      return {
        id: uuidv4(),
        name: "Imported Cap Table",
        totalShares,
        totalFullyDiluted,
        shareClasses,
        shareholders,
        lastUpdated: new Date(),
        snapshots: [{ id: uuidv4(), name: "Import", date: new Date() }],
        isUserProvided: true,
      };
    } catch (error) {
      console.error("Error processing CSV data:", error);
      setError(
        "Failed to process the CSV file. Please check the format and try again."
      );
      return null;
    }
  };

  const processJSONData = (jsonContent: string): CapTableSummary | null => {
    try {
      const parsedData = JSON.parse(jsonContent);

      // Process the JSON data using our utility function
      return processCapTableFromJson(parsedData);
    } catch (error) {
      console.error("Error processing JSON data:", error);
      setError(
        "Failed to process the JSON file. Please check the format and try again."
      );
      return null;
    }
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    fileType: "csv" | "json"
  ) => {
    const file = event.target.files?.[0];
    setError(null);

    if (!file) return;

    // Check file extension
    const fileExt = file.name.split(".").pop()?.toLowerCase();
    if (fileType === "csv" && fileExt !== "csv") {
      setError("Only CSV files are supported for this upload option.");
      return;
    }

    if (fileType === "json" && fileExt !== "json") {
      setError("Only JSON files are supported for this upload option.");
      return;
    }

    try {
      setUploading(true);
      const content = await readFileAsText(file);

      let processedData;
      if (fileType === "csv") {
        processedData = processCSVData(content);
      } else {
        processedData = processJSONData(content);
      }

      if (processedData) {
        onDataUploaded(processedData);
        toast({
          title: "Data Uploaded Successfully",
          description: `${processedData.shareholders.length} shareholders with ${processedData.totalShares.toLocaleString()} shares imported.`,
        });
      }
    } catch (error) {
      console.error(`Error uploading ${fileType} file:`, error);
      setError(`Failed to upload the ${fileType} file. Please try again.`);
    } finally {
      setUploading(false);
      // Reset file input
      if (fileType === "csv" && csvFileInputRef.current) {
        csvFileInputRef.current.value = "";
      } else if (fileType === "json" && jsonFileInputRef.current) {
        jsonFileInputRef.current.value = "";
      }
    }
  };

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === "string") {
          resolve(reader.result);
        } else {
          reject(new Error("Failed to read file as text"));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsText(file);
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Cap Table Data</CardTitle>
        <CardDescription>
          Import your cap table data from a file
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="csv" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="csv">CSV Upload</TabsTrigger>
            <TabsTrigger value="json">JSON Upload</TabsTrigger>
          </TabsList>

          <TabsContent value="csv">
            <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
              <File className="h-12 w-12 text-gray-400 mb-4" />
              <p className="text-sm text-gray-600 mb-4 text-center">
                Upload a CSV file with your cap table data.
                <br />
                File should include shareholder names, roles, and share amounts.
              </p>

              <input
                type="file"
                ref={csvFileInputRef}
                accept=".csv"
                onChange={(e) => handleFileChange(e, "csv")}
                className="hidden"
              />

              <Button
                onClick={handleCsvUploadClick}
                disabled={uploading}
                className="flex items-center"
              >
                <Import className="mr-2 h-4 w-4" />
                {uploading ? "Uploading..." : "Upload CSV File"}
              </Button>
            </div>

            <div className="mt-4 text-sm text-gray-500">
              <p className="font-medium mb-1">Expected CSV format:</p>
              <p>
                First row: Header with columns like Name, Role, Common Shares,
                etc.
              </p>
              <p>Following rows: One row per shareholder with their data</p>
            </div>
          </TabsContent>

          <TabsContent value="json">
            <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
              <File className="h-12 w-12 text-gray-400 mb-4" />
              <p className="text-sm text-gray-600 mb-4 text-center">
                Upload a JSON file with your cap table data.
                <br />
                File should include shareholders, investment rounds,
                convertibles, etc.
              </p>

              <input
                type="file"
                ref={jsonFileInputRef}
                accept=".json"
                onChange={(e) => handleFileChange(e, "json")}
                className="hidden"
              />

              <Button
                onClick={handleJsonUploadClick}
                disabled={uploading}
                className="flex items-center"
              >
                <Upload className="mr-2 h-4 w-4" />
                {uploading ? "Uploading..." : "Upload JSON File"}
              </Button>
            </div>

            <div className="mt-4 text-sm text-gray-500">
              <p className="font-medium mb-1">Expected JSON format:</p>
              <p>
                A structured JSON file with shareholders array,
                investmentRounds, convertibles, etc.
              </p>
              <p>
                For each shareholder: name, role, commonShares, preferredShares,
                options
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CapTableUploader;
