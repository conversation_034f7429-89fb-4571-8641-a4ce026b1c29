import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, waitFor } from "@testing-library/react";
import { useFormData } from "../getFormData";
import { mockServerCompanyData } from "@/test/mocks/companyData";
import { AllTheProviders } from "@/test/utils";

// Mock the auth context
vi.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({
    user: {
      companyId: "test-company-id",
      id: "test-user-id",
    },
  }),
}));

// Mock the company info hook
vi.mock("@/integrations/legal-concierge/hooks/useCompanyInfo", () => ({
  useCompanyById: vi.fn(),
}));

import { useCompanyById } from "@/integrations/legal-concierge/hooks/useCompanyInfo";

describe("useFormData", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should transform server data to form data format", async () => {
    vi.mocked(useCompanyById).mockReturnValue({
      data: mockServerCompanyData,
      isLoading: false,
      error: null,
    } as any);

    const { result } = renderHook(() => useFormData(), {
      wrapper: AllTheProviders,
    });

    await waitFor(() => {
      const { formData } = result.current;

      // Check basic company information transformation
      expect(formData.companyName).toBe(mockServerCompanyData.name);
      expect(formData.nameAvailabilityChecked).toBe(true);
      expect(formData.incorporatingToday).toBe(
        mockServerCompanyData.isIncorporatedToday
      );

      // Check address transformation
      expect(formData.companyAddressStructured).toEqual({
        street1: mockServerCompanyData.streetAddress,
        street2: mockServerCompanyData.streetAddress2,
        city: mockServerCompanyData.city,
        state: mockServerCompanyData.state,
        zipCode: mockServerCompanyData.zipCode,
      });

      // Check shares transformation
      expect(formData.authorizedShares).toBe(
        mockServerCompanyData.numberOfAuthorizedShares
      );
      expect(formData.issuedShares).toBe(
        mockServerCompanyData.numberOfAuthorizedSharesToBeIssuedInitially
      );
      expect(formData.parValuePerShare).toBe(
        mockServerCompanyData.parValuePerShare
      );

      // Check stock option plan
      expect(formData.includeStockOptionPlan).toBe(
        mockServerCompanyData.includeStockOptionPlan
      );
      expect(formData.stockOptionPlanPercentage).toBe(
        mockServerCompanyData.stockOptionPlanPercentage
      );

      // Check form status fields
      expect(formData.isFormConfirmed).toBe(
        mockServerCompanyData.isFormConfirmed
      );
      expect(formData.isReviewConfirmed).toBe(
        mockServerCompanyData.isReviewConfirmed
      );
      expect(formData.isVestingConfirmed).toBe(
        mockServerCompanyData.isVestingConfirmed
      );
    });
  });

  it("should transform officers to directors with officer titles", async () => {
    vi.mocked(useCompanyById).mockReturnValue({
      data: mockServerCompanyData,
      isLoading: false,
      error: null,
    } as any);

    const { result } = renderHook(() => useFormData(), {
      wrapper: AllTheProviders,
    });

    await waitFor(() => {
      const { formData } = result.current;

      expect(formData.directors).toHaveLength(2);

      // Check first officer (CEO)
      const firstDirector = formData.directors[0];
      expect(firstDirector.name).toBe("John Doe");
      expect(firstDirector.officerTitles).toContain("CEO");
      expect(firstDirector.isVester).toBe(true);
      expect(firstDirector.vestingSchedule).toBe("Standard"); // FOURYEARSONEYEARCLIFF -> Standard
      expect(firstDirector.acceleration).toBe("SingleTrigger"); // SINGLETRIGGER -> SingleTrigger

      // Check second officer (President + Secretary)
      const secondDirector = formData.directors[1];
      expect(secondDirector.name).toBe("Jane Smith");
      expect(secondDirector.officerTitles).toContain("President");
      expect(secondDirector.officerTitles).toContain("Secretary");
      expect(secondDirector.vestingSchedule).toBe("Monthly"); // FOURYEARSNOCLIFF -> Monthly
      expect(secondDirector.acceleration).toBe("DoubleTrigger"); // DOUBLETRIGGER -> DoubleTrigger
    });
  });

  it("should create vesting info from officers", async () => {
    vi.mocked(useCompanyById).mockReturnValue({
      data: mockServerCompanyData,
      isLoading: false,
      error: null,
    } as any);

    const { result } = renderHook(() => useFormData(), {
      wrapper: AllTheProviders,
    });

    await waitFor(() => {
      const { formData } = result.current;

      expect(formData.vestingInfo).toHaveLength(2);

      // Check vesting info matches directors
      expect(formData.vestingInfo[0].id).toBe("officer-1");
      expect(formData.vestingInfo[0].isVester).toBe(true);
      expect(formData.vestingInfo[0].vestingSchedule).toBe("Standard");
      expect(formData.vestingInfo[0].acceleration).toBe("SingleTrigger");

      expect(formData.vestingInfo[1].id).toBe("officer-2");
      expect(formData.vestingInfo[1].isVester).toBe(true);
      expect(formData.vestingInfo[1].vestingSchedule).toBe("Monthly");
      expect(formData.vestingInfo[1].acceleration).toBe("DoubleTrigger");
    });
  });

  it("should transform intellectual properties to tech assignments", async () => {
    vi.mocked(useCompanyById).mockReturnValue({
      data: mockServerCompanyData,
      isLoading: false,
      error: null,
    } as any);

    const { result } = renderHook(() => useFormData(), {
      wrapper: AllTheProviders,
    });

    await waitFor(() => {
      const { formData } = result.current;

      expect(formData.founderTechAssignments).toHaveLength(1);

      const techAssignment = formData.founderTechAssignments[0];
      expect(techAssignment.founderId).toBe("officer-1");
      expect(techAssignment.founderName).toBe("John Doe");
      expect(techAssignment.techItems).toHaveLength(1);
      expect(techAssignment.techItems[0].id).toBe("ip-1");
      expect(techAssignment.techItems[0].description).toBe("Core AI Algorithm");
      expect(techAssignment.techItems[0].isExcluded).toBe(false);
    });
  });

  it("should handle missing company data", async () => {
    vi.mocked(useCompanyById).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: null,
    } as any);

    const { result } = renderHook(() => useFormData(), {
      wrapper: AllTheProviders,
    });

    expect(result.current.formData.companyName).toBe("");
    expect(result.current.formData.directors).toEqual([]);
    expect(result.current.currentStep).toBe("company-name");
  });

  it("should handle loading state", async () => {
    vi.mocked(useCompanyById).mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    const { result } = renderHook(() => useFormData(), {
      wrapper: AllTheProviders,
    });

    expect(result.current.isLoading).toBe(true);
  });

  it("should handle officers without intellectual properties", async () => {
    const companyDataWithoutIP = {
      ...mockServerCompanyData,
      officers: [
        {
          ...mockServerCompanyData.officers[0],
          intellectualProperties: [],
        },
      ],
    };

    vi.mocked(useCompanyById).mockReturnValue({
      data: companyDataWithoutIP,
      isLoading: false,
      error: null,
    } as any);

    const { result } = renderHook(() => useFormData(), {
      wrapper: AllTheProviders,
    });

    await waitFor(() => {
      const { formData } = result.current;
      expect(formData.founderTechAssignments).toHaveLength(0);
    });
  });
});
