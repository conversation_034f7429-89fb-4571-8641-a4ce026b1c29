export interface Collaborator {
  id: string;
  name?: string;
  fullName?: string | null;
  email: string;
  role: CollaboratorRole;
  status: InvitationStatus;
  dateAdded?: Date;
  lastActive?: Date;
  activity?: string;
}

export type CollaboratorRole =
  | "collaborator"
  | "owner"
  | "admin"
  | "signer"
  | "COLLABORATOR"
  | "ADMIN"
  | "OWNER"
  | "SIGNER";
export type InvitationStatus =
  | "accepted"
  | "pending"
  | "declined"
  | "Accepted"
  | "Pending"
  | "Declined";

export interface Comment {
  id: string;
  userId: string;
  userName: string;
  documentId: string;
  content: string;
  timestamp: Date;
  position?: {
    x: number;
    y: number;
  };
  resolved: boolean;
  statusText?: string;
}

export interface DiscussionMessage {
  id: string;
  userId: string;
  userName: string;
  content: string;
  timestamp: Date;
  attachments?: string[];
  replyTo?: string;
  resolved?: boolean;
  statusText?: string;
}
