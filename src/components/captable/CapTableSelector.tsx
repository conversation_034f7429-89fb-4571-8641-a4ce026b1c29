import React from "react";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { CapTableSummary } from "@/types/capTable";

interface CapTableSelectorProps {
  capTables: CapTableSummary[];
  currentTableId?: string;
  onTableChange: (id: string) => void;
}

const CapTableSelector: React.FC<CapTableSelectorProps> = ({
  capTables,
  currentTableId,
  onTableChange,
}) => {
  if (capTables.length <= 1) return null;

  return (
    <AnimatedTransition delay={0.05}>
      <Tabs
        value={currentTableId || ""}
        onValueChange={onTableChange}
        className="mb-6"
      >
        <TabsList className="mb-2">
          {capTables.map((table) => (
            <TabsTrigger
              key={table.id}
              value={table.id || ""}
              className="px-4 py-2"
            >
              {table.name || "Unnamed Cap Table"}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </AnimatedTransition>
  );
};

export default CapTableSelector;
