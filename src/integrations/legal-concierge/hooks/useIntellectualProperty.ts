import { useMutation, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import {
  IntellectualPropertyRequest,
  IntellectualPropertyUpdateRequest,
} from "../types/IntellectualProperty";
import { QueryKey } from "../keys";

const api = new APIClient();

// Intellectual Property hooks
export const useAddOfficerIP = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      officerId,
      companyId,
      ...payload
    }: {
      officerId: string;
      companyId: string;
    } & Omit<IntellectualPropertyRequest, "officerId">) => {
      const { data } = await api.addOfficerIP(officerId, payload);
      return { companyId, id: data.id };
    },
    onSuccess: (result: { companyId: string; id: string }) => {
      console.log("GAGO?");
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId],
      });
    },
  });
};

export const useUpdateOfficerIP = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      companyId,
      ...payload
    }: {
      id: string;
      companyId: string;
    } & Omit<IntellectualPropertyUpdateRequest, "id">) => {
      await api.updateOfficerIP(id, payload);
      return { companyId }; // Return companyId for invalidation
    },
    onSuccess: (result: { companyId: string }) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId],
      });
    },
  });
};

export const useDeleteOfficerIP = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      companyId,
    }: {
      id: string;
      companyId: string;
    }) => {
      await api.deleteOfficerIP(id);
      return { companyId }; // Return companyId for invalidation
    },
    onSuccess: (result: { companyId: string }) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, result.companyId],
      });
    },
  });
};
