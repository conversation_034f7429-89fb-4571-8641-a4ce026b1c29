import React, { useState } from "react";
import { Document } from "@/integrations/legal-concierge/types/Document";
import { Button } from "@/components/ui/button";
import {
  Eye,
  Edit2,
  CheckCircle,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { User } from "@/contexts/auth/types";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { APIClient } from "@/integrations/legal-concierge/client";

interface DocumentsListProps {
  documents?: Document[];
  isLoading?: boolean;
  error?: Error | null;
  handleDocumentOpen: (doc: Document, mode: 'view' | 'edit') => void;
  userData?: User | null;
  companyId?: string;
  onReviewConfirmed?: () => void;
}

const DocumentsList: React.FC<DocumentsListProps> = ({
  documents = [],
  isLoading = false,
  error = null,
  handleDocumentOpen,
  userData,
  companyId,
  onReviewConfirmed
}) => {
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const api = new APIClient();

  const handleViewDocument = async (document: Document) => {
    try {
      window.open(document.url, "_blank");
    } catch (error) {
      console.error("Error opening document:", error);
      toast.error("Error opening document");
    }
  };

  const handleDownloadDocument = (doc: Document) => {
    const link = window.document.createElement("a");
    link.href = doc.url;
    link.download = doc.fileName;
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
  };

  const handleConfirmReview = async () => {
    if (!companyId) {
      toast.error("Company ID is required");
      return;
    }

    try {
      const response = await api.confirmCompanyForm(companyId);
      if ('error' in response && response.error) {
        throw new Error(response.error);
      }
      toast.success("Documents review confirmed successfully");
      setShowReviewDialog(false);
      onReviewConfirmed?.();
    } catch (error) {
      console.error("Error confirming review:", error);
      toast.error("Failed to confirm review");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={`skeleton-${index}`} className="p-4 bg-white rounded-lg border">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-4 w-40" />
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-9 w-20" />
                <Skeleton className="h-9 w-20" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-6 text-red-500">
        Error loading documents. Please try again later.
      </div>
    );
  }

  if (!documents || documents.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500">
        No documents found. Documents will appear here after they are generated.
      </div>
    );
  }

  const isOwner = userData?.roles?.includes('OWNER');

  return (
    <>
      <div className="space-y-4">
        {documents.map((doc) => (
          <div
            key={doc.id || `doc-${doc.fileName}-${doc.createdAt}`}
            className="flex items-center justify-between p-4 bg-white rounded-lg border"
          >
            <div className="flex items-center gap-2">
              <img
                src={doc.thumbnailUrl}
                alt="Document thumbnail"
                className="h-10 w-10 object-contain"
              />
              <h3 className="font-medium">{doc.fileName}</h3>
              {/* <p className="text-sm text-gray-500">
                {doc.createdAt ? new Date(doc.createdAt).toLocaleDateString() : 'No date'}
              </p> */}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDocumentOpen(doc, 'view')}
              >
                <Eye className="h-4 w-4 mr-1" />
                View / Add Comments
              </Button>
              {isOwner && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDocumentOpen(doc, 'edit')}
                  >
                    <Edit2 className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                </>
              )}
            </div>
          </div>
        ))}
      </div>

      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Document Review</DialogTitle>
            <DialogDescription>
              Are you sure you want to confirm review of these documents? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReviewDialog(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DocumentsList;
