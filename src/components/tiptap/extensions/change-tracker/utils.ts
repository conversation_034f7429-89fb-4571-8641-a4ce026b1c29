import { Editor } from "@tiptap/core";

// Utility functions for change tracking

/**
 * Get all commits from the track state
 */
export function getCommits(editor: Editor) {
  const trackState = editor.commands.getTrackState();
  return trackState ? [] : []; // Will be populated when getTrackState returns actual state
}

/**
 * Check if there are uncommitted changes
 */
export function hasUncommittedChanges(editor: Editor): boolean {
  return editor.commands.hasUncommittedChanges();
}

/**
 * Save document with optional message
 */
export function saveDocument(editor: Editor, message?: string): boolean {
  return editor.commands.saveDocument(message);
}

/**
 * Commit changes with a message
 */
export function commitChanges(editor: Editor, message: string): boolean {
  return editor.commands.commitChanges(message);
}

/**
 * Format timestamp for display
 */
export function formatTimestamp(date: Date): string {
  return date.toLocaleString();
}

/**
 * Generate a default commit message
 */
export function generateDefaultCommitMessage(): string {
  const now = new Date();
  return `Changes saved at ${now.toLocaleTimeString()}`;
}

/**
 * Add keyboard shortcut for saving (Ctrl+S)
 */
export function addSaveShortcut(editor: Editor) {
  const handleKeyDown = (event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      saveDocument(editor, generateDefaultCommitMessage());
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  
  // Return cleanup function
  return () => {
    document.removeEventListener('keydown', handleKeyDown);
  };
}
