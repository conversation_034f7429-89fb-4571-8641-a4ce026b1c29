import {
  ServiceProvider,
  PromisedGrant,
  CompanyEquityInfo,
} from "@/types/serviceProvider";

export interface UseServiceProvidersState {
  serviceProviders: ServiceProvider[];
  promisedGrants: PromisedGrant[];
  companyEquity: CompanyEquityInfo;
  loading: boolean;
}

export interface UseServiceProvidersActions {
  addServiceProvider: (
    provider: Omit<ServiceProvider, "id" | "status" | "documents">
  ) => Promise<boolean>;
  updateGrantStatus: (
    grantId: string,
    status: PromisedGrant["status"]
  ) => Promise<boolean>;
  issueEquityGrant: (
    grantId: string,
    fairMarketValue: number
  ) => Promise<boolean>;
  terminateServiceProvider: (id: string) => Promise<boolean>;
}

export interface UseServiceProvidersReturn
  extends UseServiceProvidersState,
    UseServiceProvidersActions {}
