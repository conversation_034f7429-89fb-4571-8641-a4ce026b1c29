import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { QuestionnaireFormData } from "../types";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useConfirmCompanyForm } from "@/integrations/legal-concierge/hooks/useCompanyInfo";
import { useGenerateCompanyDocument } from "@/integrations/legal-concierge/hooks/useDocuments";

export const useFormSubmit = (
  formData: QuestionnaireFormData,
  isEditMode: boolean
) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const confirmCompanyForm = useConfirmCompanyForm();
  const generateCompanyDocument = useGenerateCompanyDocument();

  const handleSubmit = async () => {
    setLoading(true);

    try {
      if (!user) {
        toast.error("You must be logged in to save company details");
        setLoading(false);
        return;
      }

      // First, confirm the form completion
      if (user.companyId) {
        try {
          console.log("Confirming company form completion...");
          await confirmCompanyForm.mutateAsync(user.companyId);
          console.log("Company form confirmed successfully");
        } catch (confirmError) {
          console.error("Error confirming company form:", confirmError);
          // Continue with the rest of the submission even if confirmation fails
        }
      }

      // Form data is already saved via react-query hooks

      // Generate documents if not in edit mode
      if (
        !isEditMode &&
        user.companyId &&
        formData.registerMode == "getting-started"
      ) {
        try {
          console.log("Generating company documents...");
          await generateCompanyDocument.mutateAsync(user.companyId);
          console.log("Documents generated successfully");
          toast.success("Your documents have been generated successfully!");
        } catch (generateError) {
          console.error("Error generating documents:", generateError);
          toast.error(
            "There was an error generating your documents. Please try again later."
          );
          // Continue with the rest of the submission even if document generation fails
        }
      }

      if (isEditMode) {
        toast.success("Your company details have been updated!");
      } else {
        toast.success("Your company formation details have been saved!");
      }

      // Form completion is tracked via server state

      if (formData.registerMode == "getting-started") {
        navigate("/dashboard");
      } else {
        navigate("/data-room");
      }
    } catch (error) {
      console.error("Error saving form data:", error);
      toast.error("There was an error saving your data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return { handleSubmit, loading };
};
