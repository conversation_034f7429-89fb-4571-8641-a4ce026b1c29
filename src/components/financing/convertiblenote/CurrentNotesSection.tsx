import React from "react";
import { AlertCircle, ExternalLink } from "lucide-react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { ConvertibleNote } from "@/types/financing";
import { Link } from "react-router-dom";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Mock data for demonstration
const mockCurrentNotes: (ConvertibleNote & { hasMfnTrigger?: boolean })[] = [
  {
    id: "1",
    dateAuthorized: new Date("2023-03-15"),
    maturityDate: new Date("2025-03-15"),
    authorizedAmount: 500000,
    outstandingPrincipal: 450000,
    interestRate: 5,
    interestType: "Simple Interest",
    valuationCap: 10000000,
    discount: 20,
    mfn: true,
    boardApproved: true,
    hasMfnTrigger: true,
  },
  {
    id: "2",
    dateAuthorized: new Date("2023-05-20"),
    maturityDate: new Date("2025-05-20"),
    authorizedAmount: 750000,
    outstandingPrincipal: 750000,
    interestRate: 6,
    interestType: "Simple Interest",
    valuationCap: 12000000,
    discount: 15,
    mfn: false,
    boardApproved: true,
  },
];

const CurrentNotesSection: React.FC = () => {
  return (
    <Card>
      <CardContent className="pt-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date Authorized</TableHead>
              <TableHead>Maturity Date</TableHead>
              <TableHead>Authorized Amount</TableHead>
              <TableHead>Outstanding Principal</TableHead>
              <TableHead>Interest Rate</TableHead>
              <TableHead>Valuation Cap</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>MFN</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockCurrentNotes.map((note) => (
              <TableRow key={note.id}>
                <TableCell className="relative">
                  {note.dateAuthorized
                    ? format(note.dateAuthorized, "PP")
                    : "N/A"}
                  {note.hasMfnTrigger && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertCircle className="h-4 w-4 text-red-500 ml-2 inline" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            Per the terms of the convertible note, you will need
                            to amend and restate all issued and outstanding
                            convertible notes from this round. Visit the MFN tab
                            for further action.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <div className="mt-1">
                    <Link
                      to="/captable"
                      className="text-xs text-blue-600 flex items-center hover:underline"
                    >
                      Issued and Outstanding Convertible Notes
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </Link>
                  </div>
                </TableCell>
                <TableCell>
                  {note.maturityDate ? format(note.maturityDate, "PP") : "N/A"}
                </TableCell>
                <TableCell>${note.authorizedAmount.toLocaleString()}</TableCell>
                <TableCell>
                  ${note.outstandingPrincipal.toLocaleString()}
                </TableCell>
                <TableCell>{note.interestRate}%</TableCell>
                <TableCell>${note.valuationCap.toLocaleString()}</TableCell>
                <TableCell>{note.discount}%</TableCell>
                <TableCell>{note.mfn ? "Yes" : "No"}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <p className="text-sm text-muted-foreground mt-4">
          Please make sure to update the Cap Table tab for accuracy.
        </p>
      </CardContent>
    </Card>
  );
};

export default CurrentNotesSection;
