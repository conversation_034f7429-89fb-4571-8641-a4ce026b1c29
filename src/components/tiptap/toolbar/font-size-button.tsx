import {
  Dropdown<PERSON><PERSON>u,
  Dropdown<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { useEditorStore } from "@/store/use-editor-store";
import { type Level } from "@tiptap/extension-heading";
import { MinusIcon, PlusIcon } from "lucide-react";

const FONT_SIZES = [
  { label: "8", value: "8pt" },
  { label: "9", value: "9pt" },
  { label: "10", value: "10pt" },
  { label: "11", value: "11pt" },
  { label: "12", value: "12pt" },
  { label: "14", value: "14pt" },
  { label: "16", value: "16pt" },
  { label: "18", value: "18pt" },
  { label: "20", value: "20pt" },
  { label: "24", value: "24pt" },
  { label: "28", value: "28pt" },
  { label: "32", value: "32pt" },
  { label: "36", value: "36pt" },
  { label: "48", value: "48pt" },
  { label: "72", value: "72pt" },
];

function toPt(fontSize: string): number {
  if (!fontSize) return 11;
  if (fontSize.endsWith("pt")) {
    return parseFloat(fontSize);
  }
  if (fontSize.endsWith("px")) {
    return (parseFloat(fontSize) * 72) / 96;
  }
  if (fontSize.endsWith("em") || fontSize.endsWith("rem")) {
    return parseFloat(fontSize) * 11;
  }
  return parseFloat(fontSize);
}

export const FontSizeButton = () => {
  const { editor } = useEditorStore();

  const getActiveHeadingInfo = () => {
    for (let level = 1; level <= 6; level++) {
      if (editor?.isActive("heading", { level })) {
        const fontSize =
          editor.getAttributes("textStyle").fontSize ||
          {
            1: "24pt", // H1
            2: "18pt", // H2
            3: "16pt", // H3
            4: "14pt", // H4
            5: "12pt", // H5
            6: "10pt", // H6
          }[level];
        return { level, fontSize };
      }
    }
    return null;
  };

  const activeHeadingInfo = getActiveHeadingInfo();

  const currentFontSize = activeHeadingInfo
    ? String(Math.round(toPt(activeHeadingInfo.fontSize)))
    : String(
        Math.round(toPt(editor?.getAttributes("textStyle").fontSize || "11pt"))
      );

  const updateFontSize = (newSize: string | number) => {
    const size = typeof newSize === "string" ? parseInt(newSize) : newSize;
    if (!isNaN(size) && size > 0) {
      editor?.chain().focus().setFontSize(`${size}pt`).run();
      editor
        ?.chain()
        .focus()
        .toggleHeading({ level: 0 as Level })
        .run();
    }
  };

  const increment = () => {
    const newSize = Math.min(Number(currentFontSize) + 1, 72);
    updateFontSize(newSize);
  };

  const decrement = () => {
    const newSize = Math.max(Number(currentFontSize) - 1, 1);
    updateFontSize(newSize);
  };

  return (
    <div className="flex items-center gap-x-0.5">
      <button
        onClick={decrement}
        className="h-7 w-7 shrink-0 flex items-center justify-center rounded-sm hover:bg-neutral-200/80"
      >
        <MinusIcon className="size-4" />
      </button>
      <DropdownMenu>
        <DropdownMenuTrigger asChild className="px-0">
          <button className="h-7 w-auto min-w-4 shrink-0 flex items-center justify-center rounded-sm hover:bg-neutral-200/80 overflow-hidden text-sm">
            <span className="truncate">{currentFontSize}</span>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="p-1 flex flex-col gap-y-1 w-auto min-w-4">
          {FONT_SIZES.map(({ label, value }) => (
            <button
              key={value}
              onClick={() => {
                updateFontSize(value.replace("pt", ""));
              }}
              className={cn(
                "flex items-center px-2 py-1 rounded-sm hover:bg-neutral-200/80",
                currentFontSize === value.replace("pt", "") &&
                  "bg-neutral-200/80"
              )}
            >
              <span>{label}</span>
            </button>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <button
        onClick={increment}
        className="h-7 w-7 shrink-0 flex items-center justify-center rounded-sm hover:bg-neutral-200/80"
      >
        <PlusIcon className="size-4" />
      </button>
    </div>
  );
};
