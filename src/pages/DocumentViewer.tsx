import { useEffect, useState, useRef } from "react";

const DocumentViewer: React.FC = () => {
  const [loaded, setLoaded] = useState(false);
  const editorRef = useRef(null);

  const configs = {
    document: {
      fileType: "docx",
      key: "Khirz6zTPdfd7",
      title: "Action by Sole Incorporator (Form).docx ",
      url: "https://form-documents-dev.s3.us-east-1.amazonaws.com/2b28de05-ed7c-4be9-8ee9-69b8def57627/Action%20by%20Sole%20Incorporator%20%28Form%29.docx?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEDAaCXVzLWVhc3QtMSJGMEQCIE3nfCvTkT3R4a%2BVgUxPCxKAuMgv7WQYMARsph39DMILAiBg%2BYDK3K4SikGINLXimCVAvrrt%2ByYHpm5vcUkdE7etayrXAwjZ%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F8BEAAaDDE1MTI4NzEyMDc5NSIMWlXEwUX%2F%2FDSLS1NsKqsDEyRiBbpEzAWjU0DbPP6UG%2FO2Pd7k7v4HhjUhoBpAQMJaTy352ejAbAI6CXFuDkRhN7UWqsoklWnr9K9cL1%2FIn7EhsX8OJ3g9%2Fcwa6PNEtIkZYRgeVIUGcL00tBqI7oj3KZlESemyje%2BTwdbBevzzquVmLym6n98AV7d3TJkR%2B3zSf9JDR4YGO14dD4iKEpmPc%2BQ2ofKjzKryuMpFIJgHpJfYONHskZu41D9cyMQy%2BGac8MxSBfgjCZCB6vQ62LBBIYc1FMzCdJckkQyvHQZpJTLG4q9s28Q8afptNR8Nfx0lTg1y1SnslluTmyvKLJBIhSJIzl00PeIiNeqw0C8SB%2F31ZD6i4gT05OTzBorrnmudHNpx6FEg7IhgAkty6zyhOt%2FmJdZRo2c%2F6M1YMx4DRysfwHPoM%2FTP6KQ6DMXQFGPdc0srald2rAG8kp%2FJwSowuL%2Fd%2FFB7y46j%2Bsr%2BUJj1efTiedky8V0Dc9793owScrtLvuM3z9rd1iLeRQpzdDLJE4WPYdZONYspXKPuW5LXf36DjV2R8p%2FBHq0lbmRE8PLtcvCbMT9z77XjKzDVr4jBBjrfAujU8IWCwqqQSckH8mjVn7szUyJ%2Fofsm%2BuM9RNjfz20UKgGgtbgRFHOK0obMt3H2ezjgQJ6dYvvKjkugZ80dqXg3Vu8hy1Id%2FxyeIFmlBKQjEG8xbmscqGWLmPRvkKf265JyGS1Nw4p1676WY7bgFFjIsxHlpJiAlGKeNbyKyhw00FRIgRdK4LMiJ0TV7aSf2KMv632JmhafOq96vN54WpFpWGT4vVGzux%2BWeIqX3P%2FOErXC685Bv3sZr1CRkyfnc%2B%2FWr3%2Fvjqe6yMcH9BODZOmeJNNasowA7QLh5JwFTerDshkw68M5LPN7qrB7z0sRkjKRHMIrzYn1sCeEGAyR4F5TqPRsS63TxZsHJR3GhNpkVLPXmSuYu4BHFod55JqkVh%2BgZw2yrltHWKXVebJUycjfcRrMblRziQxbMba68Piqyj0Iow4KfpwrC8KCLnEwoTNJdge5nGbyIBM%2FRxcTuA%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIASGOLKIONQJ3GAJDF%2F20250512%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250512T154718Z&X-Amz-Expires=1800&X-Amz-SignedHeaders=host&X-Amz-Signature=dd0adc5df535b3d6540c5b2a2ecd72d813066fa17f37f8ceabf39befeb456771",
      permissions: {
        edit: true,
        download: true,
        forcesave: true,
      },
    },
    documentType: "word",
    editorConfig: {
      mode: "edit",
      callbackUrl: "https://apidev.foundersform.com/api/onlyoffice/callback",
      user: {
        id: "123",
        name: "John Doe",
      },
      customization: {
        autosave: true,
        compactHeader: true,
        compactToolbar: true,
        hideRulers: true,
        hideStatusBar: true,
        hideRightMenu: true,
        hideLeftMenu: true,
        toolbarHideFileName: true,
        about: false,
        feedback: false,
        help: false,
        plugins: false,
        toolbar: false,
        header: false,
        disableContextMenu: true,
        forcesave: true,
      },
    },
    height: "100%",
    width: "100%",
    token:
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkb2N1bWVudCI6eyJmaWxlVHlwZSI6ImRvY3giLCJrZXkiOiJLaGlyejZ6VFBkZmQ3IiwidGl0bGUiOiIxLmRvY3giLCJ1cmwiOiJodHRwczovL2Rldi5mb3VuZGVyc2Zvcm0uY29tLzEuZG9jeCIsInBlcm1pc3Npb25zIjp7ImVkaXQiOnRydWUsImRvd25sb2FkIjp0cnVlLCJmb3JjZXNhdmUiOnRydWV9fSwiZG9jdW1lbnRUeXBlIjoid29yZCIsImVkaXRvckNvbmZpZyI6eyJtb2RlIjoiZWRpdCIsImNhbGxiYWNrVXJsIjoiaHR0cHM6Ly9hcGlkZXYuZm91bmRlcnNmb3JtLmNvbS9hcGkvb25seW9mZmljZS9jYWxsYmFjayIsInVzZXIiOnsiaWQiOiIxMjMiLCJuYW1lIjoiSm9obiBEb2UifSwiY3VzdG9taXphdGlvbiI6eyJhdXRvc2F2ZSI6dHJ1ZSwiY29tcGFjdEhlYWRlciI6dHJ1ZSwiY29tcGFjdFRvb2xiYXIiOnRydWUsImhpZGVSdWxlcnMiOnRydWUsImhpZGVTdGF0dXNCYXIiOnRydWUsImhpZGVSaWdodE1lbnUiOnRydWUsImhpZGVMZWZ0TWVudSI6dHJ1ZSwidG9vbGJhckhpZGVGaWxlTmFtZSI6dHJ1ZSwiYWJvdXQiOmZhbHNlLCJmZWVkYmFjayI6ZmFsc2UsImhlbHAiOmZhbHNlLCJwbHVnaW5zIjpmYWxzZSwidG9vbGJhciI6ZmFsc2UsImhlYWRlciI6ZmFsc2UsImRpc2FibGVDb250ZXh0TWVudSI6dHJ1ZSwiZm9yY2VzYXZlIjp0cnVlfX0sImhlaWdodCI6IjEwMCUiLCJ3aWR0aCI6IjEwMCUiLCJuYmYiOjE3NDYyOTQ2OTcsImV4cCI6MTc0NjI5NTg5NywiaWF0IjoxNzQ2Mjk0Njk3fQ.VU6OVq0NPt75nz8n-NWe5MWyyT5HcrDBNmYCIc9Aq_M",
  };

  const handleSaveClick = () => {
    console.log("clicked save.");
    console.log(editorRef.current);
    if (editorRef.current && editorRef.current.editor) {
      console.log("clicked save inside editor.");
      editorRef.current.editor.requestSave(); // triggers save and callbackUrl
    }
  };
  const handleCloseTab = () => {
    console.log("closing");
    if (editorRef.current) {
      console.log(editorRef.current);
      console.log("destroying editor");
      editorRef.current.destroyEditor();
      editorRef.current = null; // true triggers forcesave
    }
    // window.close();
  };

  useEffect(() => {
    const onBeforeUnload = (e) => {
      if (editorRef.current) {
        editorRef.current.destroyEditor(true);
        editorRef.current = null; // true triggers forcesave
      }
    };
    window.addEventListener("beforeunload", onBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", onBeforeUnload);
    };
  }, []);

  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://docs.foundersform.com/web-apps/apps/api/documents/api.js";
    script.onload = () => setLoaded(true);
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  useEffect(() => {
    if (loaded && window.DocsAPI) {
      const docEditor = new window.DocsAPI.DocEditor("placeholder", configs);
      editorRef.current = docEditor; // Store the instance
    }
  }, [loaded, configs]);

  return (
    <div style={{ width: "100vw", height: "100vh" }}>
      <button onClick={handleSaveClick}>Save</button>
      <button onClick={handleCloseTab}>Close</button>
      <div id="placeholder" style={{ width: "100%", height: "100%" }} />
    </div>
  );
}
export default DocumentViewer;