import { describe, it, expect, vi, beforeEach } from "vitest";
import { validateStep } from "../formValidation";
import { QuestionnaireFormData } from "../../types";
import { mockLegacyFormData } from "@/test/mocks/companyData";

describe("formValidation", () => {
  const mockShowNameDialog = vi.fn();
  const mockShowIncorporationDialog = vi.fn();
  const mockShowSharesDialog = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("validateStep", () => {
    describe("company-name step", () => {
      it("should return false if company name is empty", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          companyName: "",
        };

        const result = validateStep(
          "company-name",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false);
      });

      it("should show name dialog if name availability not checked in getting-started mode", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          registerMode: "getting-started",
          companyName: "Test Company",
          nameAvailabilityChecked: false,
        };

        const result = validateStep(
          "company-name",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false);
        expect(mockShowNameDialog).toHaveBeenCalled();
      });

      it("should return false if incorporating today is null in getting-started mode", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          registerMode: "getting-started",
          companyName: "Test Company",
          nameAvailabilityChecked: true,
          incorporatingToday: null,
        };

        const result = validateStep(
          "company-name",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false);
      });

      it("should return true for valid company name step", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          registerMode: "getting-started",
          companyName: "Test Company",
          nameAvailabilityChecked: true,
          incorporatingToday: true,
          incorporationDate: new Date(), // Add incorporation date
        };

        const result = validateStep(
          "company-name",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false); // Should be false because it shows incorporation dialog
      });
    });

    describe("company-address step", () => {
      it("should return false if required address fields are missing", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "",
            street2: "",
            city: "",
            state: "",
            zipCode: "",
          },
        };

        const result = validateStep(
          "company-address",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false);
      });

      it("should return true for valid address", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "123 Main St",
            street2: "",
            city: "San Francisco",
            state: "CA",
            zipCode: "94105",
          },
        };

        const result = validateStep(
          "company-address",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(true);
      });
    });

    describe("authorized-shares step", () => {
      it("should return false if authorized shares is 0", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          authorizedShares: 0,
        };

        const result = validateStep(
          "authorized-shares",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false);
      });

      it("should return false if issued shares exceed authorized shares", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          authorizedShares: 1000000,
          issuedShares: 2000000,
        };

        const result = validateStep(
          "authorized-shares",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false);
      });

      it("should return true for valid shares configuration", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          authorizedShares: 10000000,
          issuedShares: 8000000,
          parValuePerShare: 0.00001,
        };

        const result = validateStep(
          "authorized-shares",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(true);
      });
    });

    describe("directors-and-officers step", () => {
      it("should return false if no directors are added", () => {
        const formData: QuestionnaireFormData = {
          ...mockLegacyFormData,
          directors: [],
        };

        const result = validateStep(
          "directors-and-officers",
          formData,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(false);
      });

      it("should return true with valid directors", () => {
        // Create test data with all required officer roles assigned
        const formDataWithValidDirectors: QuestionnaireFormData = {
          ...mockLegacyFormData,
          directors: [
            {
              ...mockLegacyFormData.directors[0],
              isDirector: true,
              isCEO: true,
              isPresident: true,
              isSecretary: false,
              isTreasurer: false,
            },
            {
              ...mockLegacyFormData.directors[1],
              isDirector: true,
              isCEO: false,
              isPresident: false,
              isSecretary: true,
              isTreasurer: true,
            },
          ],
        };

        const result = validateStep(
          "directors-and-officers",
          formDataWithValidDirectors,
          mockShowNameDialog,
          mockShowIncorporationDialog,
          mockShowSharesDialog
        );

        expect(result).toBe(true);
      });
    });
  });
});
