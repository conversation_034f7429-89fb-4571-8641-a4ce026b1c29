import { useEffect } from "react";
import { QuestionnaireFormData } from "../types";
import {
  calculateTotalDirectorPercentage,
  calculateRemainingPercentage,
  calculateShareAmounts,
} from "../utils/shareCalculations";

export const useSharesCalculation = (
  formData: QuestionnaireFormData,
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void
) => {
  const totalDirectorPercentage = calculateTotalDirectorPercentage(
    formData.directors
  );
  const remainingPercentage = calculateRemainingPercentage(formData.directors);

  // Update share amounts when percentages or issued shares change
  useEffect(() => {
    if (formData.issuedShares > 0) {
      const updatedDirectors = calculateShareAmounts(
        formData.directors,
        formData.issuedShares
      );

      if (
        JSON.stringify(updatedDirectors) !== JSON.stringify(formData.directors)
      ) {
        updateFormData("directors", updatedDirectors);
      }
    }
  }, [
    formData.issuedShares,
    formData.directors.map((d) => d.stockOwnership).join(","),
  ]);

  // When adding first director, set ownership to 100%
  useEffect(() => {
    if (
      formData.directors.length === 1 &&
      formData.directors[0].stockOwnership === 0
    ) {
      const updatedDirectors = [...formData.directors];
      updatedDirectors[0] = {
        ...updatedDirectors[0],
        stockOwnership: 100,
      };
      updateFormData("directors", updatedDirectors);
    }
  }, [formData.directors.length]);

  // When stock option plan is toggled or percentage changes, adjust accordingly
  useEffect(() => {
    if (formData.directors.length === 0) return;

    if (formData.includeStockOptionPlan) {
      // When turning on stock option plan, set its percentage to remaining percentage
      // (only if the stock option plan percentage is near zero)
      if (formData.stockOptionPlanPercentage < 0.01) {
        const available = 100 - totalDirectorPercentage;
        if (available > 0) {
          updateFormData(
            "stockOptionPlanPercentage",
            Number(available.toFixed(2))
          );
        }
      }
    }
  }, [formData.includeStockOptionPlan, totalDirectorPercentage]);

  // When stock option plan percentage is manually adjusted, adjust director percentages proportionally
  useEffect(() => {
    if (!formData.includeStockOptionPlan || formData.directors.length === 0)
      return;

    const availableForDirectors = 100 - formData.stockOptionPlanPercentage;

    // Only adjust if there's a meaningful difference to prevent infinite loops
    if (Math.abs(totalDirectorPercentage - availableForDirectors) > 0.01) {
      // Scale director percentages to fit within the new available percentage
      const scaleFactor = availableForDirectors / totalDirectorPercentage;

      const updatedDirectors = formData.directors.map((director) => ({
        ...director,
        stockOwnership: Number(
          (director.stockOwnership * scaleFactor).toFixed(2)
        ),
      }));

      // Adjust the last director to ensure total is exactly the available percentage
      const newTotal = updatedDirectors.reduce(
        (sum, d) => sum + d.stockOwnership,
        0
      );
      const diff = availableForDirectors - newTotal;

      if (Math.abs(diff) > 0.01 && updatedDirectors.length > 0) {
        const lastIndex = updatedDirectors.length - 1;
        updatedDirectors[lastIndex].stockOwnership = Number(
          (updatedDirectors[lastIndex].stockOwnership + diff).toFixed(2)
        );
      }

      updateFormData("directors", updatedDirectors);
    }
  }, [formData.stockOptionPlanPercentage, formData.includeStockOptionPlan]);

  return {
    totalDirectorPercentage,
    remainingPercentage: formData.includeStockOptionPlan
      ? 100 - totalDirectorPercentage
      : 0,
  };
};
