import React, { useState } from "react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Scroll, ScrollBar } from "@/components/ui/scroll-area";
import { Check, Info } from "lucide-react";
import SignatureModal from "@/components/signature/SignatureModal";
import { PromisedGrant } from "@/types/serviceProvider";

interface BoardApprovalSectionProps {
  grant: PromisedGrant;
  onCancel: () => void;
  onComplete: () => void;
  fairMarketValue: string;
  isSubmitting?: boolean;
}

const BoardApprovalSection: React.FC<BoardApprovalSectionProps> = ({
  grant,
  onCancel,
  onComplete,
  fairMarketValue,
  isSubmitting = false,
}) => {
  const [isSignatureModalOpen, setIsSignatureModalOpen] = useState(false);
  const [hasAdminSigned, setHasAdminSigned] = useState(false);

  const handleAdminSignatureComplete = () => {
    setHasAdminSigned(true);
    setIsSignatureModalOpen(false);
  };

  const formattedDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="space-y-6">
      <Alert className="bg-blue-50 border-blue-200">
        <Info className="h-4 w-4 text-blue-600" />
        <AlertTitle className="text-blue-800">
          Board Approval Required
        </AlertTitle>
        <AlertDescription className="text-blue-700">
          This equity grant requires board approval. Review the documents below
          and sign to initiate the board consent process.
        </AlertDescription>
      </Alert>

      <div className="border rounded-md p-4">
        <h3 className="font-semibold mb-2">Board Action by Written Consent</h3>
        <Scroll className="h-48 overflow-auto">
          <div className="p-2 text-sm">
            <p className="font-bold text-center">ACTION BY WRITTEN CONSENT</p>
            <p className="font-bold text-center">
              OF THE BOARD OF DIRECTORS OF
            </p>
            <p className="font-bold text-center">[COMPANY NAME], INC.</p>
            <p className="mt-4">
              The undersigned, constituting all of the members of the Board of
              Directors (the "Board") of [COMPANY NAME], INC., a Delaware
              corporation (the "Company"), in accordance with Section 141(f) of
              the Delaware General Corporation Law, hereby adopt the following
              resolutions by written consent:
            </p>
            <p className="mt-4 font-bold">Approval of Equity Grant</p>
            <p className="mt-2">
              WHEREAS, the Board deems it advisable and in the best interests of
              the Company to grant to {grant.serviceProviderName} (the "
              {grant.serviceProviderType}")
              {grant.shares.toLocaleString()} shares of the Company's{" "}
              {grant.grantType === "Option"
                ? `Common Stock pursuant to the Company's Stock Option Plan with an exercise price of $${fairMarketValue} per share`
                : "Common Stock as Restricted Stock"}
              , with vesting in accordance with the {grant.vestingSchedule}{" "}
              vesting schedule.
            </p>
            <p className="mt-4">
              NOW, THEREFORE, BE IT RESOLVED, that the grant to the{" "}
              {grant.serviceProviderType} of
              {grant.grantType === "Option"
                ? " an option to purchase"
                : ""}{" "}
              {grant.shares.toLocaleString()} shares of the Company's Common
              Stock{" "}
              {grant.grantType === "Option"
                ? `at an exercise price of $${fairMarketValue} per share, which the Board has determined to be the fair market value of such stock as of the date hereof`
                : ""}
              , with vesting in accordance with the {grant.vestingSchedule}{" "}
              vesting schedule, is hereby authorized and approved;
            </p>
            <p className="mt-4">
              RESOLVED FURTHER, that the officers of the Company are authorized
              and directed to take such actions and execute such documents as
              they may deem necessary or appropriate to implement and carry out
              the intent of the foregoing resolutions.
            </p>
            <p className="mt-6">
              This Action by Written Consent may be executed in counterparts,
              each of which shall be deemed an original and all of which
              together shall constitute one instrument. This Action by Written
              Consent shall be filed with the minutes of the proceedings of the
              Board.
            </p>
            <p className="mt-6">
              The undersigned have executed this Action by Written Consent as of{" "}
              {formattedDate}.
            </p>
          </div>
          <ScrollBar />
        </Scroll>
      </div>

      <div className="pt-2 text-center">
        <Button
          onClick={() => setIsSignatureModalOpen(true)}
          disabled={hasAdminSigned || isSubmitting}
          className="mx-auto"
        >
          {hasAdminSigned ? (
            <div className="flex items-center">
              <Check className="mr-2 h-4 w-4" />
              Signed
            </div>
          ) : (
            "Sign as Administrator"
          )}
        </Button>
      </div>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button onClick={onComplete} disabled={!hasAdminSigned || isSubmitting}>
          {isSubmitting ? "Processing..." : "Complete"}
        </Button>
      </div>

      <SignatureModal
        isOpen={isSignatureModalOpen}
        onClose={() => setIsSignatureModalOpen(false)}
        documentName="Board Consent for Equity Grant"
        onComplete={handleAdminSignatureComplete}
      />
    </div>
  );
};

export default BoardApprovalSection;
