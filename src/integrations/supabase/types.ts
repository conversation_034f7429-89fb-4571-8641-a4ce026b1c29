export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      board_meetings: {
        Row: {
          agenda_file_url: string | null;
          board_materials: Json | null;
          company_id: string | null;
          created_at: string | null;
          id: string;
          is_cancelled: boolean | null;
          meeting_date: string;
          meeting_purpose: string;
          meeting_timezone: string | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          agenda_file_url?: string | null;
          board_materials?: Json | null;
          company_id?: string | null;
          created_at?: string | null;
          id?: string;
          is_cancelled?: boolean | null;
          meeting_date: string;
          meeting_purpose: string;
          meeting_timezone?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          agenda_file_url?: string | null;
          board_materials?: Json | null;
          company_id?: string | null;
          created_at?: string | null;
          id?: string;
          is_cancelled?: boolean | null;
          meeting_date?: string;
          meeting_purpose?: string;
          meeting_timezone?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "board_meetings_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
        ];
      };
      company_details: {
        Row: {
          authorized_shares: number | null;
          company_address: Json | null;
          company_name: string;
          created_at: string | null;
          id: string;
          incorporating_today: boolean | null;
          incorporation_date: string | null;
          issued_shares: number | null;
          par_value_per_share: number | null;
          planned_incorporation_date: string | null;
          stock_option_plan_percentage: number | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          authorized_shares?: number | null;
          company_address?: Json | null;
          company_name: string;
          created_at?: string | null;
          id?: string;
          incorporating_today?: boolean | null;
          incorporation_date?: string | null;
          issued_shares?: number | null;
          par_value_per_share?: number | null;
          planned_incorporation_date?: string | null;
          stock_option_plan_percentage?: number | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          authorized_shares?: number | null;
          company_address?: Json | null;
          company_name?: string;
          created_at?: string | null;
          id?: string;
          incorporating_today?: boolean | null;
          incorporation_date?: string | null;
          issued_shares?: number | null;
          par_value_per_share?: number | null;
          planned_incorporation_date?: string | null;
          stock_option_plan_percentage?: number | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [];
      };
      directors: {
        Row: {
          appointment_date: string;
          company_id: string | null;
          created_at: string | null;
          id: string;
          name: string;
          resignation_date: string | null;
          resigned: boolean | null;
          stockholder_approval: boolean | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          appointment_date: string;
          company_id?: string | null;
          created_at?: string | null;
          id?: string;
          name: string;
          resignation_date?: string | null;
          resigned?: boolean | null;
          stockholder_approval?: boolean | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          appointment_date?: string;
          company_id?: string | null;
          created_at?: string | null;
          id?: string;
          name?: string;
          resignation_date?: string | null;
          resigned?: boolean | null;
          stockholder_approval?: boolean | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "directors_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
        ];
      };
      discussion_messages: {
        Row: {
          company_id: string | null;
          content: string;
          id: string;
          reply_to: string | null;
          resolved: boolean | null;
          timestamp: string;
          user_id: string;
          user_name: string;
        };
        Insert: {
          company_id?: string | null;
          content: string;
          id?: string;
          reply_to?: string | null;
          resolved?: boolean | null;
          timestamp?: string;
          user_id: string;
          user_name: string;
        };
        Update: {
          company_id?: string | null;
          content?: string;
          id?: string;
          reply_to?: string | null;
          resolved?: boolean | null;
          timestamp?: string;
          user_id?: string;
          user_name?: string;
        };
        Relationships: [
          {
            foreignKeyName: "discussion_messages_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "discussion_messages_reply_to_fkey";
            columns: ["reply_to"];
            isOneToOne: false;
            referencedRelation: "discussion_messages";
            referencedColumns: ["id"];
          },
        ];
      };
      document_comments: {
        Row: {
          company_id: string | null;
          content: string;
          document_id: string;
          id: string;
          position: Json | null;
          resolved: boolean | null;
          timestamp: string;
          user_id: string;
          user_name: string;
        };
        Insert: {
          company_id?: string | null;
          content: string;
          document_id: string;
          id?: string;
          position?: Json | null;
          resolved?: boolean | null;
          timestamp?: string;
          user_id: string;
          user_name: string;
        };
        Update: {
          company_id?: string | null;
          content?: string;
          document_id?: string;
          id?: string;
          position?: Json | null;
          resolved?: boolean | null;
          timestamp?: string;
          user_id?: string;
          user_name?: string;
        };
        Relationships: [
          {
            foreignKeyName: "document_comments_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
        ];
      };
      external_documents: {
        Row: {
          document_name: string;
          document_type: string;
          document_url: string;
          external_incorporation_id: string;
          id: string;
          is_required: boolean | null;
          uploaded_at: string | null;
        };
        Insert: {
          document_name: string;
          document_type: string;
          document_url: string;
          external_incorporation_id: string;
          id?: string;
          is_required?: boolean | null;
          uploaded_at?: string | null;
        };
        Update: {
          document_name?: string;
          document_type?: string;
          document_url?: string;
          external_incorporation_id?: string;
          id?: string;
          is_required?: boolean | null;
          uploaded_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "external_documents_external_incorporation_id_fkey";
            columns: ["external_incorporation_id"];
            isOneToOne: false;
            referencedRelation: "external_incorporations";
            referencedColumns: ["id"];
          },
        ];
      };
      external_incorporations: {
        Row: {
          authorized_shares: number;
          company_address: Json;
          company_name: string;
          created_at: string | null;
          id: string;
          incorporation_date: string;
          issued_shares: number;
          par_value_per_share: number;
          registered_agent_address: string;
          registered_agent_email: string | null;
          registered_agent_name: string;
          registered_agent_phone: string | null;
          stock_option_plan: boolean | null;
          stock_option_plan_percentage: number | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          authorized_shares: number;
          company_address: Json;
          company_name: string;
          created_at?: string | null;
          id?: string;
          incorporation_date: string;
          issued_shares: number;
          par_value_per_share: number;
          registered_agent_address: string;
          registered_agent_email?: string | null;
          registered_agent_name: string;
          registered_agent_phone?: string | null;
          stock_option_plan?: boolean | null;
          stock_option_plan_percentage?: number | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          authorized_shares?: number;
          company_address?: Json;
          company_name?: string;
          created_at?: string | null;
          id?: string;
          incorporation_date?: string;
          issued_shares?: number;
          par_value_per_share?: number;
          registered_agent_address?: string;
          registered_agent_email?: string | null;
          registered_agent_name?: string;
          registered_agent_phone?: string | null;
          stock_option_plan?: boolean | null;
          stock_option_plan_percentage?: number | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      external_stakeholders: {
        Row: {
          acceleration: string | null;
          address: string | null;
          created_at: string | null;
          email: string | null;
          external_incorporation_id: string;
          has_vesting: boolean | null;
          id: string;
          is_director: boolean | null;
          name: string;
          officer_titles: string[] | null;
          shares_amount: number | null;
          stock_ownership_percentage: number | null;
          updated_at: string | null;
          vesting_schedule: string | null;
        };
        Insert: {
          acceleration?: string | null;
          address?: string | null;
          created_at?: string | null;
          email?: string | null;
          external_incorporation_id: string;
          has_vesting?: boolean | null;
          id?: string;
          is_director?: boolean | null;
          name: string;
          officer_titles?: string[] | null;
          shares_amount?: number | null;
          stock_ownership_percentage?: number | null;
          updated_at?: string | null;
          vesting_schedule?: string | null;
        };
        Update: {
          acceleration?: string | null;
          address?: string | null;
          created_at?: string | null;
          email?: string | null;
          external_incorporation_id?: string;
          has_vesting?: boolean | null;
          id?: string;
          is_director?: boolean | null;
          name?: string;
          officer_titles?: string[] | null;
          shares_amount?: number | null;
          stock_ownership_percentage?: number | null;
          updated_at?: string | null;
          vesting_schedule?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "external_stakeholders_external_incorporation_id_fkey";
            columns: ["external_incorporation_id"];
            isOneToOne: false;
            referencedRelation: "external_incorporations";
            referencedColumns: ["id"];
          },
        ];
      };
      external_tech_contributions: {
        Row: {
          created_at: string | null;
          description: string;
          external_incorporation_id: string;
          id: string;
          stakeholder_id: string;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          description: string;
          external_incorporation_id: string;
          id?: string;
          stakeholder_id: string;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          description?: string;
          external_incorporation_id?: string;
          id?: string;
          stakeholder_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "external_tech_contributions_external_incorporation_id_fkey";
            columns: ["external_incorporation_id"];
            isOneToOne: false;
            referencedRelation: "external_incorporations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "external_tech_contributions_stakeholder_id_fkey";
            columns: ["stakeholder_id"];
            isOneToOne: false;
            referencedRelation: "external_stakeholders";
            referencedColumns: ["id"];
          },
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          full_name: string | null;
          id: string;
          updated_at: string | null;
          username: string | null;
          website: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          full_name?: string | null;
          id: string;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          full_name?: string | null;
          id?: string;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
        };
        Relationships: [];
      };
      promised_grants: {
        Row: {
          company_id: string | null;
          created_at: string;
          grant_type: string;
          id: string;
          option_type: string | null;
          service_provider_id: string | null;
          service_provider_name: string;
          service_provider_type: string;
          shares: number;
          status: string;
          updated_at: string;
          user_id: string;
          vesting_commencement_date: string;
          vesting_schedule: string;
        };
        Insert: {
          company_id?: string | null;
          created_at?: string;
          grant_type: string;
          id?: string;
          option_type?: string | null;
          service_provider_id?: string | null;
          service_provider_name: string;
          service_provider_type: string;
          shares: number;
          status?: string;
          updated_at?: string;
          user_id: string;
          vesting_commencement_date: string;
          vesting_schedule: string;
        };
        Update: {
          company_id?: string | null;
          created_at?: string;
          grant_type?: string;
          id?: string;
          option_type?: string | null;
          service_provider_id?: string | null;
          service_provider_name?: string;
          service_provider_type?: string;
          shares?: number;
          status?: string;
          updated_at?: string;
          user_id?: string;
          vesting_commencement_date?: string;
          vesting_schedule?: string;
        };
        Relationships: [
          {
            foreignKeyName: "promised_grants_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "promised_grants_service_provider_id_fkey";
            columns: ["service_provider_id"];
            isOneToOne: false;
            referencedRelation: "service_providers";
            referencedColumns: ["id"];
          },
        ];
      };
      service_providers: {
        Row: {
          address: string | null;
          cliff: number | null;
          company_id: string | null;
          compensation: number | null;
          compensation_period: string | null;
          created_at: string | null;
          email: string;
          grant_type: string | null;
          id: string;
          name: string;
          option_type: string | null;
          services: string | null;
          shares: number | null;
          start_date: string;
          type: string;
          updated_at: string | null;
          user_id: string | null;
          vesting_period: number | null;
          vesting_schedule: string | null;
        };
        Insert: {
          address?: string | null;
          cliff?: number | null;
          company_id?: string | null;
          compensation?: number | null;
          compensation_period?: string | null;
          created_at?: string | null;
          email: string;
          grant_type?: string | null;
          id?: string;
          name: string;
          option_type?: string | null;
          services?: string | null;
          shares?: number | null;
          start_date: string;
          type: string;
          updated_at?: string | null;
          user_id?: string | null;
          vesting_period?: number | null;
          vesting_schedule?: string | null;
        };
        Update: {
          address?: string | null;
          cliff?: number | null;
          company_id?: string | null;
          compensation?: number | null;
          compensation_period?: string | null;
          created_at?: string | null;
          email?: string;
          grant_type?: string | null;
          id?: string;
          name?: string;
          option_type?: string | null;
          services?: string | null;
          shares?: number | null;
          start_date?: string;
          type?: string;
          updated_at?: string | null;
          user_id?: string | null;
          vesting_period?: number | null;
          vesting_schedule?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "service_providers_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
        ];
      };
      stakeholders: {
        Row: {
          address: string | null;
          company_id: string | null;
          created_at: string | null;
          email: string | null;
          id: string;
          is_director: boolean | null;
          name: string;
          officer_titles: string[] | null;
          shares_amount: number | null;
          stock_ownership_percentage: number | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          address?: string | null;
          company_id?: string | null;
          created_at?: string | null;
          email?: string | null;
          id?: string;
          is_director?: boolean | null;
          name: string;
          officer_titles?: string[] | null;
          shares_amount?: number | null;
          stock_ownership_percentage?: number | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          address?: string | null;
          company_id?: string | null;
          created_at?: string | null;
          email?: string | null;
          id?: string;
          is_director?: boolean | null;
          name?: string;
          officer_titles?: string[] | null;
          shares_amount?: number | null;
          stock_ownership_percentage?: number | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "stakeholders_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
        ];
      };
      tech_assignments: {
        Row: {
          company_id: string | null;
          created_at: string | null;
          id: string;
          stakeholder_id: string | null;
          tech_description: string | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          company_id?: string | null;
          created_at?: string | null;
          id?: string;
          stakeholder_id?: string | null;
          tech_description?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          company_id?: string | null;
          created_at?: string | null;
          id?: string;
          stakeholder_id?: string | null;
          tech_description?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "tech_assignments_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tech_assignments_stakeholder_id_fkey";
            columns: ["stakeholder_id"];
            isOneToOne: false;
            referencedRelation: "stakeholders";
            referencedColumns: ["id"];
          },
        ];
      };
      vesting_info: {
        Row: {
          acceleration: string | null;
          company_id: string | null;
          created_at: string | null;
          has_vesting: boolean | null;
          id: string;
          stakeholder_id: string | null;
          updated_at: string | null;
          user_id: string | null;
          vesting_schedule: string | null;
        };
        Insert: {
          acceleration?: string | null;
          company_id?: string | null;
          created_at?: string | null;
          has_vesting?: boolean | null;
          id?: string;
          stakeholder_id?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          vesting_schedule?: string | null;
        };
        Update: {
          acceleration?: string | null;
          company_id?: string | null;
          created_at?: string | null;
          has_vesting?: boolean | null;
          id?: string;
          stakeholder_id?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          vesting_schedule?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "vesting_info_company_id_fkey";
            columns: ["company_id"];
            isOneToOne: false;
            referencedRelation: "company_details";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "vesting_info_stakeholder_id_fkey";
            columns: ["stakeholder_id"];
            isOneToOne: false;
            referencedRelation: "stakeholders";
            referencedColumns: ["id"];
          },
        ];
      };
      vesting_schedules: {
        Row: {
          acceleration: string | null;
          cliff_months: number;
          company_id: string | null;
          created_at: string | null;
          director_id: string | null;
          director_name: string;
          id: string;
          schedule_type: string;
          total_shares: number;
          updated_at: string | null;
          user_id: string | null;
          vesting_period_months: number;
          vesting_start_date: string;
        };
        Insert: {
          acceleration?: string | null;
          cliff_months?: number;
          company_id?: string | null;
          created_at?: string | null;
          director_id?: string | null;
          director_name: string;
          id?: string;
          schedule_type: string;
          total_shares: number;
          updated_at?: string | null;
          user_id?: string | null;
          vesting_period_months?: number;
          vesting_start_date: string;
        };
        Update: {
          acceleration?: string | null;
          cliff_months?: number;
          company_id?: string | null;
          created_at?: string | null;
          director_id?: string | null;
          director_name?: string;
          id?: string;
          schedule_type?: string;
          total_shares?: number;
          updated_at?: string | null;
          user_id?: string | null;
          vesting_period_months?: number;
          vesting_start_date?: string;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_promised_grants_summary: {
        Args: {
          p_user_id: string;
          p_company_id: string;
        };
        Returns: {
          total_shares: number;
          allocated_shares: number;
          remaining_shares: number;
        }[];
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type PublicSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;
