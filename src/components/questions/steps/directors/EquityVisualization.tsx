import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import { Officer } from "../../types/Officer";
import {
  Tooltip as UITooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Info } from "lucide-react";

interface EquityVisualizationProps {
  directors: Officer[];
  includeStockOptionPlan: boolean;
  stockOptionPlanPercentage: number;
}

const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884D8",
  "#82CA9D",
  "#FF6B6B",
  "#6C757D",
];

const EquityVisualization: React.FC<EquityVisualizationProps> = ({
  directors,
  includeStockOptionPlan,
  stockOptionPlanPercentage,
}) => {
  // Prepare data for the pie chart
  const chartData = directors.map((director, index) => ({
    name: director.name || `Unnamed Director ${index + 1}`,
    value: director.stockOwnership,
    color: COLORS[index % COLORS.length],
  }));

  // Add option plan if included
  if (includeStockOptionPlan && stockOptionPlanPercentage > 0) {
    chartData.push({
      name: "Stock Option Plan",
      value: stockOptionPlanPercentage,
      color: "#A9A9A9", // Gray color for option plan
    });
  }

  // Ensure we have data before rendering
  if (chartData.length === 0) {
    return (
      <div className="text-center p-8 bg-gray-50 rounded-lg">
        <p className="text-gray-500">
          Add directors to visualize equity distribution
        </p>
      </div>
    );
  }

  // Custom tooltip for pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow-sm">
          <p className="font-medium">{payload[0].name}</p>
          <p className="text-sm">{`${payload[0].value}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="mt-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base font-medium">Equity Distribution</h3>
        <TooltipProvider>
          <UITooltip>
            <TooltipTrigger asChild>
              <button className="inline-flex items-center text-gray-500 hover:text-gray-700">
                <Info size={16} className="mr-1" />
                <span className="text-sm">What is this?</span>
              </button>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <p>
                Visual representation of how equity is distributed among
                founders and the option pool (if applicable).
              </p>
            </TooltipContent>
          </UITooltip>
        </TooltipProvider>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-4 grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
          {chartData.map((entry, index) => (
            <div key={`legend-${index}`} className="flex items-center">
              <div
                className="w-3 h-3 mr-2 rounded-sm"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm truncate">
                {entry.name}: {entry.value}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EquityVisualization;
