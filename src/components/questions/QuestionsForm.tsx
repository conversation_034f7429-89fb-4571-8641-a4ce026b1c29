import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/common/Card";
import Button from "@/components/common/Button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { FORM_STEPS } from "./constants/formSteps";
import ProgressIndicator from "./ProgressIndicator";
import StepRenderer from "./StepRenderer";
import { useQuestionsForm } from "./hooks/useQuestionsForm";
import QuestionsFormSkeleton from "./QuestionsFormSkeleton";
import {
  NameAvailabilityDialog,
  IncorporationDialog,
  SharesValidationDialog,
} from "./dialogs/FormDialogs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface QuestionsFormProps {
  isEditMode?: boolean;
  readOnly?: boolean;
}

const QuestionsForm: React.FC<QuestionsFormProps> = ({
  isEditMode = false,
  readOnly = false,
}) => {
  const navigate = useNavigate();
  const {
    currentStep,
    formData,
    loading,
    isLoading,
    showNameDialog,
    showIncorporationDialog,
    showSharesDialog,
    setShowNameDialog,
    setShowIncorporationDialog,
    setShowSharesDialog,
    updateFormData,
    handleSubmit,
    handleBack,
    handleNext,
    handleNameDialogConfirm,
    handleIncorporationDialogConfirm,
  } = useQuestionsForm(isEditMode);
  // Add a local loading state for the Continue button
  const [isContinueLoading, setIsContinueLoading] = useState(false);

  const [confirmationChecked, setConfirmationChecked] = useState(false);
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false);

  const getStepTitle = () => {
    const step = FORM_STEPS.find((s) => s.id === currentStep);
    return step?.label || "";
  };

  const handleConfirmationChange = (checked: boolean) => {
    setConfirmationChecked(checked);
    // Note: The form confirmation will be sent when the Continue button is clicked
    // in the handleNext function in useQuestionsForm.ts
  };

  const handleFinalSubmit = () => {
    if (currentStep === "review" && !confirmationChecked) {
      // Show error or toast message
      return;
    }

    setShowFinalConfirmation(true);
  };

  if (isLoading) {
    return <QuestionsFormSkeleton />;
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <ProgressIndicator steps={FORM_STEPS} currentStep={currentStep} />
        <CardTitle>{getStepTitle()}</CardTitle>
      </CardHeader>
      <CardContent>
        <StepRenderer
          currentStep={currentStep}
          formData={formData}
          updateFormData={updateFormData}
          confirmationChecked={confirmationChecked}
          onConfirmationChange={handleConfirmationChange}
          isFormConfirmed={formData.isFormConfirmed}
          readOnly={readOnly}
        />
      </CardContent>
      {!readOnly && (
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            icon={<ChevronLeft size={16} />}
            disabled={formData.isFormConfirmed}
          >
            Back
          </Button>
          {currentStep === "review" ? (
            // Only show the Generate Documents button if the form is not already confirmed
            !formData.isFormConfirmed ? (
              <Button
                onClick={handleFinalSubmit}
                loading={loading}
                disabled={!confirmationChecked}
              >
                {formData.registerMode == "getting-started"
                  ? isEditMode
                    ? "Save Changes"
                    : "Generate Documents"
                  : isEditMode
                    ? "Save Changes"
                    : "Upload Documents"}
              </Button>
            ) : (
              <Button
                onClick={() => navigate("/dashboard")}
                className="bg-green-600 hover:bg-green-700"
              >
                Go to Dashboard
              </Button>
            )
          ) : (
            <Button
              onClick={async () => {
                setIsContinueLoading(true);
                try {
                  await handleNext();
                } finally {
                  setIsContinueLoading(false);
                }
              }}
              icon={<ChevronRight size={16} />}
              iconPosition="right"
              loading={isContinueLoading}
            >
              Continue
            </Button>
          )}
        </CardFooter>
      )}
      {readOnly && (
        <CardFooter className="flex justify-center">
          <Button onClick={() => navigate("/dashboard")} variant="outline">
            Back to Dashboard
          </Button>
        </CardFooter>
      )}

      {/* Dialogs */}
      {formData.registerMode == "getting-started" && (
        <>
          <NameAvailabilityDialog
            open={showNameDialog}
            onOpenChange={setShowNameDialog}
            onConfirm={handleNameDialogConfirm}
          />
          <IncorporationDialog
            open={showIncorporationDialog}
            onOpenChange={setShowIncorporationDialog}
            onConfirm={handleIncorporationDialogConfirm}
          />
        </>
      )}

      <SharesValidationDialog
        open={showSharesDialog}
        onOpenChange={setShowSharesDialog}
      />

      {/* Final Confirmation Dialog */}
      <Dialog
        open={showFinalConfirmation}
        onOpenChange={setShowFinalConfirmation}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {formData.registerMode == "getting-started"
                ? isEditMode
                  ? "Save Company Details Changes"
                  : "Generate Incorporation Documents"
                : isEditMode
                  ? "Save Changes"
                  : "Upload Incorporation Documents"}
            </DialogTitle>
            <DialogDescription>
              {formData.registerMode == "getting-started"
                ? isEditMode
                  ? "Are you sure you want to save these changes? They will be reflected throughout the system."
                  : "Are you sure you want to generate the incorporation documents now? Once generated, you cannot edit the information."
                : isEditMode
                  ? "Are you sure you want to save these changes? They will be reflected throughout the system."
                  : "Are you sure you want to upload the incorporation documents now? Once uploaded, you cannot edit the information."}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowFinalConfirmation(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowFinalConfirmation(false);
                handleSubmit();
              }}
            >
              {formData.registerMode == "getting-started"
                ? isEditMode
                  ? "Yes, save changes"
                  : "Yes, generate now"
                : isEditMode
                  ? "Yes, save changes"
                  : "Yes, proceed to upload"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default QuestionsForm;
