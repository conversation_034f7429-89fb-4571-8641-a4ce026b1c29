export interface OfficerOwnership {
  officerId: string;
  ownershipPercentage: number;
  amountOfShares: number;
}

export interface StockOptionPlanRequest {
  companyId: string | null;
  includeStockOptionPlan: boolean | null;
  stockOptionPlanPercentage: number | null;
  officersOwnership?: OfficerOwnership[];
}

export type VestingSchedule = "FOURYEARSONEYEARCLIFF" | "FOURYEARSNOCLIFF";

export interface VestingUnit {
  officerId: string;
  isVester: boolean;
  vestingSchedule: VestingSchedule;
  acceleration: VestingAcceleration;
}

export type VestingAcceleration = "NONE" | "SINGLETRIGGER" | "DOUBLETRIGGER";

export interface VestingRequest {
  companyId: string | null;
  vestingUnits: VestingUnit[] | null;
  isVestingConfirmed: boolean;
}
