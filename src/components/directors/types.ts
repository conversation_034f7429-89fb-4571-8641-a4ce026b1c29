export interface Director {
  id: string;
  name: string;
  appointmentDate: Date;
  resigned: boolean;
  resignationDate: Date | null;
  stockholderApproval: boolean;
  removed?: boolean;
  removalDate?: Date | null;
  resignationLetter?: File | null;
}

export interface BoardConsentEmailData {
  companyName: string;
  administrator: string;
  newDirectors: string[];
  amendBylaws: boolean;
}

export interface StockholderConsentEmailData {
  companyName: string;
  administrator: string;
  newDirectors: string[];
  amendBylaws: boolean;
}
