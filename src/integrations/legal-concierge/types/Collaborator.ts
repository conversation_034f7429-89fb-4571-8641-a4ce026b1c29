export type CollaboratorRole =
| "collaborator"
| "owner"
| "admin"
| "COLLABORATOR"
| "ADMIN"
| "OWNER"
export type CollaboratorStatus =
  | "accepted"
  | "pending"
  | "declined"
  | "Accepted"
  | "Pending"
  | "Declined";

export interface Collaborator {
  id: string;
  companyId: string;
  userId?: string;
  name?: string;
  fullName?: string | null;
  email: string;
  role: CollaboratorRole;
  status: CollaboratorStatus;
  dateAdded?: string; // ISO date string
  lastActive?: string; // ISO date string
  activity?: string;
}

export interface CollaboratorInviteRequest {
  email: string;
  role: CollaboratorRole;
}

export interface CollaboratorRoleUpdateRequest {
  role: CollaboratorRole;
}
