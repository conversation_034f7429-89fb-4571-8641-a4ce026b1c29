import React from "react";
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface IncreaseSharesInitialStateProps {
  currentAuthorizedShares: number;
  totalShares: number;
  additionalShares: string;
  boardApproved: boolean;
  stockholdersApproved: boolean;
  onAdditionalSharesChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClose: () => void;
  onBoardApprovalClick: () => void;
  onStockholderApprovalClick: () => void;
  onFileAmendmentClick: () => void;
}

const IncreaseSharesInitialState: React.FC<IncreaseSharesInitialStateProps> = ({
  currentAuthorizedShares,
  totalShares,
  additionalShares,
  boardApproved,
  stockholdersApproved,
  onAdditionalSharesChange,
  onClose,
  onBoardApprovalClick,
  onStockholderApprovalClick,
  onFileAmendmentClick,
}) => {
  return (
    <>
      <DialogHeader>
        <DialogTitle>Increase Authorized Shares</DialogTitle>
        <DialogDescription>
          Add more shares to your company's authorized capital
        </DialogDescription>
      </DialogHeader>

      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="currentShares" className="text-right">
            Authorized Shares:
          </Label>
          <Input
            id="currentShares"
            value={currentAuthorizedShares.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="additionalShares" className="text-right">
            Additional Shares:
          </Label>
          <Input
            id="additionalShares"
            value={additionalShares}
            onChange={onAdditionalSharesChange}
            className="col-span-3"
            placeholder="Enter number of shares"
          />
        </div>

        {additionalShares && (
          <Alert className="mt-2">
            <AlertDescription className="italic">
              The authorized share capital of the Company will be{" "}
              {totalShares.toLocaleString()} shares. To authorize this amount of
              shares, you will need to obtain board consent and stockholder
              consent.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Board Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">{boardApproved ? "Yes" : "No"}</span>
            <Button
              variant="outline"
              disabled={!additionalShares || boardApproved}
              onClick={onBoardApprovalClick}
            >
              Submit for Board Approval
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Stockholders Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">
              {stockholdersApproved ? "Yes" : "No"}
            </span>
            <Button
              variant="outline"
              disabled={!additionalShares || stockholdersApproved}
              onClick={onStockholderApprovalClick}
            >
              Submit for Stockholder Approval
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">File Amendment:</Label>
          <div className="col-span-3">
            <Button
              disabled={!boardApproved || !stockholdersApproved}
              onClick={onFileAmendmentClick}
            >
              File Certificate of Amendment
            </Button>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
      </DialogFooter>
    </>
  );
};

export default IncreaseSharesInitialState;
