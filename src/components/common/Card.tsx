import React from "react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "glass" | "outline";
  hover?: boolean;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = "default",
  hover = false,
  onClick,
}) => {
  const baseStyles = "rounded-xl overflow-hidden";

  const variantStyles = {
    default: "bg-white border border-gray-100 shadow-sm",
    glass: "bg-white/80 backdrop-blur-lg border border-white/20 shadow-sm",
    outline: "bg-transparent border border-gray-200",
  };

  const hoverStyles = hover
    ? "transition-all duration-300 hover:shadow-md hover:-translate-y-1"
    : "";

  const cardProps = {
    className: cn(baseStyles, variantStyles[variant], hoverStyles, className),
    ...(onClick && { onClick }),
  };

  return onClick ? (
    <motion.div
      {...cardProps}
      whileHover={
        hover ? { y: -4, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" } : {}
      }
      whileTap={onClick ? { scale: 0.98 } : {}}
    >
      {children}
    </motion.div>
  ) : (
    <div {...cardProps}>{children}</div>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

const CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => (
  <div className={cn("px-6 py-5", className)}>{children}</div>
);

interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
}

const CardTitle: React.FC<CardTitleProps> = ({ children, className }) => (
  <h3 className={cn("text-xl font-semibold text-gray-900", className)}>
    {children}
  </h3>
);

interface CardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

const CardDescription: React.FC<CardDescriptionProps> = ({
  children,
  className,
}) => <p className={cn("text-sm text-gray-500 mt-1", className)}>{children}</p>;

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

const CardContent: React.FC<CardContentProps> = ({ children, className }) => (
  <div className={cn("px-6 py-4", className)}>{children}</div>
);

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

const CardFooter: React.FC<CardFooterProps> = ({ children, className }) => (
  <div className={cn("px-6 py-4 bg-gray-50/50", className)}>{children}</div>
);

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
};
