import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";

interface Company {
  companyId: string;
  companyName?: string;
  role: string;
}

interface Props {
  open: boolean;
  companies: Company[];
  onSelect: (companyId: string) => void;
  onClose: VoidFunction;
  isClosable?: boolean;
}

export const CompanySwitcherDialog = ({
  open,
  companies,
  onSelect,
  onClose,
  isClosable = true,
}: Props) => {
  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        // Only allow closing if isClosable is true
        if (!state && isClosable) {
          onClose();
        }
      }}
    >
      <DialogContent className="max-w-md">
        {/* Close button always rendered */}
        {isClosable && (
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        )}

        <DialogTitle>Choose Your Workspace</DialogTitle>
        <p className="text-sm text-muted-foreground mt-1">
          You're part of multiple organizations. Select one to continue — you
          can switch later from your profile.
        </p>

        <ul className="flex flex-col gap-4 w-full divide-y divide-border">
          {companies.map((c) => (
            <li
              key={c.companyId}
              className="py-4 px-2 hover:bg-muted/50 rounded-xl transition-colors cursor-pointer"
              onClick={() => onSelect(c.companyId)}
            >
              <div className="flex items-center space-x-4">
                <div className="shrink-0">
                  <div className="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center text-sm font-bold">
                    {(c.companyName?.[0] ?? "N/A").toUpperCase()}
                  </div>
                </div>
                <div className="min-w-0">
                  <p className="text-sm font-medium text-foreground truncate">
                    {c.companyName || "No Name"}
                  </p>
                  <p className="text-xs text-muted-foreground mt-0.5">
                    <span className="font-semibold text-foreground">
                      Your role:
                    </span>{" "}
                    {c.role}
                  </p>
                  <p className="bg-gray-200 text-gray-800 text-xs font-medium me-2 px-2 py-0.5 mt-1 rounded-sm dark:bg-gray-700 dark:text-gray-500">
                    {c.companyId}
                  </p>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </DialogContent>
    </Dialog>
  );
};
