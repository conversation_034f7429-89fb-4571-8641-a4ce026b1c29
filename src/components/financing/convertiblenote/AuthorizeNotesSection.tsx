import React, { useState } from "react";
import { AlertCircle, HelpCircle } from "lucide-react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { ConvertibleNote, InterestType } from "@/types/financing";

const AuthorizeNotesSection: React.FC = () => {
  const { toast } = useToast();
  const [boardApprovalDialogOpen, setBoardApprovalDialogOpen] = useState(false);
  const [hasLowerValuationCap, setHasLowerValuationCap] = useState(false);
  const [hasHigherDiscount, setHasHigherDiscount] = useState(false);
  const [hasHigherInterestRate, setHasHigherInterestRate] = useState(false);

  // For demo purposes - in a real app this would come from API/state
  const [boardApproved, setBoardApproved] = useState(false);

  const form = useForm({
    defaultValues: {
      authorizedAmount: "",
      interestRate: "",
      interestType: "Simple Interest" as InterestType,
      maturityDate: null as Date | null,
      valuationCap: "",
      discount: "",
      mfn: "no",
    },
  });

  const onSubmit = (data: any) => {
    console.log("Authorized Note Data:", data);
    // Here you would typically save this data to your backend
    toast({
      title: "Form Submitted",
      description:
        "Your convertible note authorization form has been submitted.",
    });

    setBoardApprovalDialogOpen(true);
  };

  // This would check against existing notes in a real application
  // For demo purposes, we're just showing the UI
  const checkMfnTriggers = (
    value: string,
    field: "valuationCap" | "discount" | "interestRate"
  ) => {
    if (field === "valuationCap") {
      setHasLowerValuationCap(parseFloat(value) < 10000000); // Example threshold
    } else if (field === "discount") {
      setHasHigherDiscount(parseFloat(value) > 15); // Example threshold
    } else if (field === "interestRate") {
      setHasHigherInterestRate(parseFloat(value) > 5); // Example threshold
    }
  };

  const handleBoardApprovalSubmit = () => {
    // In a real app, this would generate and send documents to directors
    toast({
      title: "Board Approval Requested",
      description: "Board approval documents have been sent to all directors.",
    });

    setBoardApprovalDialogOpen(false);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <h3 className="text-lg font-semibold mb-4">Round Details</h3>
        <p className="text-sm text-muted-foreground mb-6">
          Please include the terms of the convertible note.
        </p>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="authorizedAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Authorized Amount of Round</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        $
                      </span>
                      <Input
                        {...field}
                        type="text"
                        placeholder="0.00"
                        className="pl-8"
                        onChange={(e) => {
                          field.onChange(e);
                        }}
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="interestRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Interest Rate</FormLabel>
                    <FormControl>
                      <div className="relative flex">
                        <Input
                          {...field}
                          type="text"
                          placeholder="0.00"
                          className="pr-8"
                          onChange={(e) => {
                            field.onChange(e);
                            checkMfnTriggers(e.target.value, "interestRate");
                          }}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          %
                        </span>
                        {hasHigherInterestRate && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <AlertCircle className="h-4 w-4 text-red-500 ml-2 self-center" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">
                                  The terms of this round will trigger the MFN
                                  under one or more of the outstanding SAFEs
                                  and/or convertible notes and may replace the
                                  terms of those convertibles. Please consult
                                  Founders Forms if you have any questions
                                  before issuing SAFEs with these terms.
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="interestType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Interest Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select interest type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Simple Interest">
                          Simple Interest
                        </SelectItem>
                        <SelectItem value="Compound Interest">
                          Compound Interest
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="maturityDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Maturity Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value || undefined}
                        onSelect={field.onChange}
                        initialFocus
                        className={cn("p-3 pointer-events-auto")}
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="valuationCap"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Valuation Cap</FormLabel>
                    <FormControl>
                      <div className="relative flex">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2">
                          $
                        </span>
                        <Input
                          {...field}
                          type="text"
                          placeholder="0.00"
                          className="pl-8"
                          onChange={(e) => {
                            field.onChange(e);
                            checkMfnTriggers(e.target.value, "valuationCap");
                          }}
                        />
                        {hasLowerValuationCap && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <AlertCircle className="h-4 w-4 text-red-500 ml-2 self-center" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">
                                  The terms of this round will trigger the MFN
                                  under one or more of the outstanding SAFEs
                                  and/or convertible notes and may replace the
                                  terms of those convertibles. Please consult
                                  Founders Forms if you have any questions
                                  before issuing convertible notes with these
                                  terms.
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="discount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Discount</FormLabel>
                    <FormControl>
                      <div className="relative flex">
                        <Input
                          {...field}
                          type="text"
                          placeholder="0.00"
                          className="pr-8"
                          onChange={(e) => {
                            field.onChange(e);
                            checkMfnTriggers(e.target.value, "discount");
                          }}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          %
                        </span>
                        {hasHigherDiscount && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <AlertCircle className="h-4 w-4 text-red-500 ml-2 self-center" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">
                                  The terms of this round will trigger the MFN
                                  under one or more of the outstanding SAFEs
                                  and/or convertible notes and may replace the
                                  terms of those convertibles. Please consult
                                  Founders Forms if you have any questions
                                  before issuing convertible notes with these
                                  terms.
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="mfn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>MFN (Most Favored Nation)</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select yes or no" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">Yes</SelectItem>
                      <SelectItem value="no">No</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <div className="border p-4 rounded-md bg-gray-50">
              <h4 className="font-medium mb-2">Board Approval</h4>
              <p className="text-sm text-muted-foreground mb-4">
                To issue your convertible notes, you will need board approval.
                Please click Submit for Board Approval to submit the form of
                convertible note and round details for board approval.
              </p>
              <div className="flex items-center mb-4">
                <span className="font-medium mr-2">Approved:</span>
                <span>{boardApproved ? "Yes" : "No"}</span>
              </div>
              <Button type="submit">Submit for Board Approval</Button>
            </div>
          </form>
        </Form>

        <Dialog
          open={boardApprovalDialogOpen}
          onOpenChange={setBoardApprovalDialogOpen}
        >
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Board Approval for Convertible Note</DialogTitle>
              <DialogDescription>
                Review and sign the board consent and form of convertible note
              </DialogDescription>
            </DialogHeader>

            <div className="h-96 border rounded-md p-4 overflow-y-auto bg-gray-50 my-4">
              <h3 className="text-lg font-semibold mb-2">
                Form of Convertible Note
              </h3>
              <p className="text-sm">
                This is where the generated form of convertible note would
                appear. In a real application, this would be a document that can
                be reviewed and edited.
              </p>
              {/* Mock content for demo purposes */}
              <div className="mt-4 space-y-2">
                <p>CONVERTIBLE PROMISSORY NOTE</p>
                <p>Principal Amount: ${form.watch("authorizedAmount")}</p>
                <p>
                  Interest Rate: {form.watch("interestRate")}% (
                  {form.watch("interestType")})
                </p>
                <p>
                  Maturity Date:{" "}
                  {form.watch("maturityDate")
                    ? format(form.watch("maturityDate"), "PPP")
                    : "Not set"}
                </p>
                <p>Valuation Cap: ${form.watch("valuationCap")}</p>
                <p>Discount: {form.watch("discount")}%</p>
                <p>MFN: {form.watch("mfn") === "yes" ? "Yes" : "No"}</p>
                {/* More mock content would be here */}
              </div>
            </div>

            <div className="border p-4 rounded-md">
              <h4 className="font-medium mb-2">Signature</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Please sign this document to request board approval
              </p>
              <div className="border h-20 rounded-md mb-4 bg-gray-50">
                {/* Signature pad would go here */}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setBoardApprovalDialogOpen(false)}
              >
                Save as Draft
              </Button>
              <Button onClick={handleBoardApprovalSubmit}>Finish</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default AuthorizeNotesSection;
