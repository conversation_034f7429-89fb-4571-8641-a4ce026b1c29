import { PromisedGrant } from "@/types/serviceProvider";
import { toast } from "sonner";
import { useUpdateGrantStatus } from "./useUpdateGrantStatus";

export function useIssueEquityGrant(
  promisedGrants: PromisedGrant[],
  setPromisedGrants: React.Dispatch<React.SetStateAction<PromisedGrant[]>>,
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
) {
  const updateGrantStatus = useUpdateGrantStatus(
    promisedGrants,
    setPromisedGrants
  );

  const issueEquityGrant = async (
    grantId: string,
    fairMarketValue: number
  ): Promise<boolean> => {
    setLoading(true);
    try {
      // In a real app, this would trigger the board consent process
      const grant = promisedGrants.find((g) => g.id === grantId);

      if (!grant) {
        throw new Error("Grant not found");
      }

      // Update grant status to Board Approved
      const success = await updateGrantStatus(grantId, "Board Approved");

      if (success) {
        toast(
          "Equity Grant Initiated - Board consent has been sent to directors for approval."
        );

        // Simulate board approval (in a real app this would be an actual approval flow)
        setTimeout(async () => {
          await updateGrantStatus(grantId, "Completed");

          toast(
            "Equity Grant Completed - The board has approved and the recipient has signed the grant documents."
          );
        }, 5000);
      }

      setLoading(false);
      return success;
    } catch (error) {
      console.error("Error issuing equity grant:", error);
      toast("Error - Failed to issue equity grant. Please try again.");
      setLoading(false);
      return false;
    }
  };

  return issueEquityGrant;
}
