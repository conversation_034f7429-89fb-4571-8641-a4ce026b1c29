import React from "react";

const VestingLegend: React.FC = () => {
  return (
    <div className="mt-6 text-sm text-gray-500 space-y-3 p-4 bg-gray-50 rounded-lg">
      <h3 className="font-medium text-gray-700">Vesting Schedule Types:</h3>

      <div>
        <p className="font-medium">
          Standard Schedule (4 years, 1 year cliff):
        </p>
        <ul className="list-disc pl-5 mt-1 space-y-1">
          <li>No shares vest until the 1-year anniversary (cliff period)</li>
          <li>At the 1-year mark, 25% of total shares vest immediately</li>
          <li>
            The remaining 75% vest in equal monthly installments over the
            following 36 months
          </li>
          <li>Full vesting is achieved after 48 months (4 years)</li>
        </ul>
      </div>

      <div>
        <p className="font-medium">Monthly Schedule (4 years, no cliff):</p>
        <ul className="list-disc pl-5 mt-1 space-y-1">
          <li>Shares begin vesting from month 1 (no cliff period)</li>
          <li>1/48th of total shares vest each month (linear vesting)</li>
          <li>Vesting occurs in equal monthly installments over 48 months</li>
          <li>Full vesting is achieved after 48 months (4 years)</li>
        </ul>
      </div>

      <p className="italic mt-2">
        Note: The vesting start date is typically set to the date of
        incorporation or another agreed-upon date. This chart is for
        demonstration purposes only.
      </p>
    </div>
  );
};

export default VestingLegend;
