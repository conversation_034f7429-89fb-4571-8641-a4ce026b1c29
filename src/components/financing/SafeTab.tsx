import React, { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import AuthorizeSafesSection from "./safe/AuthorizeSafesSection";
import CurrentSafesSection from "./safe/CurrentSafesSection";

const SafeTab: React.FC = () => {
  const [activeSubTab, setActiveSubTab] = useState("authorize-safes");

  return (
    <div className="mt-6">
      <Tabs
        defaultValue="authorize-safes"
        value={activeSubTab}
        onValueChange={setActiveSubTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="authorize-safes">Authorize SAFEs</TabsTrigger>
          <TabsTrigger value="current-safes">Current SAFEs</TabsTrigger>
        </TabsList>

        <TabsContent value="authorize-safes">
          <AuthorizeSafesSection />
        </TabsContent>

        <TabsContent value="current-safes">
          <CurrentSafesSection />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SafeTab;
