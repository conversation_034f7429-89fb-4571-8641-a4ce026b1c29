// PostIncorporation API types

export interface PostIncorporationTaskResponse {
  message?: string;
  data?: any;
  error?: string | null;
  validationErrors?: Record<string, unknown> | null;
}

export interface ForeignQualificationState {
  id: string;
  state: string;
  companyId: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ForeignQualificationStateRequest {
  state: string;
}
