import { VestingSchedule, AccelerationType } from "../../types";
import { differenceInMonths, differenceInDays } from "date-fns";

export const getScheduleLabel = (schedule: VestingSchedule) => {
  switch (schedule) {
    case "Standard":
      return "4 years, 1 year cliff";
    case "Monthly":
      return "4 years, no cliff";
    default:
      return "";
  }
};

export const getAccelerationLabel = (acceleration: AccelerationType | null) => {
  switch (acceleration) {
    case "SingleTrigger":
      return "Single Trigger";
    case "DoubleTrigger":
      return "Double Trigger";
    case "None":
      return "None";
    default:
      return "";
  }
};

// Calculate shares based on ownership percentage
export const calculateShares = (
  percentage: number,
  issuedShares: number
): number => {
  return Math.round((percentage / 100) * issuedShares);
};

// Get company details from storage for calculations
export const getCompanyDetails = () => {
  try {
    // First try to get completed details
    let savedData = localStorage.getItem("completedCompanyDetails");

    // Fall back to in-progress questionnaire if needed
    if (!savedData) {
      savedData = localStorage.getItem("companyQuestionnaire");
    }

    if (savedData) {
      return JSON.parse(savedData);
    }
  } catch (error) {
    console.error("Error loading company details in vesting helpers:", error);
  }
  return null;
};

// Calculate vested percentage for a given vesting schedule
export const calculateVestedPercentage = (
  startDate: Date,
  schedule: VestingSchedule
): number => {
  const today = new Date();
  const monthsElapsed = differenceInMonths(today, startDate);

  // Monthly schedule (4 years, no cliff)
  if (schedule === "Monthly") {
    // Cap at 48 months (4 years)
    const cappedMonths = Math.min(monthsElapsed, 48);
    // Calculate percentage (each month is 1/48 of total)
    return Math.max(0, (cappedMonths / 48) * 100);
  }

  // Standard schedule (4 years, 1 year cliff)
  else {
    // Before cliff period (1 year)
    if (monthsElapsed < 12) {
      return 0;
    }

    // At cliff exactly - 25% vests immediately
    if (monthsElapsed === 12) {
      return 25;
    }

    // After cliff, before 4 years
    if (monthsElapsed > 12 && monthsElapsed < 48) {
      // 25% at cliff, remaining 75% spread over 36 months
      const additionalMonths = monthsElapsed - 12;
      const additionalPercentage = (additionalMonths / 36) * 75;
      return 25 + additionalPercentage;
    }

    // After 4 years - fully vested
    return 100;
  }
};

// Calculate number of vested shares
export const calculateVestedShares = (
  totalShares: number,
  startDate: Date,
  schedule: VestingSchedule
): number => {
  const vestedPercentage = calculateVestedPercentage(startDate, schedule);
  return Math.round((vestedPercentage / 100) * totalShares);
};

// Utility function to get saved issued shares count
export const getSavedIssuedShares = (): number => {
  const details = getCompanyDetails();
  return details?.issuedShares || 0;
};
