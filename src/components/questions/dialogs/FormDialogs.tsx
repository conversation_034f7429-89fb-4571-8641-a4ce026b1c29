import React, { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface NameDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export const NameAvailabilityDialog: React.FC<NameDialogProps> = ({
  open,
  onOpenChange,
  onConfirm,
}) => (
  <AlertDialog open={open} onOpenChange={onOpenChange}>
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>Name Availability Check</AlertDialogTitle>
        <AlertDialogDescription>
          Before selecting the official name of the Company, we recommend you
          conduct a name availability search with
          <a
            href="https://icis.corp.delaware.gov/eCorp/EntitySearch/NameSearch.aspx"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            {" "}
            Delaware Secretary of State{" "}
          </a>
          and conduct a trademark search at{" "}
          <a
            href="https://tmsearch.uspto.gov/search/search-information"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            USPTO Trademark Search
          </a>
          . Founders Form does not guarantee that the name will be available now
          or in the future.
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel>No</AlertDialogCancel>
        <AlertDialogAction onClick={onConfirm}>
          Yes, I've checked
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);

interface IncorporationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export const IncorporationDialog: React.FC<IncorporationDialogProps> = ({
  open,
  onOpenChange,
  onConfirm,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    setIsLoading(true);
    try {
      await onConfirm();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Name Reservation Recommended</AlertDialogTitle>
          <AlertDialogDescription>
            To assure your preferred name is available, we recommend reserving
            the name, if available. For a payment of $75.00, Delaware will
            reserve the name of the company for sixty (60) days. Visit{" "}
            <a
              href="https://icis.corp.delaware.gov/eCorp/EntitySearch/NameSearch.aspx"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              Delaware Secretary of State's website
            </a>{" "}
            to reserve the name.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? "Saving..." : "Continue"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

interface SharesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const SharesValidationDialog: React.FC<SharesDialogProps> = ({
  open,
  onOpenChange,
}) => (
  <AlertDialog open={open} onOpenChange={onOpenChange}>
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>Invalid Share Amount</AlertDialogTitle>
        <AlertDialogDescription>
          The number of shares to be issued cannot be more than the number of
          authorized shares.
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogAction onClick={() => onOpenChange(false)}>
          OK
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);
