import React from "react";
import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Trigger,
  TooltipContent,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { QuestionnaireFormData } from "../../types";

interface VestingIntroProps {
  formData: QuestionnaireFormData;
}


const VestingIntro: React.FC<VestingIntroProps> = ({ formData }) => {
  return (
    <div className="flex items-center mb-4">
      <h3 className="text-base font-medium">
        {formData.registerMode == 'getting-started' 
        ? 'Select which founders will have vesting on their shares and their applicable vesting schedules.' 
        : 'Select which founders have vesting on their shares and their vesting schedules.'}
        
      </h3>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button className="ml-2 inline-flex items-center text-gray-500 hover:text-gray-700">
              <Info size={16} />
            </button>
          </TooltipTrigger>
          <TooltipContent className="max-w-xs p-2">
            <p className="text-sm">
              <strong>Vesting</strong> is a process where founders earn their
              shares over time, encouraging long-term commitment.
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default VestingIntro;
