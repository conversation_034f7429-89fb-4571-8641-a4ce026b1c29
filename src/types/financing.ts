export type InterestType = "Simple Interest" | "Compound Interest";

export interface ConvertibleNote {
  id: string;
  dateAuthorized: Date | null;
  authorizedAmount: number;
  outstandingPrincipal: number;
  interestRate: number;
  interestType: InterestType;
  maturityDate: Date | null;
  valuationCap: number;
  discount: number;
  mfn: boolean;
  boardApproved: boolean;
}

export interface Safe {
  id: string;
  dateAuthorized: Date | null;
  authorizedAmount: number;
  outstandingSafeAmount: number;
  valuationCap: number;
  discount: number;
  mfn: boolean;
  boardApproved: boolean;
}

export interface SafeRepurchase {
  id: string;
  selected: boolean;
  repurchaseAmount: number;
  boardApproved: boolean;
  investorName: string;
  dateIssued: Date | null;
  purchaseAmount: number;
  valuationCap: number;
  discount: number;
  mfn: boolean;
}

export interface MfnConvertibleNoteTrigger {
  currentNote: ConvertibleNote;
  newNoteTerms: ConvertibleNote;
}

export interface MfnSafeTrigger {
  currentSafe: Safe;
  newSafeTerms: Safe;
}
