import { Officer as BaseOfficer } from "@/integrations/legal-concierge/types/Company";
import { OfficerTitle, VestingSchedule, AccelerationType } from "../types";

// Extend the Officer type to include additional properties for form handling
// Override the vestingSchedule and acceleration fields to use our app types
export interface Officer
  extends Omit<BaseOfficer, "vestingSchedule" | "accleration"> {
  isNew?: boolean;
  officerTitles?: OfficerTitle[];
  // Vesting information embedded directly in officer object (overriding API types)
  isVester?: boolean;
  vestingSchedule?: VestingSchedule | null;
  acceleration?: AccelerationType | null;
}
