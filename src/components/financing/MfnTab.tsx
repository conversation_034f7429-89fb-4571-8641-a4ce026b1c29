import React, { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import MfnConvertibleNotesSection from "./mfn/MfnConvertibleNotesSection";
import MfnSafesSection from "./mfn/MfnSafesSection";
import MfnSignDocumentsSection from "./mfn/MfnSignDocumentsSection";

const MfnTab: React.FC = () => {
  const [activeSubTab, setActiveSubTab] = useState("convertible-notes");

  return (
    <div className="mt-6">
      <Tabs
        defaultValue="convertible-notes"
        value={activeSubTab}
        onValueChange={setActiveSubTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="convertible-notes">Convertible Notes</TabsTrigger>
          <TabsTrigger value="safes">SAFEs</TabsTrigger>
          <TabsTrigger value="sign-documents">Sign Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="convertible-notes">
          <MfnConvertibleNotesSection />
        </TabsContent>

        <TabsContent value="safes">
          <MfnSafesSection />
        </TabsContent>

        <TabsContent value="sign-documents">
          <MfnSignDocumentsSection />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MfnTab;
