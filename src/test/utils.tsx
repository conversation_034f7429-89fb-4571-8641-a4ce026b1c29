import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, MemoryRouter } from "react-router-dom";

// Simple test wrapper without router for basic component testing
const SimpleProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

// Mock router component for tests that need routing
const MockRouter = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="mock-router">{children}</div>
);

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <MockRouter>
      <QueryClientProvider client={queryClient}>
        <div data-testid="mock-auth-provider">
          <div data-testid="mock-company-selection-provider">{children}</div>
        </div>
      </QueryClientProvider>
    </MockRouter>
  );
};

// Router test wrapper for testing routing behavior
interface RouterTestWrapperProps {
  children: React.ReactNode;
  initialEntries?: string[];
}

export const RouterTestWrapper: React.FC<RouterTestWrapperProps> = ({
  children,
  initialEntries = ["/"],
}) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <MemoryRouter initialEntries={initialEntries}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </MemoryRouter>
  );
};

// Full app test wrapper with real router
export const AppTestWrapper: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </BrowserRouter>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, "wrapper">
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from "@testing-library/react";
export { customRender as render };
export { AllTheProviders, SimpleProviders };
