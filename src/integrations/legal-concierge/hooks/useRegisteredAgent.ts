import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import {
  RegisteredAgent,
  UpdateRegisteredAgentRequest,
} from "../types/RegisteredAgent";
import { QueryKey } from "../keys";

const api = new APIClient();

// Type guard to check if response has an error
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// Get registered agents for a company
export const useGetRegisteredAgents = (companyId: string) => {
  return useQuery({
    queryKey: [QueryKey().company, companyId, "registeredAgents"],
    queryFn: async () => {
      const response = await api.getRegisteredAgents(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }

      // The API might return a single agent or an array of agents
      // Handle both cases by converting to an array if needed
      const data = response.data;
      if (Array.isArray(data)) {
        return data as RegisteredAgent[];
      } else if (data) {
        // If it's a single object, wrap it in an array
        return [data] as RegisteredAgent[];
      }

      // Return empty array if no data
      return [] as RegisteredAgent[];
    },
    enabled: !!companyId,
  });
};

// Update registered agent
export const useUpdateRegisteredAgent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      ...payload
    }: { id: string } & UpdateRegisteredAgentRequest) =>
      api.updateRegisteredAgent(id, payload),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.id, "registeredAgents"],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.id],
      });
    },
  });
};
