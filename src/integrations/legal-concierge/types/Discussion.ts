export interface DiscussionMessageRequest {
  content: string | null;
  attachments?: string[] | null;
  replyTo?: string | null;  // UUID format
  resolved?: boolean | null;
}

export interface DiscussionMessageResponse {
  id: string;  // UUID format
  userId: string;  // UUID format
  userName: string | null;
  content: string | null;
  timestamp: string;  // date-time format
  attachments?: string[] | null;
  replyTo?: string | null;  // UUID format
  resolved?: boolean | null;
} 