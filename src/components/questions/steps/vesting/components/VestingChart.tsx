import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  CartesianGrid,
  ReferenceLine,
} from "recharts";

interface VestingChartProps {
  chartData: any[];
  maxShares: number;
  vestingTimelines: any[];
  colors: string[];
}

const VestingChart: React.FC<VestingChartProps> = ({
  chartData,
  maxShares,
  vestingTimelines,
  colors,
}) => {
  // Ensure we have data to display
  if (
    !chartData ||
    chartData.length === 0 ||
    !vestingTimelines ||
    vestingTimelines.length === 0
  ) {
    return (
      <div className="flex items-center justify-center h-[400px] bg-gray-50 rounded-lg">
        <p className="text-gray-500">No vesting data available to display</p>
      </div>
    );
  }

  // Custom tooltip formatter
  const formatTooltip = (value: any, name: string) => {
    if (!value && value !== 0) return ["-", name];
    // Format the value with commas for thousands
    return [`${value.toLocaleString()} shares`, name];
  };

  // Format axis ticks with commas for thousands
  const formatYAxisTick = (value: number) => value.toLocaleString();

  return (
    <ResponsiveContainer width="100%" height={400}>
      <LineChart
        data={chartData}
        margin={{
          top: 20,
          right: 30,
          left: 60, // Increased for better y-axis label visibility
          bottom: 60,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="label"
          angle={-45}
          textAnchor="end"
          height={80}
          label={{
            value: "Vesting Timeline (Months)",
            position: "insideBottom",
            offset: -30,
          }}
        />
        <YAxis
          label={{
            value: "Shares Vested",
            angle: -90,
            position: "insideLeft",
            offset: -5,
          }}
          domain={[0, maxShares * 1.05]} // Add 5% padding to the top
          tickFormatter={formatYAxisTick}
        />
        <Tooltip
          formatter={formatTooltip}
          labelFormatter={(label) => `Date: ${label}`}
        />
        <Legend verticalAlign="top" wrapperStyle={{ paddingBottom: "10px" }} />

        {/* Add reference line for the typical cliff at 12 months if any Standard schedule exists */}
        {vestingTimelines.some(
          (timeline) => timeline && timeline.schedule === "Standard"
        ) && (
          <ReferenceLine
            x={chartData[2]?.label}
            stroke="#ff0000"
            strokeDasharray="3 3"
            label={{
              value: "Cliff (1 Year)",
              position: "top",
              fill: "#ff0000",
            }}
          />
        )}

        {vestingTimelines.map((timeline, index) => {
          if (!timeline) return null;

          // Use stepAfter for Standard schedule (cliff) and monotone for Monthly (no cliff)
          const lineType =
            timeline.schedule === "Standard" ? "stepAfter" : "monotone";

          return (
            <Line
              key={index}
              type={lineType}
              dataKey={timeline.director}
              name={`${timeline.director} (${timeline.schedule === "Standard" ? "with cliff" : "no cliff"})`}
              stroke={colors[index % colors.length]}
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 8 }}
            />
          );
        })}
      </LineChart>
    </ResponsiveContainer>
  );
};

export default VestingChart;
