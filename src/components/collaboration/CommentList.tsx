import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import CommentItem from "./CommentItem";
import EmptyCommentState from "./EmptyCommentState";
import { Comment } from "./types";
import { InfoIcon } from "lucide-react";
import { useEditorComments } from "@/contexts/EditorCommentsContext";

interface CommentListProps {
  comments: Comment[];
  onResolve: (id: string) => void;
  isUserLoggedIn: boolean;
  loading: boolean;
  showResolved: boolean;
  isDashboard?: boolean;
  useEditorIntegration?: boolean;
}

const CommentList: React.FC<CommentListProps> = ({
  comments,
  onResolve,
  isUserLoggedIn,
  loading,
  showResolved,
  isDashboard = false,
  useEditorIntegration = false,
}) => {
  // Get editor context for navigation
  const editorContext = useEditorComments();

  // Debug: Log active comment ID changes
  console.log(
    "CommentList - Editor context active comment ID:",
    editorContext?.activeCommentId
  );

  const filteredComments = showResolved
    ? comments
    : comments.filter((comment) => !comment.resolved);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-center">
          <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
          <p className="mt-2 text-sm text-gray-500">Loading comments...</p>
        </div>
      </div>
    );
  }

  if (filteredComments.length === 0) {
    return <EmptyCommentState showResolved={showResolved} />;
  }

  return (
    <div className="flex-1 flex flex-col overflow-y-auto max-h-[400px]">
      {isDashboard && (
        <div className="mb-4 p-3 bg-blue-50 rounded-md text-sm text-blue-700 flex items-start">
          <InfoIcon className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p>
              <strong>Note:</strong> Comments can only be added and resolved
              directly on documents in the signing screen.
            </p>
            <p className="mt-1">This view is for reference only.</p>
          </div>
        </div>
      )}

      <div
        className="space-y-3 pr-2 overflow-auto"
        onClick={(e) => {
          // Clear active comment if clicking on empty space
          if (
            e.target === e.currentTarget &&
            useEditorIntegration &&
            editorContext
          ) {
            editorContext.clearActiveComment();
          }
        }}
      >
        {filteredComments.map((comment) => (
          <div
            key={comment.id}
            onClick={() => {
              console.log("Comment clicked:", comment.id, {
                useEditorIntegration,
                editorContext,
                currentActiveId: editorContext?.activeCommentId,
              });
              if (useEditorIntegration && editorContext) {
                editorContext.highlightComment(comment.id);
              }
            }}
            className={`
              transition-all duration-200 ease-in-out m-1
              ${useEditorIntegration ? "cursor-pointer hover:bg-gray-50" : ""}
              ${
                useEditorIntegration &&
                editorContext?.activeCommentId === comment.id
                  ? "ring-2 ring-blue-500 bg-blue-50 rounded-lg shadow-sm"
                  : ""
              }
            `}
          >
            <CommentItem
              comment={comment}
              onResolve={onResolve}
              isUserLoggedIn={isUserLoggedIn}
              isDashboard={isDashboard}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default CommentList;
