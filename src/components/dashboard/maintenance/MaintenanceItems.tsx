import React from "react";
import { UserPlus, FileUp, LineChart, Calendar, Users } from "lucide-react";
import { MaintenanceItemType } from "./types";
import { getServiceProviderItems } from "./ServiceProviderItems";

interface MaintenanceItemsProps {
  onSharesDialogClick: () => void;
  onOptionPlanDialogClick: () => void;
  onBoardMeetingDialogClick: () => void;
  onAdvisorClick: () => void;
  onContractorClick: () => void;
  onEmployeeClick: () => void;
  onTerminateClick: () => void;
  onPromisedGrantsClick: () => void;
}

export const getMaintenanceItems = (
  props: MaintenanceItemsProps
): MaintenanceItemType[] => {
  return [
    {
      icon: UserPlus,
      title: "Hire/Terminate Service Provider",
      description: "Manage employees, consultants and advisors.",
      buttonText: "Start",
      linkTo: "/service-providers",
    },
    {
      icon: FileUp,
      title: "Increase Authorized Shares",
      description:
        "Increase the total number of shares your company can issue.",
      buttonText: "Start",
      onClick: props.onSharesDialogClick,
    },
    {
      icon: LineChart,
      title: "Increase Stock Option Plan",
      description: "Expand your company's stock option plan for employees.",
      buttonText: "Start",
      onClick: props.onOptionPlanDialogClick,
    },
    {
      icon: Calendar,
      title: "Schedule Board Meeting",
      description: "Plan and schedule your next board of directors meeting.",
      buttonText: "Start",
      onClick: props.onBoardMeetingDialogClick,
    },
    {
      icon: Users,
      title: "Manage Directors",
      description: "Add, remove, or update your company's board of directors.",
      buttonText: "Start",
      linkTo: "/manage-directors",
    },
  ];
};
