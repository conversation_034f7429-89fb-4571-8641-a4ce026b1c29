import React from "react";
import { MessageSquare, File, Users, CheckCircle } from "lucide-react";
import TimelineItem from "./TimelineItem";

const HowItWorksSection: React.FC = () => {
  const timelineItems = [
    {
      title: "Answer simple questions",
      description:
        "Our guided wizard collects all the information needed for your incorporation documents.",
      icon: MessageSquare,
    },
    {
      title: "Review generated documents",
      description:
        "We automatically generate all required legal documents based on your inputs.",
      icon: File,
    },
    {
      title: "Sign electronically",
      description:
        "Easily sign documents and invite co-founders to review and sign as well.",
      icon: Users,
    },
    {
      title: "Complete formation and beyond",
      description:
        "Get post-incorporation guidance for setting up your new company properly.",
      icon: CheckCircle,
    },
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="container px-4 md:px-8">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <div className="inline-block bg-legal-50 px-3 py-1 rounded-full text-legal-700 text-sm font-medium mb-6">
            Simple Process
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            How Legal Concierge works
          </h2>
          <p className="text-xl text-gray-600">
            Our intuitive platform guides you through every step of the
            incorporation process
          </p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          {/* Timeline line */}
          <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-0.5 bg-legal-100 md:-ml-0.5"></div>

          {/* Timeline items */}
          <div className="space-y-12">
            {timelineItems.map((item, i) => (
              <TimelineItem
                key={i}
                index={i}
                title={item.title}
                description={item.description}
                icon={item.icon}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
