import React from "react";
import { ExternalIncorporationFormData } from "../types";
import { Card, CardContent } from "@/components/common/Card";
import { Check, AlertCircle } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface ReviewStepProps {
  formData: ExternalIncorporationFormData;
  confirmationChecked?: boolean;
  onConfirmationChange?: (checked: boolean) => void;
}

const ReviewStep: React.FC<ReviewStepProps> = ({
  formData,
  confirmationChecked = false,
  onConfirmationChange = () => {},
}) => {
  const totalPercentage = formData.stakeholders.reduce(
    (sum, stakeholder) => sum + stakeholder.stockOwnershipPercentage,
    0
  );

  const percentageIssue = Math.abs(totalPercentage - 100) > 0.01;
  const requiredDocumentsUploaded = formData.documents
    .filter((doc) => doc.isRequired)
    .every((doc) => doc.isUploaded);

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Please review all information before completing the registration.
        </p>
      </div>

      {(percentageIssue || !requiredDocumentsUploaded) && (
        <div className="p-3 bg-red-50 border border-red-100 rounded-md text-red-700 text-sm mb-4">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">
                Please correct the following issues:
              </p>
              <ul className="list-disc ml-5">
                {percentageIssue && (
                  <li>
                    Total stakeholder ownership ({totalPercentage.toFixed(2)}%)
                    does not equal 100%
                  </li>
                )}
                {!requiredDocumentsUploaded && (
                  <li>Not all required documents have been uploaded</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      )}

      <Card className="mb-4">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <Check size={18} className="text-green-500 mr-2" />
            Company Information
          </h3>
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              <div className="text-gray-500">Company Name:</div>
              <div>{formData.companyName}</div>

              <div className="text-gray-500">Address:</div>
              <div>
                {formData.companyAddress.street}, {formData.companyAddress.city}
                , {formData.companyAddress.state}{" "}
                {formData.companyAddress.zipCode}
              </div>

              <div className="text-gray-500">Incorporation Date:</div>
              <div>
                {formData.incorporationDate
                  ? new Date(formData.incorporationDate).toLocaleDateString()
                  : "Not specified"}
              </div>

              <div className="text-gray-500">Authorized Shares:</div>
              <div>{formData.authorizedShares.toLocaleString()}</div>

              <div className="text-gray-500">Issued Shares:</div>
              <div>{formData.issuedShares.toLocaleString()}</div>

              <div className="text-gray-500">Par Value per Share:</div>
              <div>${formData.parValuePerShare}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-4">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <Check size={18} className="text-green-500 mr-2" />
            Stakeholders
          </h3>
          <div className="space-y-4">
            {formData.stakeholders.length === 0 ? (
              <p className="text-gray-500 italic">No stakeholders added</p>
            ) : (
              formData.stakeholders.map((stakeholder) => (
                <div
                  key={stakeholder.id}
                  className="border-b pb-3 last:border-b-0 last:pb-0"
                >
                  <h4 className="font-medium">{stakeholder.name}</h4>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-2">
                    <div className="text-gray-500">Director:</div>
                    <div>{stakeholder.isDirector ? "Yes" : "No"}</div>

                    <div className="text-gray-500">Officer Titles:</div>
                    <div>
                      {stakeholder.officerTitles.length > 0
                        ? stakeholder.officerTitles.join(", ")
                        : "None"}
                    </div>

                    <div className="text-gray-500">Stock Ownership:</div>
                    <div>
                      {stakeholder.stockOwnershipPercentage}% (
                      {stakeholder.sharesAmount.toLocaleString()} shares)
                    </div>

                    <div className="text-gray-500">Vesting:</div>
                    <div>
                      {stakeholder.isVester
                        ? `${stakeholder.vestingSchedule} (${stakeholder.acceleration} acceleration)`
                        : "None"}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {formData.hasStockOptionPlan && (
        <Card className="mb-4">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Check size={18} className="text-green-500 mr-2" />
              Stock Option Plan
            </h3>
            <div className="space-y-2">
              <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                <div className="text-gray-500">Option Pool Percentage:</div>
                <div>{formData.stockOptionPlanPercentage}%</div>

                <div className="text-gray-500">Option Pool Shares:</div>
                <div>
                  {Math.round(
                    (formData.stockOptionPlanPercentage / 100) *
                      formData.issuedShares
                  ).toLocaleString()}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {formData.techContributions.length > 0 && (
        <Card className="mb-4">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Check size={18} className="text-green-500 mr-2" />
              Technology Contributions
            </h3>
            <div className="space-y-4">
              {formData.techContributions.map((contribution) => {
                const stakeholder = formData.stakeholders.find(
                  (s) => s.id === contribution.stakeholderId
                );

                return (
                  <div
                    key={contribution.id}
                    className="border-b pb-3 last:border-b-0 last:pb-0"
                  >
                    <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                      <div className="text-gray-500">Contributor:</div>
                      <div>{stakeholder?.name || "Unknown"}</div>

                      <div className="text-gray-500">Description:</div>
                      <div className="whitespace-pre-line">
                        {contribution.description}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="mb-4">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <Check size={18} className="text-green-500 mr-2" />
            Registered Agent
          </h3>
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-x-4 gap-y-1">
              <div className="text-gray-500">Name:</div>
              <div>{formData.registeredAgent.name}</div>

              <div className="text-gray-500">Address:</div>
              <div>{formData.registeredAgent.address}</div>

              {formData.registeredAgent.email && (
                <>
                  <div className="text-gray-500">Email:</div>
                  <div>{formData.registeredAgent.email}</div>
                </>
              )}

              {formData.registeredAgent.phone && (
                <>
                  <div className="text-gray-500">Phone:</div>
                  <div>{formData.registeredAgent.phone}</div>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-4">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <Check size={18} className="text-green-500 mr-2" />
            Documents
          </h3>
          <div className="space-y-2">
            <ul className="divide-y">
              {formData.documents.map((doc) => (
                <li
                  key={doc.id}
                  className="py-2 flex justify-between items-center"
                >
                  <div>
                    <span className={doc.isRequired ? "font-medium" : ""}>
                      {doc.name}
                    </span>
                    {doc.isRequired && (
                      <span className="text-xs ml-1 text-red-500">
                        (Required)
                      </span>
                    )}
                  </div>
                  <div>
                    {doc.isUploaded ? (
                      <span className="text-green-600 text-sm flex items-center">
                        <Check size={14} className="mr-1" />
                        Uploaded
                      </span>
                    ) : (
                      <span className="text-gray-400 text-sm">
                        Not uploaded
                      </span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg border">
        <div className="flex items-start space-x-3">
          <Checkbox
            id="confirmation"
            checked={confirmationChecked}
            onCheckedChange={(checked) =>
              onConfirmationChange(checked === true)
            }
            disabled={percentageIssue || !requiredDocumentsUploaded}
          />
          <div>
            <Label htmlFor="confirmation" className="font-medium">
              I confirm that all the information provided is accurate and
              complete.
            </Label>
            <p className="text-sm text-gray-600 mt-1">
              By checking this box, you confirm that all company information,
              stakeholders, and documents are correctly uploaded and
              represented. After submission, this information will be used for
              post-incorporation tasks.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewStep;
