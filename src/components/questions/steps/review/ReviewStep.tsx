import React from "react";
import { QuestionnaireFormData } from "../../types";
import ReviewConfirmation from "./ReviewConfirmation";
import CompanyInfoSection from "./sections/CompanyInfoSection";
import ShareStructureSection from "./sections/ShareStructureSection";
import StakeholderSection from "./sections/StakeholderSection";
import VestingInfoSection from "./sections/VestingInfoSection";
import OtherInfoSection from "./sections/OtherInfoSection";
import { toast } from "sonner";

interface ReviewStepProps {
  formData: QuestionnaireFormData;
  onConfirmationChange?: (checked: boolean) => void;
  confirmationChecked?: boolean;
}

// Export the save function so it can be called from the continue button
export const saveReviewConfirmationData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  confirmCompanyForm: any
): Promise<boolean> => {
  try {
    if (!formData.isFormConfirmed) {
      toast.error(
        "Please confirm that the information is accurate and complete"
      );
      return false;
    }

    console.log("Confirming company form for companyId:", companyId);

    await confirmCompanyForm.mutateAsync(companyId);

    console.log("Company form confirmed successfully");
    toast.success("Form confirmation saved successfully");
    return true;
  } catch (error) {
    console.error("Error confirming company form:", error);
    toast.error("Failed to save form confirmation");
    return false;
  }
};

const ReviewStep: React.FC<ReviewStepProps> = ({
  formData,
  onConfirmationChange,
  confirmationChecked = false,
}) => {
  return (
    <div className="space-y-6">
      <div className="bg-legal-50 border border-legal-100 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold text-legal-900 mb-2">
          {formData.registerMode == "getting-started"
            ? `Review your information below before generating your documents`
            : `Review your information below before uploading your documents`}
        </h2>
        <p className="text-legal-700">
          {formData.registerMode == "getting-started"
            ? ` Please confirm all details are correct. You will not be able to make
          changes after document generation.`
            : ` Please confirm all details are correct. You will not be able to make
          changes after document upload.`}
        </p>
      </div>

      <div className="space-y-6">
        <CompanyInfoSection formData={formData} />
        <ShareStructureSection formData={formData} />
        <StakeholderSection formData={formData} />
        <VestingInfoSection formData={formData} />
        <OtherInfoSection formData={formData} />

        <ReviewConfirmation
          checked={confirmationChecked}
          onCheckedChange={onConfirmationChange}
          formData={formData}
        />
      </div>
    </div>
  );
};

export default ReviewStep;
