import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { QuestionnaireFormData } from "../types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { US_STATES } from "@/components/incorporation/types";
import { AlertTriangle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { toast } from "sonner";

interface CompanyAddressStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void;
}

// Export the save function so it can be called from the continue button
export const saveCompanyAddressData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  updateCompanyAddress: any
): Promise<boolean> => {
  try {
    const payload = {
      id: companyId,
      companyId: companyId,
      streetAddress: formData.companyAddressStructured.street1,
      streetAddress2: formData.companyAddressStructured.street2,
      city: formData.companyAddressStructured.city,
      state: formData.companyAddressStructured.state,
      zipCode: formData.companyAddressStructured.zipCode,
    };

    console.log("Saving company address with payload:", payload);

    await updateCompanyAddress.mutateAsync(payload);

    console.log("Company address saved successfully");
    toast.success("Company address information saved successfully");
    return true;
  } catch (error) {
    console.error("Error saving company address:", error);
    toast.error("Failed to save company address information");
    return false;
  }
};

const CompanyAddressStep: React.FC<CompanyAddressStepProps> = ({
  formData,
  updateFormData,
}) => {
  const [addressVerified, setAddressVerified] = useState<boolean | null>(null);
  const [localAddress, setLocalAddress] = useState(
    formData.companyAddressStructured
  );
  const [isUserEditing, setIsUserEditing] = useState(false);
  const { toast } = useToast();

  // Only update local state from formData if user is not actively editing
  useEffect(() => {
    if (!isUserEditing) {
      setLocalAddress(formData.companyAddressStructured);
    }
  }, [formData.companyAddressStructured, isUserEditing]);

  // Reset editing state when user stops typing (debounced)
  useEffect(() => {
    if (isUserEditing) {
      const timer = setTimeout(() => {
        setIsUserEditing(false);
      }, 1000); // 1 second after user stops typing

      return () => clearTimeout(timer);
    }
  }, [localAddress, isUserEditing]);

  // Auto-verify the address whenever any field changes
  useEffect(() => {
    // Only verify if at least one field has been filled out
    if (
      localAddress.street1 ||
      localAddress.city ||
      localAddress.state ||
      localAddress.zipCode
    ) {
      verifyAddress();
    }
  }, [
    localAddress.street1,
    localAddress.street2,
    localAddress.city,
    localAddress.state,
    localAddress.zipCode,
  ]);

  const handleAddressChange = (field: string, value: string) => {
    const updatedAddress = { ...localAddress };
    updatedAddress[field as keyof typeof updatedAddress] = value;

    setLocalAddress(updatedAddress);
    setIsUserEditing(true);
    updateFormData("companyAddressStructured", updatedAddress);
  };

  const verifyAddress = () => {
    const { street1, city, state, zipCode } = localAddress;

    // Simple validation first
    if (!street1 || !city || !state || !zipCode) {
      setAddressVerified(false);
      return;
    }

    // For demo purposes, we'll use a simple validation:
    // ZIP code format validation (basic 5-digit US zip)
    const zipRegex = /^\d{5}$/;
    const isValidZip = zipRegex.test(zipCode);

    // Set as verified only if zip is valid
    setAddressVerified(isValidZip);

    if (isValidZip && addressVerified !== true) {
      toast({
        title: "Address validated",
        description: "The address format appears to be valid.",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="street1">Street Address</Label>
          <Input
            id="street1"
            value={localAddress.street1}
            onChange={(e) => handleAddressChange("street1", e.target.value)}
            placeholder="123 Main St"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="street2">Street Address 2 (optional)</Label>
          <Input
            id="street2"
            value={localAddress.street2}
            onChange={(e) => handleAddressChange("street2", e.target.value)}
            placeholder="Suite 100"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="city">City</Label>
          <Input
            id="city"
            value={localAddress.city}
            onChange={(e) => handleAddressChange("city", e.target.value)}
            placeholder="San Francisco"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="state">State</Label>
            <Select
              value={localAddress.state}
              onValueChange={(value) => handleAddressChange("state", value)}
            >
              <SelectTrigger id="state">
                <SelectValue placeholder="Select state" />
              </SelectTrigger>
              <SelectContent>
                {US_STATES.map((state) => (
                  <SelectItem key={state.value} value={state.value}>
                    {state.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="zipCode">ZIP Code</Label>
            <Input
              id="zipCode"
              value={localAddress.zipCode}
              onChange={(e) => handleAddressChange("zipCode", e.target.value)}
              placeholder="94105"
            />
          </div>
        </div>

        {addressVerified === false && (
          <div className="mt-2 p-3 bg-red-50 border border-red-100 rounded-md flex items-start">
            <AlertTriangle className="text-red-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-red-800">
                Invalid address
              </p>
              <p className="text-xs text-red-700 mt-1">
                Please check all fields and ensure your ZIP code is valid (5
                digits).
              </p>
            </div>
          </div>
        )}

        {/* Delaware incorporation notice for non-Delaware states */}
        {localAddress.state && localAddress.state !== "DE" && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
            <p className="text-sm text-blue-800">
              We will incorporate your company in Delaware, which offers the
              most business-friendly corporate laws in the United States.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompanyAddressStep;
