import React from "react";
import CollaboratorsList from "./CollaboratorsList";
import DiscussionThread from "./DiscussionThread";
import DocumentComments from "./DocumentComments";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { useAuth } from "@/contexts/AuthContext";

const CollaborationSection: React.FC = () => {
  const { user } = useAuth();
  return (
    <AnimatedTransition delay={0.2}>
      <h2 className="text-2xl font-bold text-gray-900 mb-4">
        Team Collaboration
      </h2>

      <div className="space-y-6">
        <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">
            Collaborate with Your Team
          </h3>
          <p className="text-blue-700">
            {user?.roles?.includes('OWNER') ? 
            "Invite co-founders to review your incorporation documents, leave comments, and discuss important decisions together."
            : "Review your incorporation documents, leave comments, and discuss important decisions together."}
            
          </p>
        </div>

        <CollaboratorsList />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <DiscussionThread />
          <DocumentComments isDashboard={true} />
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default CollaborationSection;
