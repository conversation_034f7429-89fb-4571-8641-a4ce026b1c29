// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://bhduszuortuiynlkcmlj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJoZHVzenVvcnR1aXlubGtjbWxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyMzY2OTgsImV4cCI6MjA1NzgxMjY5OH0.QIZZ4NpZxWxK8ZUnoeT6-pGhso2bmRzL-BgBTLw_9Zc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY
);
