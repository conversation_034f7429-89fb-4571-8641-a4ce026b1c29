import { Comment } from "@/components/collaboration/types";
import { DocumentCommentsContext } from "@/components/signature/DocumentSigningContainer";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { api } from "@/integrations/legal-concierge/api";
import { Document as FFDocument } from "@/integrations/legal-concierge/types/Document";
import { createOnlyOfficeConfig } from "@/utils/documentUtils";
import { formatDistanceToNow } from "date-fns";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import DocumentComments from "../collaboration/DocumentComments";
import { Editor } from "../tiptap/editor";
import { Toolbar } from "../tiptap/toolbar";
import { convertDocxUrlToHtml } from "./convertDocxUrlToHtml";
import { EditorCommentsProvider } from "@/contexts/EditorCommentsContext";

interface OnlyOfficeConfig {
  document: {
    fileType: string;
    key: string;
    title: string;
    url: string;
    permissions: {
      edit: boolean;
      download: boolean;
      forcesave: boolean;
    };
  };
  documentType: string;
  editorConfig: {
    mode: string;
    user: {
      id: string;
      name: string;
    };
  };
  width: string;
  height: string;
}

interface OnlyOfficeEditor {
  destroyEditor: (save?: boolean) => void;
}

declare global {
  interface Window {
    DocsAPI: {
      DocEditor: new (id: string, config: OnlyOfficeConfig) => OnlyOfficeEditor;
    };
  }
}

interface DocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  ffdocument: FFDocument;
  mode?: "view" | "edit";
}

const ONLYOFFICE_SCRIPT_ID = "onlyoffice-api-script";
const ONLYOFFICE_PLACEHOLDER_ID = "onlyoffice-placeholder";

const DocumentModal: React.FC<DocumentModalProps> = ({
  isOpen,
  onClose,
  ffdocument,
  mode = "view",
}) => {
  const editorRef = useRef<OnlyOfficeEditor | null>(null);
  const [loaded, setLoaded] = useState(false);
  const placeholderRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);

  const [documentContent, setDocumentContent] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && ffdocument?.url) {
      convertDocxUrlToHtml(ffdocument.url)
        .then((html) => {
          console.log("Converted HTML:", html);
          setDocumentContent(html);
        })
        .catch((err) => {
          console.error("Conversion failed:", err);
          setDocumentContent(null);
        });
    }
  }, [isOpen, ffdocument?.url]);

  // Reset loading state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setLoading(true);
    } else {
      setLoading(false);
      setComments([]); // Clear comments when modal closes
    }
  }, [isOpen]);

  // Load the OnlyOffice script
  useEffect(() => {
    if (!loaded && isOpen) {
      // Check if script already exists
      const existingScript = document.getElementById(ONLYOFFICE_SCRIPT_ID);
      if (existingScript) {
        setLoaded(true);
        return;
      }

      const script = document.createElement("script");
      script.id = ONLYOFFICE_SCRIPT_ID;
      script.src =
        "https://docs.foundersform.com/web-apps/apps/api/documents/api.js";
      script.async = true;
      script.onload = () => {
        console.log("OnlyOffice script loaded successfully");
        setLoaded(true);
      };
      script.onerror = (error) => {
        console.error("Failed to load OnlyOffice script:", error);
      };
      document.head.appendChild(script);

      // Don't remove the script on cleanup
      return;
    }
  }, [loaded, isOpen]);

  // Initialize the editor
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const initEditor = async () => {
      if (!window.DocsAPI) {
        console.error("DocsAPI not found");
        return;
      }

      const placeholder = document.getElementById(ONLYOFFICE_PLACEHOLDER_ID);
      if (!placeholder || !user) {
        console.error("Placeholder element or user not found");
        return;
      }

      try {
        // Create the config
        const config = createOnlyOfficeConfig(
          {
            fileName: ffdocument.fileName,
            url: ffdocument.url,
          },
          mode,
          {
            id: user.id,
            name: user.email?.split("@")[0] || "Anonymous",
          }
        );

        // Get the token
        console.log("Getting document token", user.companyId);
        const tokenResponse = await api.getDocumentToken(
          config,
          user.companyId
        );
        if (tokenResponse.error || !tokenResponse.data?.token) {
          throw new Error(
            tokenResponse.error || "Failed to get document token"
          );
        }

        // Add token to config
        const configWithToken = {
          ...config,
          token: tokenResponse.data.token,
        };

        if (editorRef.current) {
          editorRef.current.destroyEditor();
          editorRef.current = null;
        }

        console.log("Initializing OnlyOffice editor");
        const docEditor = new window.DocsAPI.DocEditor(
          ONLYOFFICE_PLACEHOLDER_ID,
          configWithToken
        );
        editorRef.current = docEditor;
      } catch (error) {
        console.error("Error initializing OnlyOffice editor:", error);
        toast.error("Failed to initialize document viewer");
      }
    };

    if (loaded && isOpen) {
      timeoutId = setTimeout(initEditor, 500);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      if (editorRef.current) {
        try {
          editorRef.current.destroyEditor();
          editorRef.current = null;
        } catch (error) {
          console.error("Error destroying editor:", error);
        }
      }
    };
  }, [loaded, isOpen, ffdocument, user, mode]);

  // Fetch comments when the modal opens
  useEffect(() => {
    const fetchComments = async () => {
      if (!isOpen || !ffdocument?.fileName || !user?.companyId) {
        setLoading(false); // Set loading to false if we're not going to fetch
        return;
      }

      try {
        setLoading(true);
        const response = await api.getDocumentComments(
          user.companyId,
          ffdocument.fileName
        );

        console.log("API Response:", response);

        if (response.error) {
          throw new Error(response.error);
        }

        // Ensure response.data is an array
        const commentsData = Array.isArray(response.data)
          ? response.data
          : [response.data].filter(Boolean);

        console.log("Formatted Comments Data:", commentsData);

        // Transform the API response to match our Comment type
        const formattedComments: Comment[] = commentsData.map((comment) => {
          const timestamp = (() => {
            try {
              const date = new Date(comment.timestamp);
              return isNaN(date.getTime()) ? new Date() : date;
            } catch {
              return new Date();
            }
          })();

          return {
            id: comment.id,
            userId: comment.userId,
            userName: comment.userName || comment.userId || "Anonymous",
            documentId: comment.documentIdentifier,
            content: comment.comment,
            timestamp,
            position: comment.position,
            resolved: comment.resolved,
            statusText: comment.statusText,
          };
        });

        console.log("Final Formatted Comments:", formattedComments);
        setComments(formattedComments);
      } catch (error) {
        console.error("Error fetching comments:", error);
        toast.error("Failed to load comments");
      } finally {
        setLoading(false);
      }
    };

    fetchComments();
  }, [isOpen, ffdocument?.fileName, user?.companyId]);

  const addComment = async (commentData: {
    content: string;
    documentId: string;
    position?: { x: number; y: number };
  }) => {
    if (!user?.companyId) {
      toast.error("User or company ID not found");
      return;
    }

    if (!commentData.documentId) {
      toast.error("Document name is missing");
      return;
    }

    try {
      const response = await api.createDocumentComment(
        user.companyId,
        commentData.documentId,
        {
          comment: commentData.content,
          position: commentData.position,
          resolved: false,
        }
      );

      if (response.error || !response.data) {
        throw new Error(response.error || "Failed to create comment");
      }

      const timestamp = (() => {
        try {
          const date = new Date(response.data.createdAt);
          return isNaN(date.getTime()) ? new Date() : date;
        } catch {
          return new Date();
        }
      })();

      // Add the new comment to state
      const formattedComment: Comment = {
        id: response.data.id,
        userId: response.data.userId,
        userName: response.data.userName || response.data.userId || "Anonymous",
        documentId: response.data.documentIdentifier,
        content: response.data.comment,
        timestamp,
        position: response.data.position,
        resolved: response.data.resolved,
        statusText: response.data.statusText,
      };
      console.log("New add comment response:", response);

      setComments((prevComments) => [formattedComment, ...prevComments]);
      toast.success("Comment added successfully");
    } catch (error) {
      console.error("Error in addComment:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to add comment"
      );
    }
  };

  const handleClose = () => {
    if (editorRef.current) {
      try {
        editorRef.current.destroyEditor();
        editorRef.current = null;
      } catch (error) {
        console.error("Error destroying editor:", error);
      }
    }
    onClose();
  };
  console.log({ ffdocument });
  return (
    <DocumentCommentsContext.Provider
      value={{
        comments,
        addComment: async (commentData) => {
          if (!ffdocument?.fileName) {
            toast.error("Document name is missing");
            return;
          }
          return addComment({
            ...commentData,
            documentId: ffdocument.fileName,
          });
        },
        resolveComment: async (id) => {
          try {
            const response = await api.updateDocumentComment(id, {
              comment: comments.find((c) => c.id === id)?.content || "",
              resolved: true,
            });

            if (response.error) {
              throw new Error(response.error);
            }

            setComments((prevComments) =>
              prevComments.map((comment) =>
                comment.id === id
                  ? {
                      ...comment,
                      resolved: true,
                      statusText:
                        "Resolved " +
                        formatDistanceToNow(new Date(), { addSuffix: true }),
                    }
                  : comment
              )
            );
            toast.success("Comment resolved successfully");
          } catch (error) {
            console.error("Error in resolveComment:", error);
            toast.error("Failed to resolve comment");
          }
        },
      }}
    >
      <EditorCommentsProvider initialDocumentId={ffdocument.fileName}>
        <Dialog open={isOpen} onOpenChange={handleClose}>
          <DialogContent className="max-w-[95vw] w-[95vw] h-[95vh] p-4 flex flex-col">
            <DialogHeader className="mb-2">
              <DialogTitle>
                {ffdocument?.fileName || "Document"} Viewer
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-[2fr,1fr] gap-4 h-[calc(95vh-5rem)]">
              <div className="bg-editor-bg flex flex-col h-full justify-start align-top ">
                <div className=" px-4 pt-2 bg-[#FAFBFD] print:hidden ">
                  <Toolbar />
                </div>
                <div className="flex-1 print:pt-0 overflow-auto  max-h-[calc(100vh-160px)]">
                  <Editor
                    initialContent={documentContent}
                    documentId={ffdocument.fileName}
                  />
                </div>
              </div>
              <div className="h-full">
                <DocumentComments
                  loading={loading}
                  useEditorIntegration={true}
                  isDashboard={false}
                  documentId={ffdocument.fileName}
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </EditorCommentsProvider>
    </DocumentCommentsContext.Provider>
  );
};

export default DocumentModal;
