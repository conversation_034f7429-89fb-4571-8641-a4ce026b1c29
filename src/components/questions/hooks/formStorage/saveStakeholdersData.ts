import { APIClient } from "@/integrations/legal-concierge/client";
import { OfficerRequest } from "@/integrations/legal-concierge/types/Officer";
import { OfficerUpdateRequest } from "@/integrations/legal-concierge/types";
import { QuestionnaireFormData } from "./types";
import { Officer } from "@/integrations/legal-concierge/types/Company";

// Save stakeholders data to legal-concierge API
export const saveStakeholdersData = async (
  _userId: string, // Keeping parameter for API consistency but not using it
  companyId: string,
  formData: QuestionnaireFormData
): Promise<void> => {
  try {
    const api = new APIClient();

    // Get company details to get existing officers
    const companyDetails = await api.getCompanyById(companyId);
    const existingOfficers = companyDetails.officers || [];

    // Process each director in the form data
    for (const director of formData.directors as Officer[]) {
      // Get officer title flags directly from the director object
      const isCEO = director.isCEO;
      const isPresident = director.isPresident;
      const isSecretary = director.isSecretary;
      const isTreasurer = director.isTreasurer;
      const notApplicable = director.notApplicable;

      // Check if this officer already exists
      const existingOfficer = existingOfficers.find(
        (o) => o.name === director.name
      );

      if (existingOfficer) {
        // Update existing officer
        const updateData: OfficerUpdateRequest = {
          name: director.name,
          emailAddress: director.emailAddress,
          contactAddress: director.contactAddress,
          stockOwnership: director.stockOwnership,
          amountOfShares: director.amountOfShares,
          isDirector: director.isDirector,
          isCEO,
          isPresident,
          isSecretary,
          isTreasurer,
          notApplicable,
        };

        // We need the officer ID to update
        // For now, we'll use a placeholder - in a real implementation,
        // you would need to store and retrieve the actual officer IDs
        const officerId = director.id; // This assumes the ID is stored correctly
        await api.updateOfficer(officerId, updateData);
      } else {
        // Add new officer
        const officerData: OfficerRequest = {
          companyId,
          officerId: null, // API will generate an ID
          name: director.name,
          emailAddress: director.emailAddress,
          contactAddress: director.contactAddress,
          stockOwnership: director.stockOwnership,
          amountOfShares: director.amountOfShares,
          isDirector: director.isDirector,
          isCEO,
          isPresident,
          isSecretary,
          isTreasurer,
          notApplicable,
        };

        await api.addOfficer(companyId, officerData);
      }
    }

    // Note: Deleting officers that are no longer in the form data
    // would require additional API calls to get all officers and then
    // delete the ones not in the form data. This is left as a future enhancement.
  } catch (error) {
    console.error("Error saving stakeholders data:", error);
    throw error;
  }
};
