import { useQuery } from "@tanstack/react-query";
import { APIClient } from "../client";
import { User } from "@/contexts/auth/types";
import { QueryKey } from "../keys";

const api = new APIClient();

// Type guard to check if response has an error
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// User role constants
export const USER_ROLES = {
  OWNER: "OWNER",
  COLLABORATOR: "COLLABORATOR",
  SIGNER: "SIGNER",
  ADMIN: "ADMIN",
} as const;

export type UserRole = (typeof USER_ROLES)[keyof typeof USER_ROLES];

// Role-based permission utilities (company-aware)
export const useUserPermissions = (
  user: User | null,
  isCompanySelected: boolean = true
) => {
  // If company is not selected, return minimal permissions
  if (!isCompanySelected || !user?.companyId) {
    return {
      // Basic role checks
      isOwner: false,
      isCollaborator: false,
      isSigner: false,

      // Permission checks - all false until company is selected
      canEditForms: false,
      canViewForms: false,
      canConfirmReview: false,
      canAccessPostIncorporation: false,
      canManageCollaborators: false,
      canEditCompanyData: false,
      canViewDataRoom: false,
      canUploadDocuments: false,
      canCommentOnDocuments: false,

      // Navigation permissions - all false until company is selected
      canAccessQuestionnaire: false,
      canAccessReviewPage: false,
      canAccessDataRoomAfterSignature: false,
      canAccessDashboard: false,

      // SIGNER-specific permissions
      isSignerOnly: false,
      canOnlyAccessDataRoom: false,

      // Company selection state
      needsCompanySelection: true,
    };
  }

  // Company is selected, evaluate roles normally
  const isOwner = user?.roles?.includes(USER_ROLES.OWNER) ?? false;
  const isCollaborator =
    user?.roles?.includes(USER_ROLES.COLLABORATOR) ?? false;
  const isSigner = user?.roles?.includes(USER_ROLES.SIGNER) ?? false;

  return {
    // Basic role checks
    isOwner,
    isCollaborator,
    isSigner,

    // Permission checks
    canEditForms: isOwner,
    canViewForms: isOwner || isCollaborator, // SIGNER cannot view forms
    canConfirmReview: isOwner,
    canAccessPostIncorporation: isOwner,
    canManageCollaborators: isOwner,
    canEditCompanyData: isOwner,
    canViewDataRoom: isOwner || isCollaborator || isSigner, // SIGNER can view data room
    canUploadDocuments: isOwner,
    canCommentOnDocuments: isOwner || isCollaborator, // SIGNER cannot comment

    // Navigation permissions
    canAccessQuestionnaire: isOwner || isCollaborator, // SIGNER cannot access questionnaire
    canAccessReviewPage: isOwner || isCollaborator, // SIGNER cannot access review page
    canAccessDataRoomAfterSignature: isOwner || isCollaborator || isSigner, // SIGNER can access after signature
    canAccessDashboard: isOwner || isCollaborator, // SIGNER cannot access dashboard

    // SIGNER-specific permissions
    isSignerOnly: isSigner && !isOwner && !isCollaborator,
    canOnlyAccessDataRoom: isSigner && !isOwner && !isCollaborator,

    // Company selection state
    needsCompanySelection: false,
  };
};

// Get user profile information
export const useUserProfile = () => {
  return useQuery<User, Error>({
    queryKey: [QueryKey().user],
    queryFn: async () => {
      const response = await api.profile();
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data as User;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
