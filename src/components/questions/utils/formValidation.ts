import { QuestionnaireFormData, OfficerTitle, FormStep } from "../types";
import { toast } from "sonner";

export const validateStep = (
  step: FormStep,
  formData: QuestionnaireFormData,
  showNameDialog: () => void,
  showIncorporationDialog: () => void,
  showSharesDialog: () => void
): boolean => {
  switch (step) {
    case "company-name": {
      if (!formData.companyName) {
        toast.error("Please enter a company name");
        return false;
      }
      if (
        formData.registerMode == "getting-started" &&
        !formData.nameAvailabilityChecked
      ) {
        showNameDialog();
        return false;
      }
      if (
        formData.registerMode == "getting-started" &&
        formData.incorporatingToday === null
      ) {
        toast.error("Please select whether you are incorporating today");
        return false;
      }
      if (
        formData.registerMode == "getting-started" &&
        formData.incorporatingToday === false
      ) {
        if (!formData.incorporationDate) {
          toast.error("Please select your incorporation date");
          return false;
        }
        showIncorporationDialog();
        return false;
      } else if (
        formData.registerMode == "getting-started" &&
        formData.incorporatingToday === true &&
        !formData.incorporationDate
      ) {
        // If incorporating today but incorporation date isn't set
        toast.error("Please confirm incorporation date");
        return false;
      } else if (
        formData.registerMode == "getting-started" &&
        formData.incorporatingToday === true &&
        formData.incorporationDate
      ) {
        showIncorporationDialog();
        return false;
      }

      if (
        formData.registerMode == "register-existing" &&
        !formData.incorporationDate
      ) {
        toast.error("Please select your incorporation date");
        return false;
      }
      return true;
    }

    case "company-address": {
      const { street1, city, state, zipCode } =
        formData.companyAddressStructured;

      if (!street1) {
        toast.error("Please enter a street address");
        return false;
      }

      if (!city) {
        toast.error("Please enter a city");
        return false;
      }

      if (!state) {
        toast.error("Please select a state");
        return false;
      }

      if (!zipCode) {
        toast.error("Please enter a ZIP code");
        return false;
      }

      // Validate ZIP code format (5-digit US zip)
      const zipRegex = /^\d{5}$/;
      if (!zipRegex.test(zipCode)) {
        toast.error("Please enter a valid 5-digit ZIP code");
        return false;
      }

      return true;
    }

    case "authorized-shares": {
      if (formData.issuedShares > formData.authorizedShares) {
        showSharesDialog();
        return false;
      }
      if (!formData.authorizedShares || !formData.parValuePerShare) {
        toast.error("Please complete all fields");
        return false;
      }
      return true;
    }

    case "directors-and-officers": {
      if (formData.directors.length === 0) {
        toast.error("Please add at least one director/officer/shareholder");
        return false;
      }

      // Check if at least one person is appointed as a director
      const hasDirector = formData.directors.some(
        (person) => person.isDirector === true
      );
      if (!hasDirector) {
        toast.error("Please appoint at least one person as a director");
        return false;
      }

      // Check if all required officer roles are assigned
      const requiredRoles: OfficerTitle[] = [
        "CEO",
        "President",
        "Treasurer",
        "Secretary",
      ];

      // Extract assigned roles from the director properties
      const assignedRoles: OfficerTitle[] = [];
      formData.directors.forEach((director) => {
        if (director.isCEO) assignedRoles.push("CEO");
        if (director.isPresident) assignedRoles.push("President");
        if (director.isSecretary) assignedRoles.push("Secretary");
        if (director.isTreasurer) assignedRoles.push("Treasurer");
      });

      const missingRoles = requiredRoles.filter(
        (role) => !assignedRoles.includes(role)
      );

      if (missingRoles.length > 0) {
        toast.error(
          `Please assign all required officer roles: ${missingRoles.join(", ")}`
        );
        return false;
      }
      return true;
    }

    case "stock-plan": {
      if (formData.directors.length === 0) {
        toast.error(
          "Please add at least one director/officer/shareholder in the previous step"
        );
        return false;
      }

      // Check if percentages add up correctly based on stock option plan
      const totalDirectorPercentage = formData.directors.reduce(
        (sum, director) => sum + director.stockOwnership,
        0
      );

      // Auto-adjust stock option plan percentage if needed
      if (formData.includeStockOptionPlan) {
        // When stock option plan is included, directors + option plan should equal 100%
        const totalPercentage =
          totalDirectorPercentage + formData.stockOptionPlanPercentage;

        if (Math.abs(totalPercentage - 100) > 0.01) {
          // Auto-adjust the stock option plan percentage to make total 100%
          const adjustedOptionPlanPercentage = Math.max(
            0,
            100 - totalDirectorPercentage
          );

          // Update the form data with the adjusted percentage
          formData.stockOptionPlanPercentage = adjustedOptionPlanPercentage;

          // Inform the user about the adjustment
          toast.info(
            `Stock option plan percentage adjusted to ${adjustedOptionPlanPercentage.toFixed(2)}% to make total 100%`
          );
        }
      } else {
        // When no stock option plan, directors alone should equal 100%
        if (Math.abs(totalDirectorPercentage - 100) > 0.01) {
          toast.error("Stock ownership percentages must add up to 100%");
          return false;
        }
      }

      // The API call will be handled by the saveFormData function in useQuestionsForm
      return true;
    }

    case "vesting": {
      // Check if any directors have vesting enabled
      const directorsWithVesting = formData.directors.filter((director) => {
        const vestingInfo = formData.vestingInfo.find(
          (v) => v.id === director.id
        );
        return vestingInfo?.isVester && vestingInfo?.vestingSchedule;
      });

      // If vesting is enabled for any director, the Section 83(b) confirmation is required
      if (directorsWithVesting.length > 0 && !formData.isVestingConfirmed) {
        toast.error(
          "Please review and confirm the Section 83(b) information before continuing"
        );
        return false;
      }
      return true;
    }

    case "technology": {
      // Validation for tech step
      // We'll accept either the legacy companyTech field or the new founderTechAssignments
      if (
        formData.directors.length > 0 &&
        formData.founderTechAssignments.length === 0
      ) {
        toast.error("Please initialize the tech assignments for founders");
        return false;
      }
      return true;
    }

    default:
      return true;
  }
};
