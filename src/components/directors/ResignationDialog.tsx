import React, { useState } from "react";
import { FileUp } from "lucide-react";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ResignationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUploadResignation: () => Promise<void>;
  onFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  resignationFile: File | null;
}

const ResignationDialog: React.FC<ResignationDialogProps> = ({
  open,
  onOpenChange,
  onUploadResignation,
  onFileChange,
  resignationFile,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Upload Resignation Letter</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="resignation-letter">Resignation Letter</Label>
            <Input
              id="resignation-letter"
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={onFileChange}
            />
            <p className="text-sm text-gray-500">
              Please upload the director's resignation letter (PDF, DOC, or
              DOCX)
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onUploadResignation} disabled={!resignationFile}>
            <FileUp className="h-4 w-4 mr-2" />
            Upload & Confirm Resignation
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ResignationDialog;
