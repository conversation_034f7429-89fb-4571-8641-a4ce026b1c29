# Dashboard Routing Tests

This directory contains comprehensive test cases for the dashboard routing functionality in the legal-concierge application.

## Test File: `DashboardRouting.test.tsx`

### Purpose
Tests the role-based dashboard routing logic to ensure users are redirected to the correct dashboard based on their roles.

### Key Requirements Tested

1. **Admin users should land in the regular dashboard page** (`/dashboard`)
2. **Only SIGNER users should land in the signer-dashboard** (`/signer-dashboard`)
3. **All non-SIGNER roles (OWNER, COLLABORATOR, ADMIN) should go to regular dashboard**

### Test Coverage

#### AuthRedirect Component Tests (7 tests)
- ✅ ADMIN users → `/dashboard`
- ✅ OWNER users → `/dashboard`
- ✅ COLLABORATOR users → `/dashboard`
- ✅ SIGNER users → `/signer-dashboard`
- ✅ Loading state handling
- ✅ Non-authenticated user handling
- ✅ Company selection guard behavior

#### Login Component Tests (6 tests)
- ✅ ADMIN users → `/dashboard` (from login)
- ✅ OWNER users → `/dashboard` (from login)
- ✅ COLLABORATOR users → `/dashboard` (from login)
- ✅ SIGNER users → `/signer-dashboard` (from login)
- ✅ Multi-role users (ADMIN + OWNER) → `/dashboard`
- ✅ Mixed roles (SIGNER + COLLABORATOR) → `/dashboard` (non-signer takes precedence)

#### Summary Requirements Tests (3 tests)
- ✅ **REQUIREMENT**: Admin should land in the regular dashboard page
- ✅ **REQUIREMENT**: Only SIGNER should land in the signer-dashboard
- ✅ **REQUIREMENT**: All non-SIGNER roles should go to regular dashboard

### Role-Based Routing Logic

The application implements the following routing logic:

```typescript
// In AuthRedirect.tsx and Login.tsx
if (permissions.isSigner) {
  return <Navigate to="/signer-dashboard" replace />;
}
return <Navigate to="/dashboard" replace />;
```

### User Roles

- **ADMIN**: Administrative users with full system access → `/dashboard`
- **OWNER**: Company owners with full company access → `/dashboard`
- **COLLABORATOR**: Users with limited company access → `/dashboard`
- **SIGNER**: Users with minimal access, only for signing documents → `/signer-dashboard`

### Test Utilities

The tests use custom test utilities from `@/test/utils`:
- `RouterTestWrapper`: Provides routing context for testing
- Mock implementations for auth hooks and navigation

### Running the Tests

```bash
# Run all dashboard routing tests
npm test -- src/components/auth/__tests__/DashboardRouting.test.tsx

# Run tests in watch mode
npm test -- src/components/auth/__tests__/DashboardRouting.test.tsx --watch

# Run tests with coverage
npm test -- src/components/auth/__tests__/DashboardRouting.test.tsx --coverage
```

### Test Results

All 16 tests pass successfully, confirming that:
- Admin users are correctly routed to the regular dashboard
- Only SIGNER users are routed to the signer-dashboard
- The routing logic handles edge cases like multi-role users correctly
- Loading states and authentication flows work as expected

### Dependencies

- **Vitest**: Testing framework
- **React Testing Library**: Component testing utilities
- **React Router**: Navigation mocking
- **Custom hooks**: Auth, company selection, and user permissions
