import React from "react";
import Layout from "@/components/Layout";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ArrowRight, Users, Shield, Sparkles, Clock, Building, BookOpen } from "lucide-react";

const About: React.FC = () => {
  return (
    <Layout>
      <div className="container py-16 px-4 md:px-8">
        {/* Hero Section */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              About Legal Concierge
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              We're on a mission to simplify the legal complexities of starting and running a business.
            </p>
          </motion.div>
        </div>

        {/* Our Story */}
        <Card className="mb-16">
          <CardContent className="p-8 md:p-12">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
                <p className="text-gray-600 mb-4">
                  Legal Concierge was founded in 2023 by a team of legal tech experts and startup veterans who experienced firsthand the challenges of navigating the legal requirements of starting a business.
                </p>
                <p className="text-gray-600 mb-4">
                  We recognized that the traditional approach to business formation was outdated, expensive, and unnecessarily complex. Founders were spending thousands of dollars and countless hours on paperwork that could be streamlined with the right technology.
                </p>
                <p className="text-gray-600">
                  Our platform was built to democratize access to high-quality legal services, making it possible for entrepreneurs to form their companies properly without breaking the bank or getting lost in legal jargon.
                </p>
              </div>
              <div className="relative">
                <div className="absolute -inset-4 bg-gradient-to-r from-legal-50 to-blue-50 rounded-2xl transform rotate-1"></div>
                <div className="relative bg-white p-6 rounded-xl border border-gray-100 shadow-sm">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-legal-50 p-4 rounded-lg text-center">
                      <Clock className="h-8 w-8 mx-auto text-legal-600 mb-2" />
                      <p className="font-medium text-gray-900">Founded</p>
                      <p className="text-gray-600">2023</p>
                    </div>
                    <div className="bg-legal-50 p-4 rounded-lg text-center">
                      <Users className="h-8 w-8 mx-auto text-legal-600 mb-2" />
                      <p className="font-medium text-gray-900">Team</p>
                      <p className="text-gray-600">25+ Experts</p>
                    </div>
                    <div className="bg-legal-50 p-4 rounded-lg text-center">
                      <Building className="h-8 w-8 mx-auto text-legal-600 mb-2" />
                      <p className="font-medium text-gray-900">Companies</p>
                      <p className="text-gray-600">1000+ Formed</p>
                    </div>
                    <div className="bg-legal-50 p-4 rounded-lg text-center">
                      <BookOpen className="h-8 w-8 mx-auto text-legal-600 mb-2" />
                      <p className="font-medium text-gray-900">Documents</p>
                      <p className="text-gray-600">10,000+ Generated</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Our Values */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="p-6">
                <div className="h-12 w-12 rounded-full bg-legal-50 flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-legal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Trust & Security</h3>
                <p className="text-gray-600">
                  We prioritize the security of your data and the accuracy of your legal documents. Our platform is built with enterprise-grade security and all documents are reviewed by legal professionals.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="h-12 w-12 rounded-full bg-legal-50 flex items-center justify-center mb-4">
                  <Sparkles className="h-6 w-6 text-legal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Innovation</h3>
                <p className="text-gray-600">
                  We're constantly improving our platform with the latest legal tech innovations to make the incorporation process faster, more accurate, and more user-friendly.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="h-12 w-12 rounded-full bg-legal-50 flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-legal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Accessibility</h3>
                <p className="text-gray-600">
                  We believe quality legal services should be accessible to all entrepreneurs, regardless of budget or legal background. Our platform makes complex legal processes understandable and affordable.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Team Section - Coming Soon */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Team</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Meet the experts behind Legal Concierge
            </p>
          </div>

          <Card>
            <CardContent className="p-8 text-center">
              <div className="max-w-md mx-auto">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Coming Soon</h3>
                <p className="text-gray-600 mb-6">
                  We're currently updating our team page to showcase the talented individuals who make Legal Concierge possible. Check back soon to meet our leadership, legal experts, and tech innovators.
                </p>
                <div className="inline-flex h-1 bg-legal-200 rounded-full w-24 mb-6"></div>
                <p className="text-sm text-gray-500">
                  Want to join our team? We're always looking for talented individuals passionate about legal tech.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Ready to start your business journey?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of entrepreneurs who have successfully formed their companies with Legal Concierge.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button
              size="lg"
              className="group"
              asChild
            >
              <Link to="/login" className="flex items-center">
                Get Started
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link to="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default About;
