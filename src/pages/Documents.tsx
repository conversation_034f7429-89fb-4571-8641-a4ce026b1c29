import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "@/components/Layout";

/**
 * Documents page that redirects to Data Room
 */
const Documents: React.FC = () => {
  const navigate = useNavigate();

  // Redirect to Data Room
  useEffect(() => {
    navigate("/data-room");
  }, [navigate]);

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Redirecting to Data Room...
          </h1>
        </div>
      </div>
    </Layout>
  );
};

export default Documents;
