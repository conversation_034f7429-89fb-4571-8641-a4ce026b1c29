import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/common/Card";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

const QuestionsFormSkeleton: React.FC = () => {
  // Animation variants for staggered loading
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    show: { opacity: 1, y: 0 },
  };

  return (
    <motion.div initial="hidden" animate="show" variants={containerVariants}>
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          {/* Progress indicator skeleton */}
          <motion.div
            variants={itemVariants}
            className="flex justify-between items-center mb-6"
          >
            <Skeleton className="h-2 w-full rounded-full" />
          </motion.div>
          {/* Title skeleton */}
          <motion.div variants={itemVariants}>
            <Skeleton className="h-8 w-64 mb-2" />
          </motion.div>
        </CardHeader>
        <CardContent>
          {/* Form fields skeleton */}
          <div className="space-y-6">
            <motion.div variants={itemVariants} className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
            </motion.div>
            <motion.div variants={itemVariants} className="space-y-2">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-10 w-full" />
            </motion.div>
            <motion.div variants={itemVariants} className="space-y-2">
              <Skeleton className="h-4 w-36" />
              <Skeleton className="h-10 w-full" />
            </motion.div>
            <motion.div variants={itemVariants} className="space-y-2">
              <Skeleton className="h-4 w-44" />
              <div className="grid grid-cols-2 gap-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </motion.div>
            <motion.div variants={itemVariants} className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-24 w-full" />
            </motion.div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <motion.div variants={itemVariants}>
            <Skeleton className="h-10 w-24" />
          </motion.div>
          <motion.div variants={itemVariants}>
            <Skeleton className="h-10 w-32" />
          </motion.div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default QuestionsFormSkeleton;
