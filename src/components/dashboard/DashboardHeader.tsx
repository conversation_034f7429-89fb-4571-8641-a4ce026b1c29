import React from "react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";

interface DashboardHeaderProps {
  username?: string;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  username = "User",
}) => {
  const { companyDetails } = useCompanyDetails();

  // Clean the company name if it exists to avoid duplicative ", Inc."
  const displayCompanyName = companyDetails?.companyName
    ? companyDetails.companyName.endsWith(", Inc.")
      ? companyDetails.companyName
      : `${companyDetails.companyName}, Inc.`
    : "Dashboard";

  return (
    <AnimatedTransition className="mb-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {displayCompanyName}
          </h1>
          <p className="text-gray-600 mt-1">
            Welcome back! Here's an overview of your legal projects.
          </p>
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default DashboardHeader;
