import React, { createContext, useContext } from "react";
import { useAuthState } from "./auth/useAuthState";
import { useAuthMethods } from "./auth/useAuthMethods";
import { AuthContextType } from "./auth/types";

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const {
    isAuthenticated,
    isMfaVerified,
    userEmail,
    user,
    loading,
    setAuthState,
    handleAuthChange,
  } = useAuthState();

  // Get authentication methods
  const {
    login,
    signUp,
    logout,
    verifyMfa,
    forgotPassword,
    resetPassword,
    updateProfile,
  } = useAuthMethods(
    {
      isAuthenticated,
      isMfaVerified,
      userEmail,
      user,
      loading,
    },
    setAuthState
  );

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isMfaVerified,
        userEmail,
        user,
        loading,
        login,
        signUp,
        logout,
        verifyMfa,
        forgotPassword,
        resetPassword,
        updateProfile,
        handleAuthChange,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
