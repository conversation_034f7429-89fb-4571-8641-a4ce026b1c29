import { APIClient } from "@/integrations/legal-concierge/client";
import {
  VestingRequest,
  VestingUnit,
  VestingSchedule as ApiVestingSchedule,
  VestingAcceleration,
} from "@/integrations/legal-concierge/types/Stocks";
import { QuestionnaireFormData } from "./types";

// Map from app vesting schedule to API vesting schedule
const mapVestingSchedule = (schedule: string | null): ApiVestingSchedule => {
  if (schedule === "Standard") return "FOURYEARSONEYEARCLIFF";
  if (schedule === "Monthly") return "FOURYEARSNOCLIFF";
  return "FOURYEARSONEYEARCLIFF"; // Default
};

// Map from app acceleration type to API acceleration type
const mapAcceleration = (acceleration: string | null): VestingAcceleration => {
  if (acceleration === "SingleTrigger") return "SINGLETRIGGER";
  if (acceleration === "DoubleTrigger") return "DOUBLETRIGGER";
  if (acceleration === "None") return "NONE";
  return "NONE"; // Default
};

// Save vesting information to legal-concierge API
export const saveVestingInfo = async (
  _userId: string, // Keeping parameter for API consistency but not using it
  companyId: string,
  formData: QuestionnaireFormData
): Promise<void> => {
  try {
    const api = new APIClient();

    // Convert vesting info to the format expected by the API
    const vestingUnits: VestingUnit[] = formData.vestingInfo.map((vesting) => ({
      officerId: vesting.id, // Assuming this is the officer ID
      isVester: vesting.isVester,
      vestingSchedule: mapVestingSchedule(vesting.vestingSchedule),
      acceleration: mapAcceleration(vesting.acceleration),
    }));
    console.log("gigga 1 vestingUnits", vestingUnits);

    // Create the vesting request
    const vestingRequest: VestingRequest = {
      companyId,
      vestingUnits,
      isVestingConfirmed: formData.isVestingConfirmed || false, // Use the actual confirmation status from form data
    };

    // Update vesting info
    await api.updateVesting(companyId, vestingRequest);
  } catch (error) {
    console.error("Error saving vesting info:", error);
    throw error;
  }
};
