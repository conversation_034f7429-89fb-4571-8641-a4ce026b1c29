import React from "react";
import { Reply } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Comment } from "../types";

interface CommentFooterProps {
  comment: Comment;
  isUserLoggedIn: boolean;
  isReplying: boolean;
  setIsReplying: (value: boolean) => void;
  isDashboard?: boolean;
}

const CommentFooter: React.FC<CommentFooterProps> = ({
  comment,
  isUserLoggedIn,
  isReplying,
  setIsReplying,
  isDashboard = false,
}) => {
  return (
    <>
      <div className="flex justify-between items-center mt-2">
        <div className="text-xs text-gray-500">
          <span>Document: </span>
          <span className="font-medium">
            {comment.documentId.startsWith("reply-to-")
              ? "Reply"
              : comment.documentId}
          </span>
        </div>

        {comment.documentId.startsWith("1") ||
        comment.documentId.startsWith("2") ? (
          <Link
            to="/sign-documents"
            className="text-xs text-blue-500 hover:underline"
          >
            View Document
          </Link>
        ) : null}
      </div>

      {/* {isUserLoggedIn && !isReplying && !comment.resolved && !isDashboard && (
        <div className="mt-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 text-xs"
            onClick={() => setIsReplying(true)}
          >
            <Reply className="h-3 w-3 mr-1" />
            Reply
          </Button>
        </div>
      )} */}
    </>
  );
};

export default CommentFooter;
