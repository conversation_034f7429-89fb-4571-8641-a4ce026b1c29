import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { StakeholderData } from "../types";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import Button from "@/components/common/Button";
import { Trash2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StakeholderFormProps {
  stakeholder: StakeholderData;
  index: number;
  totalShares: number;
  onRemove: (id: string) => void;
  onUpdate: (id: string, field: keyof StakeholderData, value: any) => void;
  onToggleOfficerTitle: (id: string, title: string) => void;
}

const StakeholderForm: React.FC<StakeholderFormProps> = ({
  stakeholder,
  index,
  totalShares,
  onRemove,
  onUpdate,
  onToggleOfficerTitle,
}) => {
  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <div className="flex justify-between items-center mb-3">
        <h4 className="font-medium">
          {stakeholder.name ? stakeholder.name : `Person #${index + 1}`}
        </h4>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemove(stakeholder.id)}
          icon={<Trash2 size={16} />}
          className="text-red-500 hover:text-red-700 hover:bg-red-50"
        >
          Remove
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor={`name-${stakeholder.id}`}>Name</Label>
          <Input
            id={`name-${stakeholder.id}`}
            value={stakeholder.name}
            onChange={(e) => onUpdate(stakeholder.id, "name", e.target.value)}
            placeholder="Full name"
          />
        </div>

        <div className="space-y-2">
          <Label>Director</Label>
          <RadioGroup
            value={stakeholder.isDirector ? "yes" : "no"}
            onValueChange={(value) =>
              onUpdate(stakeholder.id, "isDirector", value === "yes")
            }
            className="flex space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="yes"
                id={`director-yes-${stakeholder.id}`}
              />
              <Label
                htmlFor={`director-yes-${stakeholder.id}`}
                className="cursor-pointer"
              >
                Yes
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id={`director-no-${stakeholder.id}`} />
              <Label
                htmlFor={`director-no-${stakeholder.id}`}
                className="cursor-pointer"
              >
                No
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-2 md:col-span-2">
          <Label>Officer Title</Label>
          <div className="flex flex-wrap gap-4">
            {(
              [
                "CEO",
                "President",
                "Treasurer",
                "Secretary",
                "Not Applicable",
              ] as string[]
            ).map((title) => (
              <div key={title} className="flex items-center space-x-2">
                <Checkbox
                  id={`title-${title}-${stakeholder.id}`}
                  checked={stakeholder.officerTitles.includes(title)}
                  onCheckedChange={() =>
                    onToggleOfficerTitle(stakeholder.id, title)
                  }
                />
                <Label
                  htmlFor={`title-${title}-${stakeholder.id}`}
                  className="cursor-pointer"
                >
                  {title}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor={`email-${stakeholder.id}`}>Email Address</Label>
          <Input
            id={`email-${stakeholder.id}`}
            type="email"
            value={stakeholder.emailAddress}
            onChange={(e) =>
              onUpdate(stakeholder.id, "emailAddress", e.target.value)
            }
            placeholder="Email address"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor={`address-${stakeholder.id}`}>Contact Address</Label>
          <Input
            id={`address-${stakeholder.id}`}
            value={stakeholder.address}
            onChange={(e) =>
              onUpdate(stakeholder.id, "address", e.target.value)
            }
            placeholder="Address"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor={`percentage-${stakeholder.id}`}>
            Stock Ownership %
          </Label>
          <div className="relative">
            <Input
              id={`percentage-${stakeholder.id}`}
              type="number"
              value={stakeholder.stockOwnershipPercentage}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                if (!isNaN(newValue) && newValue >= 0 && newValue <= 100) {
                  onUpdate(
                    stakeholder.id,
                    "stockOwnershipPercentage",
                    newValue
                  );
                }
              }}
              step="0.01"
              min="0"
              max="100"
              className="pr-7"
            />
            <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
              %
            </span>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor={`shares-${stakeholder.id}`}>Amount of Shares</Label>
          <Input
            id={`shares-${stakeholder.id}`}
            type="text"
            value={stakeholder.sharesAmount.toLocaleString()}
            readOnly
            className="bg-gray-100"
          />
          <p className="text-xs text-gray-500">
            Calculated as {stakeholder.stockOwnershipPercentage}% of total
            issued shares
          </p>
        </div>

        <div className="space-y-2">
          <Label>Vesting Schedule</Label>
          <RadioGroup
            value={stakeholder.isVester ? "yes" : "no"}
            onValueChange={(value) =>
              onUpdate(stakeholder.id, "isVester", value === "yes")
            }
            className="flex space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="yes"
                id={`vesting-yes-${stakeholder.id}`}
              />
              <Label
                htmlFor={`vesting-yes-${stakeholder.id}`}
                className="cursor-pointer"
              >
                Yes
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id={`vesting-no-${stakeholder.id}`} />
              <Label
                htmlFor={`vesting-no-${stakeholder.id}`}
                className="cursor-pointer"
              >
                No
              </Label>
            </div>
          </RadioGroup>
        </div>

        {stakeholder.isVester && (
          <>
            <div className="space-y-2">
              <Label htmlFor={`vesting-schedule-${stakeholder.id}`}>
                Vesting Type
              </Label>
              <Select
                value={stakeholder.vestingSchedule}
                onValueChange={(value) =>
                  onUpdate(stakeholder.id, "vestingSchedule", value)
                }
              >
                <SelectTrigger id={`vesting-schedule-${stakeholder.id}`}>
                  <SelectValue placeholder="Select vesting schedule" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="4-year-1-year-cliff">
                    4 years with 1-year cliff
                  </SelectItem>
                  <SelectItem value="4-year-no-cliff">
                    4 years with no cliff
                  </SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor={`acceleration-${stakeholder.id}`}>
                Acceleration
              </Label>
              <Select
                value={stakeholder.acceleration}
                onValueChange={(value) =>
                  onUpdate(stakeholder.id, "acceleration", value)
                }
              >
                <SelectTrigger id={`acceleration-${stakeholder.id}`}>
                  <SelectValue placeholder="Select acceleration terms" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="single-trigger">Single Trigger</SelectItem>
                  <SelectItem value="double-trigger">Double Trigger</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default StakeholderForm;
