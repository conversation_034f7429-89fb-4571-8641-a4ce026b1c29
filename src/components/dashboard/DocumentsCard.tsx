import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import { FileText, FolderOpen } from "lucide-react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useCompanyDetails } from "../questions/hooks/useCompanyDetails";

const DocumentsCard: React.FC = () => {
  const navigate = useNavigate();
  const { companyDetails, loading: companyDetailsLoading } =
    useCompanyDetails();
  const isRegisterExisting =
    companyDetails?.registerMode === "register-existing";
  const isGettingStartedFlow =
    companyDetails?.registerMode === "getting-started";
  return (
    <AnimatedTransition delay={0.2}>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Documents</CardTitle>
          <CardDescription>Access your legal documents</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <div className="bg-gray-50 rounded-full h-16 w-16 mx-auto flex items-center justify-center">
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="mt-4 text-gray-900 font-medium">Document Center</h3>
            <p className="text-gray-500 text-sm mt-1 mb-4">
              Access and manage all your company documents
            </p>
            {!companyDetailsLoading && (
              <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 justify-center">
                {/* View Documents Button */}
                {/* Getting-Started Flow: Show only when form is completed but signature process has NOT been initiated */}
                {isGettingStartedFlow &&
                  companyDetails.isFormConfirmed &&
                  !companyDetails.isSignatureConfirmed && (
                    <Button
                      onClick={() => navigate("/review-documents")}
                      className="w-full sm:w-auto"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      View Documents
                    </Button>
                  )}

                {/* Register-Existing Flow: Show when signature is complete */}
                {isRegisterExisting && companyDetails.isSignatureComplete && (
                  <Button
                    onClick={() => navigate("/review-documents")}
                    className="w-full sm:w-auto"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    View Documents
                  </Button>
                )}

                {/* Data Room Button - Different visibility logic for each flow */}
                {/* Register-Existing Flow: Show when form is confirmed */}
                {isRegisterExisting && companyDetails.isFormConfirmed && (
                  <Button
                    onClick={() => navigate("/data-room")}
                    variant="outline"
                    className="w-full sm:w-auto"
                  >
                    <FolderOpen className="h-4 w-4 mr-2" />
                    Data Room
                  </Button>
                )}

                {/* Getting-Started Flow: Show only after review AND signature are completed */}
                {isGettingStartedFlow &&
                  companyDetails.isReviewConfirmed &&
                  companyDetails.isSignatureComplete && (
                    <Button
                      onClick={() => navigate("/data-room")}
                      variant="outline"
                      className="w-full sm:w-auto"
                    >
                      <FolderOpen className="h-4 w-4 mr-2" />
                      Data Room
                    </Button>
                  )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </AnimatedTransition>
  );
};

export default DocumentsCard;
