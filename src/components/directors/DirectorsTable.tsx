import React from "react";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Director } from "./types";
import DirectorRow from "./DirectorRow";

interface DirectorsTableProps {
  directors: Director[];
  editingId: string | null;
  onEditClick: (director: Director) => void;
  onSaveClick: (id: string) => void;
  onDateChange: (id: string, date: Date | undefined) => void;
  onResignationDateChange: (id: string, date: Date | undefined) => void;
  onResignedChange: (id: string) => void;
  onStockholderApprovalChange?: (id: string) => void;
}

const DirectorsTable: React.FC<DirectorsTableProps> = ({
  directors,
  editingId,
  onEditClick,
  onSaveClick,
  onDateChange,
  onResignationDateChange,
  onResignedChange,
  onStockholderApprovalChange,
}) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Appointment Date</TableHead>
          <TableHead>Resigned</TableHead>
          <TableHead>Resignation Date</TableHead>
          <TableHead>Stockholder Approval</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {directors.map((director) => (
          <DirectorRow
            key={director.id}
            director={director}
            editingId={editingId}
            onEditClick={onEditClick}
            onSaveClick={onSaveClick}
            onDateChange={onDateChange}
            onResignationDateChange={onResignationDateChange}
            onResignedChange={onResignedChange}
            onStockholderApprovalChange={onStockholderApprovalChange}
          />
        ))}
      </TableBody>
    </Table>
  );
};

export default DirectorsTable;
