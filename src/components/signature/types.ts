export interface SignatureData {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  signatureImageData: string;
  timestamp: Date;
  ipAddress?: string;
}

export interface SignatureRequest {
  id: string;
  documentId: string;
  documentName: string;
  requestedBy: string;
  requestedAt: Date;
  expiresAt?: Date;
  remindersSent: number;
  lastReminderSent?: Date;
  status: "pending" | "completed" | "expired";
}

export interface DocumentSignature {
  documentId: string;
  documentName: string;
  signers: {
    userId: string;
    name: string;
    email: string;
    role: string;
    status: "pending" | "signed" | "declined";
    signedAt?: Date;
  }[];
  status: "pending" | "partial" | "completed";
  completedAt?: Date;
  documentHash?: string;
}
