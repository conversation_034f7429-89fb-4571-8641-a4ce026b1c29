import React from "react";

interface ShareSummaryProps {
  authorizedShares: number;
  issuedShares: number;
  onEditShares: () => void;
}

const ShareSummary: React.FC<ShareSummaryProps> = ({
  authorizedShares,
  issuedShares,
  onEditShares,
}) => {
  return (
    <div className="border-t pt-6 mt-6">
      <div className="grid grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg">
        <div>
          <p className="text-sm font-medium">Authorized Shares:</p>
          <p className="text-lg">{authorizedShares.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Shares Issued:</p>
          <p className="text-lg">{issuedShares.toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
};

export default ShareSummary;
