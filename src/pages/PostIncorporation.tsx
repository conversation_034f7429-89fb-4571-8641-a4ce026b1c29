import React from "react";
import Layout from "@/components/Layout";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import TaskList from "@/components/post-incorporation/TaskList";
import ForeignQualificationContent from "@/components/post-incorporation/ForeignQualificationContent";
import { usePostIncorporation } from "@/components/post-incorporation/hooks/usePostIncorporation";
import { Task } from "@/components/post-incorporation/types";
import ProgressBar from "@/components/post-incorporation/ProgressBar";

const PostIncorporation: React.FC = () => {
  const {
    tasks,
    selectedStates,
    stateLinks,
    handleTaskComplete,
    handleMarkAllComplete,
    handleAddState,
    handleRemoveState,
    allTasksComplete,
    isLoading,
    companyData,
  } = usePostIncorporation();
  console.log({ tasks });
  // Calculate progress metrics
  const totalTasks = Object.keys(tasks).length;
  const completedTasks = Object.values(tasks).filter(
    (task) => task.isComplete
  ).length;

  // Show loading state while company data is being fetched
  if (!companyData && isLoading) {
    return (
      <Layout>
        <div className="container py-8 px-4 md:px-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-gray-600">
                Loading post-incorporation tasks...
              </p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Render dialog content for the foreign qualification task
  const renderDialogContent = (task: Task) => {
    if (task.id === "foreignQualification") {
      return (
        <ForeignQualificationContent
          task={task}
          selectedStates={selectedStates}
          stateLinks={stateLinks}
          onAddState={handleAddState}
          onRemoveState={handleRemoveState}
        />
      );
    }
    return null;
  };

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <AnimatedTransition className="mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Post-Incorporation Checklist
            </h1>
            <p className="text-gray-600 mt-1">
              Here are the next steps to take after forming your company.
            </p>
          </div>
        </AnimatedTransition>

        <AnimatedTransition className="mb-6">
          <ProgressBar
            completedTasks={completedTasks}
            totalTasks={totalTasks}
          />
        </AnimatedTransition>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Essential Next Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <TaskList
              tasks={tasks}
              onTaskComplete={handleTaskComplete}
              onMarkAllComplete={handleMarkAllComplete}
              renderDialogContent={renderDialogContent}
              allTasksComplete={allTasksComplete}
              isLoading={isLoading}
            />
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default PostIncorporation;
