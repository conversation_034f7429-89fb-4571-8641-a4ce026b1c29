import { useState, useEffect, useCallback } from "react";
import { QuestionnaireFormData, FormStep } from "../types";
import { validateStep } from "../utils/formValidation";
import { useFormDialogs } from "./useFormDialogs";
import { useFormNavigation } from "./useFormNavigation";
import { useFormProgress } from "./useFormProgress";
import { useVestingSync } from "./useVestingSync";
import { useIncorporationDateSync } from "./useIncorporationDateSync";
import { useFormSubmit } from "./useFormSubmit";
import { useAuth } from "@/contexts/AuthContext";
import {
  useUpdateAuthorizedShares,
  useUpdateCompanyName,
  useUpdateCompanyAddress,
  useConfirmCompanyForm,
} from "@/integrations/legal-concierge/hooks/useCompanyInfo";
import {
  useUpdateTechnology,
  useUpdateOfficerIP,
  useAddOfficer,
  useUpdateOfficer,
} from "@/integrations/legal-concierge/hooks";
import {
  useUpdateStockOptionPlan,
  useUpdateVesting,
} from "@/integrations/legal-concierge/hooks/useStockAndVesting";
import { useUpdateRegisteredAgent } from "@/integrations/legal-concierge/hooks/useRegisteredAgent";
import { saveAuthorizedSharesData } from "../steps/AuthorizedSharesStep";
import { saveStockPlanData } from "../steps/StockPlanStep";
import { saveCompanyNameData } from "../steps/CompanyNameStep";
import { saveCompanyAddressData } from "../steps/CompanyAddressStep";
import { saveVestingData } from "../steps/VestingStep";
import { saveRegisteredAgentData } from "../steps/RegisteredAgentStep";
import { saveReviewConfirmationData } from "../steps/review/ReviewStep";
import { useFormData } from "./formStorage/getFormData";
import { saveDirectorsOfficersData } from "../steps/DirectorsOfficersSharesStep";
import { toast } from "sonner";

export const useQuestionsForm = (isEditMode = false) => {
  const { user } = useAuth();

  // Use the new hook to get form data with react-query caching
  const {
    formData: serverFormData,
    isLoading: isCompanyLoading,
    currentStep: serverCurrentStep,
  } = useFormData();
  // Initialize form state with server data or defaults
  const [currentStep, setCurrentStep] = useState<FormStep>(serverCurrentStep);
  const [formData, setFormData] =
    useState<QuestionnaireFormData>(serverFormData);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Update form data when server data changes, but preserve current form state during saves
  useEffect(() => {
    console.log("serverFormData", serverFormData);
    if (serverFormData) {
      // If we're currently saving, don't reset the form data to prevent field values from disappearing
      if (!isSaving) {
        setFormData(serverFormData);
      } else {
        // During save operations, only update fields that aren't currently being edited
        // This preserves user input while allowing server updates for other fields
        setFormData((prevFormData) => ({
          ...serverFormData,
          // Preserve current form values for fields that might be actively edited
          companyName: prevFormData.companyName || serverFormData.companyName,
          companyAddressStructured:
            prevFormData.companyAddressStructured ||
            serverFormData.companyAddressStructured,
          incorporationDate:
            prevFormData.incorporationDate || serverFormData.incorporationDate,
          incorporatingToday:
            prevFormData.incorporatingToday !== null
              ? prevFormData.incorporatingToday
              : serverFormData.incorporatingToday,
          authorizedShares:
            prevFormData.authorizedShares || serverFormData.authorizedShares,
          directors:
            prevFormData.directors.length > 0
              ? prevFormData.directors
              : serverFormData.directors,
          stockOptionPlanPercentage:
            prevFormData.stockOptionPlanPercentage ||
            serverFormData.stockOptionPlanPercentage,
          includeStockOptionPlan:
            prevFormData.includeStockOptionPlan !== null
              ? prevFormData.includeStockOptionPlan
              : serverFormData.includeStockOptionPlan,
        }));
      }

      // Only set the current step from server data on initial load
      // This prevents the step from being reset when data refetches after saves
      if (isInitialLoad && !isSaving) {
        setCurrentStep(serverCurrentStep);
        setIsInitialLoad(false);
      }
    }
  }, [serverFormData, serverCurrentStep, isInitialLoad, isSaving]);

  // Import dialog handling
  const {
    showNameDialog,
    showIncorporationDialog,
    showSharesDialog,
    setShowNameDialog,
    setShowIncorporationDialog,
    setShowSharesDialog,
    handleNameDialogConfirm,
    handleIncorporationDialogConfirm,
  } = useFormDialogs();

  // Import navigation handling
  const { handleBack, handleNext: navigateNext } = useFormNavigation(
    currentStep,
    setCurrentStep
  );

  // Track form progress
  useFormProgress(currentStep);

  // Manage vesting sync with directors
  useVestingSync(formData, setFormData);

  // Manage incorporation date sync
  const updateFormData = useCallback(
    (field: keyof QuestionnaireFormData, value: unknown) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    },
    []
  );

  useIncorporationDateSync(formData, updateFormData);

  // Handle form submission
  const { handleSubmit, loading } = useFormSubmit(formData, isEditMode);

  // Hooks for updating different data types
  const updateAuthorizedShares = useUpdateAuthorizedShares();
  const updateStockOptionPlan = useUpdateStockOptionPlan();
  const updateCompanyName = useUpdateCompanyName();
  const updateCompanyAddress = useUpdateCompanyAddress();
  const updateVesting = useUpdateVesting();
  const updateRegisteredAgent = useUpdateRegisteredAgent();
  const confirmCompanyForm = useConfirmCompanyForm();
  const updateTechnology = useUpdateTechnology();
  const updateOfficerIP = useUpdateOfficerIP();
  const addOfficer = useAddOfficer();
  const updateOfficer = useUpdateOfficer();

  // Save function that validates, saves data for specific steps, and navigates
  const handleNext = async (): Promise<void> => {
    try {
      console.log(`handleNext called for step: ${currentStep}`);
      setIsSaving(true);

      // Skip validation for some fields when in edit mode
      if (
        isEditMode &&
        currentStep === "company-name" &&
        formData.nameAvailabilityChecked
      ) {
        console.log("Edit mode, skipping validation for company-name step");
        navigateNext();
        return;
      }

      // Validation logic based on current step
      console.log(`Validating step: ${currentStep}`);
      const isValid = validateStep(
        currentStep,
        formData,
        () => setShowNameDialog(true),
        () => setShowIncorporationDialog(true),
        () => setShowSharesDialog(true)
      );

      if (!isValid) {
        console.log(`Validation failed for step: ${currentStep}`);
        return;
      }

      console.log(`Validation passed for step: ${currentStep}`);

      // Save data for specific steps before navigating
      if (currentStep === "company-name" && user?.companyId) {
        console.log("Saving company name data...");
        const saveSuccess = await saveCompanyNameData(
          formData,
          user.companyId,
          updateCompanyName
        );

        if (!saveSuccess) {
          console.log("Failed to save company name data");
          // Still continue with navigation - user can try again later
        } else {
          console.log("Company name data saved successfully");
        }
      }

      if (currentStep === "company-address" && user?.companyId) {
        console.log("Saving company address data...");
        const saveSuccess = await saveCompanyAddressData(
          formData,
          user.companyId,
          updateCompanyAddress
        );

        if (!saveSuccess) {
          console.log("Failed to save company address data");
          // Still continue with navigation - user can try again later
        } else {
          console.log("Company address data saved successfully");
        }
      }

      if (currentStep === "authorized-shares" && user?.companyId) {
        console.log("Saving authorized shares data...");
        const saveSuccess = await saveAuthorizedSharesData(
          formData,
          user.companyId,
          updateAuthorizedShares
        );

        if (!saveSuccess) {
          console.log("Failed to save authorized shares data");
          // Still continue with navigation - user can try again later
        } else {
          console.log("Authorized shares data saved successfully");
        }
      }

      if (currentStep === "stock-plan" && user?.companyId) {
        console.log("Saving stock plan data...");
        const saveSuccess = await saveStockPlanData(
          formData,
          user.companyId,
          updateStockOptionPlan
        );

        if (!saveSuccess) {
          console.log("Failed to save stock plan data");
          // Don't continue navigation if validation failed (total percentage != 100%)
          return;
        } else {
          console.log("Stock plan data saved successfully");
        }
      }

      if (currentStep === "vesting" && user?.companyId) {
        console.log("Saving vesting data...");
        const saveSuccess = await saveVestingData(
          formData,
          user.companyId,
          updateVesting
        );

        if (!saveSuccess) {
          console.log("Failed to save vesting data");
          // Still continue with navigation - user can try again later
        } else {
          console.log("Vesting data saved successfully");
        }
      }

      if (currentStep === "technology" && user?.companyId) {
        console.log("Saving technology data...");
        try {
          // Save company tech description only
          await updateTechnology.mutateAsync({
            id: user.companyId,
            technologyDescription: formData.companyTech,
          });

          console.log("Technology description saved successfully");
          toast.info("Technology description saved successfully");
        } catch (error) {
          console.error("Error saving technology description:", error);
          toast.error("Failed to save technology description");
          return; // Don't continue navigation if save fails
        }
      }

      if (currentStep === "registered-agent" && user?.companyId) {
        console.log("Saving registered agent data...");
        const saveSuccess = await saveRegisteredAgentData(
          formData,
          user.companyId,
          updateRegisteredAgent
        );

        if (!saveSuccess) {
          console.log("Failed to save registered agent data");
          // Still continue with navigation - user can try again later
        } else {
          console.log("Registered agent data saved successfully");
        }
      }

      if (currentStep === "review" && user?.companyId) {
        console.log("Saving review confirmation data...");
        const saveSuccess = await saveReviewConfirmationData(
          formData,
          user.companyId,
          confirmCompanyForm
        );

        if (!saveSuccess) {
          console.log("Failed to save review confirmation data");
          // Don't continue navigation if confirmation is required
          return;
        } else {
          console.log("Review confirmation data saved successfully");
        }
      }

      if (currentStep === "directors-and-officers" && user?.companyId) {
        console.log("Saving directors and officers data...");
        const saveSuccess = await saveDirectorsOfficersData(
          formData,
          user.companyId,
          addOfficer,
          updateOfficer
        );

        if (!saveSuccess) {
          console.log("Failed to save directors and officers data");
          // Still continue with navigation - user can try again later
        } else {
          console.log("Directors and officers data saved successfully");
        }
      }

      // Navigate to the next step
      console.log("Navigating to next step...");
      navigateNext();
      console.log("Navigation complete");
    } catch (error) {
      console.error("Error in handleNext:", error);
      // Continue with navigation even if save fails
      navigateNext();
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  // Wrap dialog handlers with necessary context
  const wrappedNameDialogConfirm = () => {
    handleNameDialogConfirm(
      (checked) => updateFormData("nameAvailabilityChecked", checked),
      formData.incorporatingToday,
      navigateNext,
      () => setShowIncorporationDialog(true)
    );
  };

  const wrappedIncorporationDialogConfirm = async (): Promise<void> => {
    handleIncorporationDialogConfirm(async () => {
      // Continue with saving company name data
      if (user?.companyId) {
        console.log("Saving company name data after incorporation dialog...");
        const saveSuccess = await saveCompanyNameData(
          formData,
          user.companyId,
          updateCompanyName
        );

        if (!saveSuccess) {
          console.log("Failed to save company name data");
          // Still continue with navigation - user can try again later
        } else {
          console.log("Company name data saved successfully");
        }
      }
      // Navigate to next step
      navigateNext();
    });
  };

  return {
    currentStep,
    formData,
    loading,
    isLoading: isCompanyLoading,
    showNameDialog,
    showIncorporationDialog,
    showSharesDialog,
    setShowNameDialog,
    setShowIncorporationDialog,
    setShowSharesDialog,
    updateFormData,
    handleSubmit,
    handleBack,
    handleNext,
    handleNameDialogConfirm: wrappedNameDialogConfirm,
    handleIncorporationDialogConfirm: wrappedIncorporationDialogConfirm,
  };
};
