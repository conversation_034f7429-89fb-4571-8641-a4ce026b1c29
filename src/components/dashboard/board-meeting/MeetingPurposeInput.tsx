import React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface MeetingPurposeInputProps {
  meetingPurpose: string;
  setMeetingPurpose: (purpose: string) => void;
  formErrors: {
    purpose?: string;
  };
}

const MeetingPurposeInput: React.FC<MeetingPurposeInputProps> = ({
  meetingPurpose,
  setMeetingPurpose,
  formErrors,
}) => {
  return (
    <div className="space-y-1.5">
      <Label htmlFor="meeting-purpose" className="text-sm font-medium">
        Purpose of Meeting
      </Label>
      <Textarea
        id="meeting-purpose"
        placeholder="Explain the purpose of this board meeting..."
        className={cn(
          "min-h-[100px] text-sm",
          formErrors.purpose && "border-red-500"
        )}
        value={meetingPurpose}
        onChange={(e) => setMeetingPurpose(e.target.value)}
      />
      {formErrors.purpose && (
        <p className="text-red-500 text-xs">{formErrors.purpose}</p>
      )}
    </div>
  );
};

export default MeetingPurposeInput;
