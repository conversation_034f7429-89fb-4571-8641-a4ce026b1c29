import React, { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { File, Users, ShieldCheck } from "lucide-react";
import FeatureCard from "./FeatureCard";

const FeaturesSection: React.FC = () => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const opacity = useTransform(scrollYProgress, [0, 0.5], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 0.5], [50, 0]);

  const features = [
    {
      icon: File,
      title: "Document Generation",
      description:
        "Automatically generate all required legal documents based on your inputs, with custom templates for different entity types.",
      features: ["Certificates of Incorporation", "Bylaws", "Stock Agreements"],
    },
    {
      icon: Users,
      title: "Collaboration & E-Signatures",
      description:
        "Invite co-founders to review documents, add comments, and sign electronically—all within the platform.",
      features: [
        "Real-time collaboration",
        "Electronic signatures",
        "Signature tracking",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Security & Compliance",
      description:
        "Bank-level security for your sensitive data with audit trails and verification for all activities.",
      features: [
        "Multi-factor authentication",
        "Document encryption",
        "Compliance tracking",
      ],
    },
  ];

  return (
    <section ref={ref} className="py-24 bg-white">
      <div className="container px-4 md:px-8">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <motion.div style={{ opacity, y }}>
            <div className="inline-block bg-legal-50 px-3 py-1 rounded-full text-legal-700 text-sm font-medium mb-6">
              Powerful Features
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Everything you need for company formation
            </h2>
            <p className="text-xl text-gray-600">
              Our platform streamlines the entire incorporation process, from
              filling out forms to signing documents.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              features={feature.features}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
