import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@/test/utils";
import userEvent from "@testing-library/user-event";
import CompanyAddressStep from "../CompanyAddressStep";
import { mockLegacyFormData } from "@/test/mocks/companyData";

describe("CompanyAddressStep", () => {
  const mockUpdateFormData = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render all address fields", () => {
    render(
      <CompanyAddressStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(screen.getByLabelText(/^Street Address$/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Street Address 2/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/City/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/State/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/ZIP Code/i)).toBeInTheDocument();
  });

  it("should display current address values", () => {
    render(
      <CompanyAddressStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(screen.getByDisplayValue("123 Main St")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Suite 100")).toBeInTheDocument();
    expect(screen.getByDisplayValue("San Francisco")).toBeInTheDocument();
    expect(screen.getByDisplayValue("94105")).toBeInTheDocument();
  });

  it("should call updateFormData when street address changes", async () => {
    const user = userEvent.setup();

    render(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "",
            street2: "",
            city: "",
            state: "",
            zipCode: "",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const streetInput = screen.getByLabelText(/^Street Address$/i);
    await user.type(streetInput, "456");

    // Check that updateFormData was called
    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalled();
    });
  });

  it("should call updateFormData when city changes", async () => {
    const user = userEvent.setup();

    render(
      <CompanyAddressStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    const cityInput = screen.getByLabelText(/City/i);
    await user.type(cityInput, "L");

    // Check that updateFormData was called
    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalled();
    });
  });

  it.skip("should update state when user selects from dropdown", async () => {
    const user = userEvent.setup();

    render(
      <CompanyAddressStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    // Find and click the state select trigger
    const stateSelect = screen.getByRole("combobox");
    await user.click(stateSelect);

    // Wait for dropdown to open and select New York
    await waitFor(
      () => {
        const nyOption = screen.getByText("New York");
        expect(nyOption).toBeInTheDocument();
        return nyOption;
      },
      { timeout: 3000 }
    );

    const nyOption = screen.getByText("New York");
    await user.click(nyOption);

    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenLastCalledWith(
        "companyAddressStructured",
        {
          street1: "123 Main St",
          street2: "Suite 100",
          city: "San Francisco",
          state: "NY",
          zipCode: "94105",
        }
      );
    });
  });

  it("should call updateFormData when ZIP code changes", async () => {
    const user = userEvent.setup();

    render(
      <CompanyAddressStep
        formData={mockLegacyFormData}
        updateFormData={mockUpdateFormData}
      />
    );

    const zipInput = screen.getByLabelText(/ZIP Code/i);
    await user.type(zipInput, "9");

    // Check that updateFormData was called
    await waitFor(() => {
      expect(mockUpdateFormData).toHaveBeenCalled();
    });
  });

  it("should show Delaware incorporation notice for non-Delaware states", () => {
    render(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            ...mockLegacyFormData.companyAddressStructured!,
            state: "CA",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.getByText(/We will incorporate your company in Delaware/i)
    ).toBeInTheDocument();
  });

  it("should not show Delaware notice for Delaware addresses", () => {
    render(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            ...mockLegacyFormData.companyAddressStructured!,
            state: "DE",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    expect(
      screen.queryByText(/We will incorporate your company in Delaware/i)
    ).not.toBeInTheDocument();
  });

  it("should maintain local address state when formData changes during editing", async () => {
    const user = userEvent.setup();
    const { rerender } = render(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "Initial Street",
            street2: "",
            city: "Initial City",
            state: "CA",
            zipCode: "12345",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const streetInput = screen.getByLabelText(/^Street Address$/i);
    const cityInput = screen.getByLabelText(/City/i);

    // Start editing
    await user.clear(streetInput);
    await user.type(streetInput, "User Typed Street");
    await user.clear(cityInput);
    await user.type(cityInput, "User Typed City");

    // Verify the user's input is displayed
    expect(screen.getByDisplayValue("User Typed Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("User Typed City")).toBeInTheDocument();

    // Simulate formData change from parent (like during a save operation)
    rerender(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "Server Updated Street",
            street2: "",
            city: "Server Updated City",
            state: "CA",
            zipCode: "12345",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    // The user's input should still be visible (not overridden by server data)
    expect(screen.getByDisplayValue("User Typed Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("User Typed City")).toBeInTheDocument();
  });

  it("should update local state from formData when user is not editing", async () => {
    const { rerender } = render(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "Initial Street",
            street2: "",
            city: "Initial City",
            state: "CA",
            zipCode: "12345",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    // Verify initial values
    expect(screen.getByDisplayValue("Initial Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Initial City")).toBeInTheDocument();

    // Update formData when user is not editing
    rerender(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "Updated Street",
            street2: "",
            city: "Updated City",
            state: "CA",
            zipCode: "12345",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    // Should show the updated values from formData
    expect(screen.getByDisplayValue("Updated Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Updated City")).toBeInTheDocument();
  });

  it("should preserve field values during rapid changes", async () => {
    const user = userEvent.setup();

    render(
      <CompanyAddressStep
        formData={{
          ...mockLegacyFormData,
          companyAddressStructured: {
            street1: "",
            street2: "",
            city: "",
            state: "",
            zipCode: "",
          },
        }}
        updateFormData={mockUpdateFormData}
      />
    );

    const streetInput = screen.getByLabelText(/^Street Address$/i);
    const cityInput = screen.getByLabelText(/City/i);
    const zipInput = screen.getByLabelText(/ZIP Code/i);

    // Rapidly type in multiple fields
    await user.type(streetInput, "123 Test Street");
    await user.type(cityInput, "Test City");
    await user.type(zipInput, "12345");

    // All values should be preserved
    expect(screen.getByDisplayValue("123 Test Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Test City")).toBeInTheDocument();
    expect(screen.getByDisplayValue("12345")).toBeInTheDocument();
  });
});
