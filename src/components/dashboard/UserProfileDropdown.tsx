import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/AuthContext";
import { User, LogOut, Workflow } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { CompanySwitcherDialog } from "../common/CompanySwitcherDialog";
import {
  useCompanies,
  useSelectCompany,
} from "@/integrations/legal-concierge/hooks/useCompanies";

const UserProfileDropdown: React.FC = () => {
  const { logout, userEmail, user, handleAuthChange } = useAuth();
  const navigate = useNavigate();
  const [showCompanyDialog, setShowCompanyDialog] = useState(false);
  const { mutate: selectCompany } = useSelectCompany();
  const { data: companies, isLoading: isCompaniesLoading } = useCompanies(true);
  // Use email from auth context, or fallback to placeholder
  const email = userEmail || "<EMAIL>";

  // Extract initials from email for avatar
  const initials = email.split("@")[0].slice(0, 2).toUpperCase();

  const handleProfileSettings = () => {
    // Navigate to profile settings page
    navigate("/profile");
  };

  const handleSelect = (id: string) => {
    selectCompany(id, {
      onSuccess: () => {
        setShowCompanyDialog(false);
        handleAuthChange(true);
      },
    });
  };

  const handleLogout = () => {
    localStorage.removeItem("companyDetailsCompleted");
    localStorage.removeItem("companyQuestionnaire");
    localStorage.removeItem("completedCompanyDetails");
    localStorage.removeItem("questionnaireProgress");
    localStorage.removeItem("postIncorporationTasks");
    localStorage.removeItem("foreignQualificationStates");
    localStorage.removeItem("capTableData");
    localStorage.removeItem("registerMode");
    logout();
  };

  return (
    <>
      <CompanySwitcherDialog
        open={showCompanyDialog}
        companies={companies ?? []}
        onSelect={(id) => handleSelect(id)}
        onClose={() => setShowCompanyDialog(false)}
      />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="relative h-10 rounded-full flex items-center gap-2"
          >
            <Avatar className="h-8 w-8">
              {user?.avatarUrl ? (
                <AvatarImage src={user.avatarUrl} alt={email} />
              ) : (
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {initials}
                </AvatarFallback>
              )}
            </Avatar>
            <span className="text-sm font-medium hidden md:block">{email}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56 z-50 bg-white">
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{email}</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user.fullName || "Logged in user"}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleProfileSettings}>
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowCompanyDialog(true)}>
            <Workflow className="mr-2 h-4 w-4" />
            <span>Choose workspace</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Logout</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};

export default UserProfileDropdown;
