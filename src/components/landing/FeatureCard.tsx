import React from "react";
import { Card, CardContent } from "@/components/common/Card";
import { CheckCircle } from "lucide-react";
import { LucideIcon } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  features: string[];
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  icon: Icon,
  title,
  description,
  features,
}) => {
  return (
    <Card variant="glass" hover className="relative overflow-hidden">
      <div className="absolute top-0 right-0 h-20 w-20 bg-legal-100 rounded-bl-full opacity-50"></div>
      <CardContent className="p-8">
        <div className="h-12 w-12 bg-legal-100 text-legal-600 rounded-lg flex items-center justify-center mb-6">
          <Icon size={24} />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">{title}</h3>
        <p className="text-gray-600">{description}</p>
        <ul className="mt-6 space-y-2">
          {features.map((item, i) => (
            <li key={i} className="flex items-start">
              <CheckCircle
                size={16}
                className="text-legal-500 mr-2 mt-0.5 flex-shrink-0"
              />
              <span className="text-sm text-gray-600">{item}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default FeatureCard;
