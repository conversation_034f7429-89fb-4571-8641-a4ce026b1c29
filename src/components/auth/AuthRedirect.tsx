import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Spinner } from "@/components/ui/spinner";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";
import CompanySelectionGuard from "./CompanySelectionGuard";

interface AuthRedirectProps {
  children: React.ReactNode;
}

/**
 * Component that redirects authenticated users to the appropriate dashboard
 * and renders children for non-authenticated users
 */
const AuthRedirect: React.FC<AuthRedirectProps> = ({ children }) => {
  const { isAuthenticated, loading, user } = useAuth();
  const { isCompanySelected, isCompanySelectionLoading } =
    useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);

  // Show loading spinner while checking auth status
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Spinner size="lg" />
      </div>
    );
  }

  // If authenticated, handle company selection and role-based redirection
  if (isAuthenticated) {
    // If company selection is still in progress, show the company selection guard
    if (!isCompanySelected || isCompanySelectionLoading) {
      return (
        <CompanySelectionGuard>
          <div />{" "}
          {/* This will never render as CompanySelectionGuard handles the flow */}
        </CompanySelectionGuard>
      );
    }

    // Company is selected, redirect based on role
    if (permissions.isSigner) {
      return <Navigate to="/signer-dashboard" replace />;
    }
    return <Navigate to="/dashboard" replace />;
  }

  // If not authenticated, render the children (typically the landing page)
  return <>{children}</>;
};

export default AuthRedirect;
