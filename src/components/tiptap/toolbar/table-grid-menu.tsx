import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { useEditorStore } from "@/store/use-editor-store";
import { TableIcon, Plus, Minus, Merge } from "lucide-react";
import { useEffect, useRef, useState } from "react";

/**
 * Context-sensitive table actions menu that appears when a table is selected.
 * Provides options for column, row, and cell operations.
 */
const TableActionsMenu = ({
  onAction,
}: {
  onAction: (action: string) => void;
}) => {
  return (
    <>
      <DropdownMenuLabel className="text-xs font-medium text-gray-600 px-2 py-1">
        Column Actions
      </DropdownMenuLabel>
      <DropdownMenuItem
        onClick={() => onAction("addColumnBefore")}
        className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer"
      >
        <Plus className="size-4" />
        Add Column Before
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => onAction("addColumnAfter")}
        className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer"
      >
        <Plus className="size-4" />
        Add Column After
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => onAction("deleteColumn")}
        className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer text-red-600 hover:text-red-700"
      >
        <Minus className="size-4" />
        Delete Column
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      <DropdownMenuLabel className="text-xs font-medium text-gray-600 px-2 py-1">
        Row Actions
      </DropdownMenuLabel>
      <DropdownMenuItem
        onClick={() => onAction("addRowBefore")}
        className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer"
      >
        <Plus className="size-4" />
        Add Row Before
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => onAction("addRowAfter")}
        className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer"
      >
        <Plus className="size-4" />
        Add Row After
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => onAction("deleteRow")}
        className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer text-red-600 hover:text-red-700"
      >
        <Minus className="size-4" />
        Delete Row
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      <DropdownMenuLabel className="text-xs font-medium text-gray-600 px-2 py-1">
        Cell Actions
      </DropdownMenuLabel>
      <DropdownMenuItem
        onClick={() => onAction("mergeCells")}
        className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer"
      >
        <Merge className="size-4" />
        Merge Cells
      </DropdownMenuItem>
    </>
  );
};

/**
 * Table grid menu component that shows different content based on context:
 * - When no table is selected: Shows table size selector for inserting new tables
 * - When cursor is in a table: Shows table action menu with column/row/cell operations
 */
export const TableGridMenu = () => {
  const { editor } = useEditorStore();

  const [open, setOpen] = useState(false);

  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const popupRef = useRef<HTMLDivElement | null>(null);

  // Check if cursor is currently in a table
  const isInTable = editor?.isActive("table") || false;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node) &&
        !buttonRef.current?.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleTableAction = (action: string) => {
    if (!editor) return;

    setOpen(false);

    switch (action) {
      case "addColumnBefore":
        editor.chain().focus().addColumnBefore().run();
        break;
      case "addColumnAfter":
        editor.chain().focus().addColumnAfter().run();
        break;
      case "deleteColumn":
        editor.chain().focus().deleteColumn().run();
        break;
      case "addRowBefore":
        editor.chain().focus().addRowBefore().run();
        break;
      case "addRowAfter":
        editor.chain().focus().addRowAfter().run();
        break;
      case "deleteRow":
        editor.chain().focus().deleteRow().run();
        break;
      case "mergeCells":
        editor.chain().focus().mergeCells().run();
        break;
      case "splitCell":
        editor.chain().focus().splitCell().run();
        break;
      default:
        break;
    }
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <button
          ref={buttonRef}
          className="h-7 min-w-7 shrink-0 flex items-center justify-center rounded-sm hover:bg-neutral-200/80"
        >
          <TableIcon className="size-4" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" ref={popupRef}>
        {isInTable ? (
          <TableActionsMenu onAction={handleTableAction} />
        ) : (
          <div className="p-1">
            <TableGridSelector
              maxRows={10}
              maxCols={10}
              onSelect={(rows, cols) => {
                setOpen(false);
                editor?.chain().focus().insertTable({ rows, cols }).run();
              }}
            />
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

function TableGridSelector({
  maxRows = 10,
  maxCols = 10,
  onSelect,
}: {
  maxRows?: number;
  maxCols?: number;
  onSelect: (rows: number, cols: number) => void;
}) {
  const [hoveredRows, setHoveredRows] = useState(0);
  const [hoveredCols, setHoveredCols] = useState(0);

  return (
    <div className="p-4">
      <div className="mb-2 text-sm text-center">
        {hoveredCols > 0 && hoveredRows > 0
          ? `${hoveredCols} × ${hoveredRows}`
          : "Select size"}
      </div>
      <div
        className="grid gap-1"
        style={{ gridTemplateColumns: `repeat(${maxCols}, 20px)` }}
      >
        {Array.from({ length: maxRows * maxCols }).map((_, i) => {
          const row = Math.floor(i / maxCols) + 1;
          const col = (i % maxCols) + 1;
          const isSelected = row <= hoveredRows && col <= hoveredCols;

          return (
            <div
              key={i}
              onMouseEnter={() => {
                setHoveredRows(row);
                setHoveredCols(col);
              }}
              onClick={() => onSelect(row, col)}
              className={`w-5 h-5 border rounded-sm cursor-pointer ${
                isSelected ? "bg-blue-500" : "bg-gray-100 hover:bg-gray-200"
              }`}
            />
          );
        })}
      </div>
    </div>
  );
}
