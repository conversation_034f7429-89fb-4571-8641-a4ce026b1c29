import React, { useState, useEffect } from "react";
import { AuthState } from "./types";
import { APIClient } from "@/integrations/legal-concierge/client";

export const useAuthState = (): AuthState & {
  setAuthState: (state: Partial<AuthState>) => void;
  handleAuthChange: (silent: boolean) => Promise<void>;
} => {
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    isMfaVerified: false,
    userEmail: null,
    user: null,
    loading: true,
  });

  const setAuthState = (newState: Partial<AuthState>) => {
    setState((prevState) => ({ ...prevState, ...newState }));
  };

  const fetchUserProfile = async () => {
    const { data, error } = await new APIClient().profile();
    if (!error) {
      return data;
    }
    //handle errrorr
  };

  const handleAuthChange = React.useCallback(async () => {
    setAuthState({ loading: true });
    const profile = await fetchUserProfile();
    if (profile) {
      setAuthState({
        isMfaVerified: true,
        isAuthenticated: true,
        userEmail: profile.email,
        user: profile,
        loading: false,
      });
    } else {
      setAuthState({
        userEmail: null,
        user: null,
        isMfaVerified: false,
        isAuthenticated: false,
        loading: false,
      });
    }
  }, []);

  useEffect(() => {
    handleAuthChange();
  }, [handleAuthChange, state.isAuthenticated]);

  return {
    ...state,
    isAuthenticated: state.user != null,
    setAuthState,
    handleAuthChange,
  };
};
