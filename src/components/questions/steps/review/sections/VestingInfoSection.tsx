import React, { useEffect, useState } from "react";
import { QuestionnaireFormData } from "../../../types";
import { Card, CardContent } from "@/components/common/Card";
import { Check, AlertTriangle } from "lucide-react";
import { vestingService, VestingScheduleData } from "@/services/vesting";
import { format } from "date-fns";

interface VestingInfoSectionProps {
  formData: QuestionnaireFormData;
}

const VestingInfoSection: React.FC<VestingInfoSectionProps> = ({
  formData,
}) => {
  const [savedSchedules, setSavedSchedules] = useState<VestingScheduleData[]>(
    []
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadVestingSchedules = async () => {
      setLoading(true);
      try {
        const schedules = await vestingService.getVestingSchedules();
        setSavedSchedules(schedules);
      } catch (error) {
        console.error("Error loading vesting schedules:", error);
      } finally {
        setLoading(false);
      }
    };

    loadVestingSchedules();
  }, []);

  const isVesterSchedules =
    savedSchedules.length > 0 ||
    formData.vestingInfo.filter((v) => v.isVester).length > 0;

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-3 flex items-center">
          <Check size={18} className="text-green-500 mr-2" />
          Vesting Information
        </h3>

        {loading ? (
          <p className="text-gray-500">Loading vesting information...</p>
        ) : savedSchedules.length > 0 ? (
          <div className="space-y-3">
            {savedSchedules.map((schedule) => {
              return (
                <div
                  key={schedule.director_id}
                  className="grid grid-cols-1 md:grid-cols-4 gap-3 p-3 border rounded-md"
                >
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p>{schedule.director_name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Vesting Schedule</p>
                    <p>
                      {schedule.schedule_type === "Standard"
                        ? "4 years, 1 year cliff"
                        : schedule.schedule_type === "Monthly"
                          ? "4 years, no cliff"
                          : "None"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Start Date</p>
                    <p>{format(schedule.vesting_start_date, "MMM d, yyyy")}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Total Shares</p>
                    <p>{schedule.total_shares.toLocaleString()}</p>
                  </div>
                  <div className="md:col-span-4">
                    <p className="text-sm text-gray-500">Acceleration</p>
                    <p>
                      {schedule.acceleration === "SingleTrigger"
                        ? "Single Trigger"
                        : schedule.acceleration === "DoubleTrigger"
                          ? "Double Trigger"
                          : "None"}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        ) : formData.vestingInfo.filter((v) => v.isVester).length > 0 ? (
          <div className="space-y-3">
            {formData.vestingInfo
              .filter((v) => v.isVester)
              .map((vesting) => {
                const director = formData.directors.find(
                  (d) => d.id === vesting.id
                );

                if (!director || !vesting.isVester) return null;

                return (
                  <div
                    key={vesting.id}
                    className="grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border rounded-md"
                  >
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p>{director.name || "Unnamed"}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Vesting Schedule</p>
                      <p>
                        {vesting.vestingSchedule === "Standard"
                          ? "4 years, 1 year cliff"
                          : vesting.vestingSchedule === "Monthly"
                            ? "4 years, no cliff"
                            : "None"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Acceleration</p>
                      <p>
                        {vesting.acceleration === "SingleTrigger"
                          ? "Single Trigger"
                          : vesting.acceleration === "DoubleTrigger"
                            ? "Double Trigger"
                            : "None"}
                      </p>
                    </div>
                  </div>
                );
              })}

            {formData.vestingInfo.filter((v) => v.isVester).length === 0 && (
              <p className="text-gray-500">No vesting schedules selected</p>
            )}
          </div>
        ) : (
          <p className="text-gray-500">No vesting information added</p>
        )}

        {isVesterSchedules && (
          <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-amber-800">
                Important Tax Consideration
              </p>
              <p className="text-sm text-amber-700 mt-1">
                Shares subject to vesting may have tax implications under
                Section 83(b) of the Internal Revenue Code. Consider filing an
                83(b) election with the IRS within 30 days of acquiring
                restricted shares to potentially reduce future tax liability.
                Consult a tax professional for advice specific to your
                situation.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VestingInfoSection;
