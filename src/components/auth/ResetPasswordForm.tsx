import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Button from "@/components/common/Button";
import { ChevronRight, Lock } from "lucide-react";
import { Link } from "react-router-dom";

interface ResetPasswordFormProps {
  email: string;
  token: string;
  newPassword: string;
  setNewPassword: (password: string) => void;
  confirmPassword: string;
  setConfirmPassword: (password: string) => void;
  loading: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({
  email,
  token,
  newPassword,
  setNewPassword,
  confirmPassword,
  setConfirmPassword,
  loading,
  onSubmit,
}) => {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="new-password">New Password</Label>
        <div className="relative">
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
            <Lock size={18} />
          </div>
          <Input
            id="new-password"
            type="password"
            placeholder="••••••••"
            className="pl-10"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirm-password">Confirm Password</Label>
        <div className="relative">
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
            <Lock size={18} />
          </div>
          <Input
            id="confirm-password"
            type="password"
            placeholder="••••••••"
            className="pl-10"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
          />
        </div>
      </div>

      <Button
        type="submit"
        className="w-full"
        loading={loading}
        icon={<ChevronRight size={16} />}
        iconPosition="right"
      >
        Reset Password
      </Button>

      <div className="text-center mt-4">
        <Link to="/login" className="text-sm text-blue-600 hover:text-blue-800">
          Back to Login
        </Link>
      </div>
    </form>
  );
};

export default ResetPasswordForm;
