import React, { useState, useRef, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Info,
  AlertTriangle,
  CheckCircle2,
  FileText,
  Calendar,
  Shield,
} from "lucide-react";

interface Section83bInfoProps {
  onConfirmationChange: (confirmed: boolean) => void;
  confirmed: boolean;
}

const Section83bInfo: React.FC<Section83bInfoProps> = ({
  onConfirmationChange,
  confirmed,
}) => {
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Handle scroll event to detect when user has reached the bottom
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;

    // Check if the user has scrolled to the bottom
    if (element.scrollHeight - element.scrollTop <= element.clientHeight + 20) {
      setHasScrolledToBottom(true);
    }
  };

  // Check if already at bottom on initial render (for small screens)
  useEffect(() => {
    if (scrollRef.current) {
      const element = scrollRef.current;
      if (element.scrollHeight <= element.clientHeight) {
        setHasScrolledToBottom(true);
      }
    }
  }, []);

  return (
    <div className="space-y-4 border rounded-lg p-4 bg-white">
      <div className="flex items-center gap-2 mb-2">
        <AlertTriangle className="h-5 w-5 text-amber-500" />
        <h3 className="text-lg font-semibold">
          Important Tax Information: Section 83(b) Election
        </h3>
      </div>

      <Alert className="bg-amber-50 border-amber-200">
        <AlertTriangle className="h-4 w-4 text-amber-500" />
        <AlertTitle>Tax Implications of Vesting Schedules</AlertTitle>
        <AlertDescription>
          Because you have chosen to implement a vesting schedule, your shares
          may be subject to particular tax rules under Section 83 of the
          Internal Revenue Code. If shares are considered "restricted" (i.e.,
          subject to forfeiture or a company's right of repurchase), you could
          face ordinary income taxation on any increase in share value as those
          restrictions lapse.
        </AlertDescription>
      </Alert>

      <div className="h-64 border rounded">
        <ScrollArea
          className="h-full p-4"
          onScroll={handleScroll}
          ref={scrollRef}
        >
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                <h4 className="text-md font-semibold">
                  1. What Is an 83(b) Election?
                </h4>
              </div>
              <p className="text-sm text-gray-700">
                An 83(b) election is an optional filing that can potentially
                lower your future tax liability. By filing an 83(b), you agree
                to pay taxes on the difference between your purchase price and
                the fair market value of the shares at the time you acquire
                them, rather than each time vesting restrictions expire.
              </p>
              <ul className="pl-6 text-sm space-y-2 text-gray-700">
                <li>
                  <span className="font-medium">Potential Benefit:</span> If you
                  expect the share value to grow significantly, paying taxes at
                  the original (lower) value might reduce your overall tax
                  burden.
                </li>
                <li>
                  <span className="font-medium">Risk:</span> If the value of the
                  shares decreases or if you leave the company before full
                  vesting, you won't get a refund for taxes already paid.
                </li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-500" />
                <h4 className="text-md font-semibold">
                  2. Important Filing Deadlines and Responsibilities
                </h4>
              </div>
              <ul className="pl-6 text-sm space-y-2 text-gray-700">
                <li>
                  <span className="font-medium">30-Day Window:</span> You must
                  file your 83(b) election with the IRS within 30 days of
                  acquiring your restricted shares. Missing this window can
                  result in higher tax liabilities if the shares increase in
                  value later.
                </li>
                <li>
                  <span className="font-medium">Supporting Documents:</span> In
                  addition to sending the election form to the IRS, you may need
                  to include a copy with your annual tax return.
                </li>
                <li>
                  <span className="font-medium">Personal Accountability:</span>{" "}
                  Even if you request help from your company or anyone else, you
                  remain responsible for submitting the 83(b) election
                  accurately and on time.
                </li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-blue-500" />
                <h4 className="text-md font-semibold">
                  3. Consider an 83(b) Election
                </h4>
              </div>
              <ul className="pl-6 text-sm space-y-2 text-gray-700">
                <li>
                  <span className="font-medium">Ask Yourself:</span> Do you
                  anticipate that your shares will appreciate over time? Are you
                  comfortable paying taxes upfront on their current value rather
                  than when they vest?
                </li>
                <li>
                  <span className="font-medium">
                    Evaluate Your Risk Tolerance:
                  </span>{" "}
                  Could the value of the shares decrease, causing you to overpay
                  taxes today?
                </li>
                <li>
                  <span className="font-medium">Consult a Professional:</span>{" "}
                  Before making a final decision, speak with a qualified tax
                  advisor who understands your overall financial situation and
                  can guide you on potential state, local, and federal
                  implications.
                </li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-500" />
                <h4 className="text-md font-semibold">4. Disclaimers</h4>
              </div>
              <ul className="pl-6 text-sm space-y-2 text-gray-700">
                <li>
                  <span className="font-medium">No Tax or Legal Advice:</span>{" "}
                  Any information provided here is for general informational
                  purposes and should not be taken as personalized legal or tax
                  advice.
                </li>
                <li>
                  <span className="font-medium">Professional Guidance:</span>{" "}
                  Your specific circumstances can vary widely; always consult a
                  licensed tax professional regarding your personal tax
                  strategy.
                </li>
                <li>
                  <span className="font-medium">
                    No Responsibility for Filing:
                  </span>{" "}
                  Founders Form, PC (and any related platforms or services) does
                  not take responsibility for filing your 83(b) election or
                  ensuring its correctness. All filing obligations rest solely
                  with you, the purchaser.
                </li>
                <li>
                  <span className="font-medium">
                    Use at Your Own Discretion:
                  </span>{" "}
                  While we strive to share helpful insights, we cannot guarantee
                  outcomes or regulatory compliance. Any decisions you make
                  about filing or not filing an 83(b) election are your own.
                </li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Info className="h-5 w-5 text-blue-500" />
                <h4 className="text-md font-semibold">Next Steps</h4>
              </div>
              <ul className="pl-6 text-sm space-y-2 text-gray-700">
                <li>Discuss your situation with a tax professional.</li>
                <li>
                  Decide if filing an 83(b) election aligns with your goals and
                  comfort level regarding potential risks.
                </li>
                <li>
                  <span className="font-medium">Stay Informed:</span> Tax laws
                  can change, and each person's situation differs. Keep up to
                  date and maintain clear records in case of audits or
                  additional inquiries.
                </li>
              </ul>
              <p className="text-sm text-gray-700 mt-4">
                By thoroughly considering an 83(b) election now, you can
                potentially reduce future tax obligations—just remember to seek
                expert advice and take proactive steps to meet the IRS
                requirements.
              </p>
            </div>
          </div>
        </ScrollArea>
      </div>

      <div
        className={`mt-4 flex items-start space-x-2 ${!hasScrolledToBottom ? "opacity-50" : ""}`}
      >
        <Checkbox
          id="confirm-83b"
          checked={confirmed}
          onCheckedChange={(checked) => {
            if (hasScrolledToBottom) {
              onConfirmationChange(checked === true);
            }
          }}
          disabled={!hasScrolledToBottom}
        />
        <Label
          htmlFor="confirm-83b"
          className={`text-sm ${!hasScrolledToBottom ? "text-gray-400" : "text-gray-700"}`}
        >
          I confirm that I have read the information about Section 83(b)
          Election and understand the potential tax implications of my vesting
          schedule.
          {!hasScrolledToBottom && (
            <span className="block text-amber-500 text-xs mt-1">
              Please scroll through all information before confirming
            </span>
          )}
        </Label>
      </div>
    </div>
  );
};

export default Section83bInfo;
