import { VestingScheduleData } from "./types";
import { format, addMonths } from "date-fns";
import { calculateVestedSharesAtDate } from "./vestingCalculation";

/**
 * Generate monthly vesting data for a chart
 */
export function generateVestingChartData(schedules: VestingScheduleData[]): {
  dates: string[];
  datasets: any[];
} {
  if (schedules.length === 0) {
    return { dates: [], datasets: [] };
  }

  // Find earliest start date among all schedules
  const earliestDate = new Date(
    Math.min(...schedules.map((s) => s.vesting_start_date.getTime()))
  );

  // Generate 49 months from earliest start date (0-48 months inclusive)
  const dates: string[] = [];
  for (let i = 0; i <= 48; i++) {
    const date = addMonths(earliestDate, i);
    dates.push(format(date, "MMM yyyy"));
  }

  // Generate dataset for each schedule
  const datasets = schedules.map((schedule) => {
    const data = dates.map((_, index) => {
      const date = addMonths(earliestDate, index);
      return calculateVestedSharesAtDate(schedule, date);
    });

    return {
      label: schedule.director_name,
      data,
      scheduleType: schedule.schedule_type,
      totalShares: schedule.total_shares,
    };
  });

  return { dates, datasets };
}
