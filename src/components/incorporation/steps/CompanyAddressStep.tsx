import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FormData, US_STATES } from "../types";
import { AlertTriangle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CompanyAddressStepProps {
  formData: FormData;
  updateFormData: (field: string, value: string) => void;
}

const CompanyAddressStep: React.FC<CompanyAddressStepProps> = ({
  formData,
  updateFormData,
}) => {
  const [addressVerified, setAddressVerified] = useState<boolean | null>(null);
  const { toast } = useToast();

  // Automatically verify address whenever any field changes
  useEffect(() => {
    // Only verify if at least one field has been filled out
    if (
      formData.companyAddress.street ||
      formData.companyAddress.city ||
      formData.companyAddress.state ||
      formData.companyAddress.zipCode
    ) {
      verifyAddress();
    }
  }, [
    formData.companyAddress.street,
    formData.companyAddress.city,
    formData.companyAddress.state,
    formData.companyAddress.zipCode,
  ]);

  const verifyAddress = () => {
    const { street, city, state, zipCode } = formData.companyAddress;

    // Simple validation first
    if (!street || !city || !state || !zipCode) {
      setAddressVerified(false);
      return;
    }

    // For demo purposes, we'll use a simple validation:
    // ZIP code format validation (basic 5-digit US zip)
    const zipRegex = /^\d{5}$/;
    const isValidZip = zipRegex.test(zipCode);

    // Set as verified only if zip is valid
    setAddressVerified(isValidZip);

    if (isValidZip) {
      toast({
        title: "Address validated",
        description: "The address format appears to be valid.",
      });
    }
  };

  const handleAddressChange = (field: string, value: string) => {
    updateFormData(field, value);
  };

  return (
    <AnimatedTransition>
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="street">Street Address</Label>
          <Input
            id="street"
            placeholder="123 Main St"
            value={formData.companyAddress.street}
            onChange={(e) =>
              handleAddressChange("companyAddress.street", e.target.value)
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="city">City</Label>
          <Input
            id="city"
            placeholder="San Francisco"
            value={formData.companyAddress.city}
            onChange={(e) =>
              handleAddressChange("companyAddress.city", e.target.value)
            }
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="state">State</Label>
            <Select
              value={formData.companyAddress.state}
              onValueChange={(value) =>
                handleAddressChange("companyAddress.state", value)
              }
            >
              <SelectTrigger id="state">
                <SelectValue placeholder="Select state" />
              </SelectTrigger>
              <SelectContent>
                {US_STATES.map((state) => (
                  <SelectItem key={state.value} value={state.value}>
                    {state.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="zipCode">ZIP Code</Label>
            <Input
              id="zipCode"
              placeholder="94105"
              value={formData.companyAddress.zipCode}
              onChange={(e) =>
                handleAddressChange("companyAddress.zipCode", e.target.value)
              }
            />
          </div>
        </div>

        {addressVerified === false && (
          <div className="mt-2 p-3 bg-red-50 border border-red-100 rounded-md flex items-start">
            <AlertTriangle className="text-red-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-red-800">
                Invalid address
              </p>
              <p className="text-xs text-red-700 mt-1">
                Please check all fields and ensure your ZIP code is valid (5
                digits).
              </p>
            </div>
          </div>
        )}
      </div>
    </AnimatedTransition>
  );
};

export default CompanyAddressStep;
