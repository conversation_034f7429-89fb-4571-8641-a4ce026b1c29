import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useServiceProviders } from "@/hooks/serviceProviders";
import { PromisedGrant } from "@/types/serviceProvider";
import { toast } from "sonner";
import BoardApprovalSection from "./BoardApprovalSection";

interface EquityIssueDialogProps {
  isOpen: boolean;
  onClose: () => void;
  grant: PromisedGrant;
}

const EquityIssueDialog: React.FC<EquityIssueDialogProps> = ({
  isOpen,
  onClose,
  grant,
}) => {
  const { issueEquityGrant } = useServiceProviders();
  const [fairMarketValue, setFairMarketValue] = useState("0.10");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<"initial" | "board-approval">("initial");

  const handleSubmit = () => {
    setStep("board-approval");
  };

  const handleBoardApprovalComplete = async () => {
    setIsSubmitting(true);
    try {
      const success = await issueEquityGrant(
        grant.id,
        parseFloat(fairMarketValue)
      );
      if (success) {
        toast(
          "Equity grant successfully initiated. Board consent process is underway."
        );
        onClose();
      }
    } catch (error) {
      console.error("Error issuing equity grant:", error);
      toast("Failed to issue equity grant. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderContent = () => {
    if (step === "board-approval") {
      return (
        <BoardApprovalSection
          grant={grant}
          fairMarketValue={fairMarketValue}
          onCancel={() => setStep("initial")}
          onComplete={handleBoardApprovalComplete}
          isSubmitting={isSubmitting}
        />
      );
    }

    return (
      <>
        <DialogHeader>
          <DialogTitle>Issue Equity Grant</DialogTitle>
          <DialogDescription>
            Complete this form to issue the equity grant to{" "}
            {grant.serviceProviderName}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          <div>
            <Label htmlFor="grantType">Grant Type</Label>
            <div className="font-medium mt-1">
              {grant.grantType} {grant.optionType && `(${grant.optionType})`}
            </div>
          </div>

          <div>
            <Label htmlFor="shares">Number of Shares</Label>
            <div className="font-medium mt-1">
              {grant.shares.toLocaleString()}
            </div>
          </div>

          <div>
            <Label htmlFor="vestingSchedule">Vesting Schedule</Label>
            <div className="font-medium mt-1">{grant.vestingSchedule}</div>
          </div>

          <div>
            <Label htmlFor="fairMarketValue">
              Fair Market Value ($ per share)
            </Label>
            <Input
              id="fairMarketValue"
              value={fairMarketValue}
              onChange={(e) => setFairMarketValue(e.target.value)}
              placeholder="Enter current FMV"
              className="mt-1"
            />
            <p className="text-sm text-gray-500 mt-1">
              This value will be used for tax purposes and should reflect the
              company's current 409A valuation.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Processing..." : "Proceed to Board Approval"}
          </Button>
        </div>
      </>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        className={`sm:max-w-[${step === "board-approval" ? "700px" : "500px"}]`}
      >
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};

export default EquityIssueDialog;
