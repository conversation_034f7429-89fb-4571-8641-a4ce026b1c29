export interface Document {
  fileName: string;
  url: string;
  thumbnailUrl: string;
  id?: string;
  companyId?: string;
  type?: string;
  size?: number;
  createdAt?: string;
  updatedAt?: string;
  status?: DocumentStatus;
  category?: DocumentCategory;
  metadata?: Record<string, any>;
  isGenerated?: boolean;
  generatedAt?: string;
}

export type DocumentStatus =
  | "DRAFT"
  | "PENDING_SIGNATURE"
  | "SIGNED"
  | "COMPLETED"
  | "ARCHIVED";

export type DocumentCategory =
  | "INCORPORATION"
  | "BYLAWS"
  | "BOARD_RESOLUTION"
  | "STOCK_CERTIFICATE"
  | "INTELLECTUAL_PROPERTY"
  | "EMPLOYMENT"
  | "OTHER";

export interface DocumentUploadRequest {
  companyId: string;
  name: string;
  type: string;
  category?: DocumentCategory;
  metadata?: Record<string, any>;
  file: File;
}

export interface DocumentUpdateRequest {
  name?: string;
  type?: string;
  category?: DocumentCategory;
  status?: DocumentStatus;
  metadata?: Record<string, any>;
}

export interface DocumentSignatureRequest {
  documentId: string;
  signers: Array<{
    email: string;
    name?: string;
    role?: string;
  }>;
  message?: string;
  expiresAt?: string; // ISO date string
}

export interface DocumentFolder {
  id: string;
  companyId: string;
  name: string;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentFolderRequest {
  companyId: string;
  name: string;
  parentId?: string;
}

export interface DocumentFolderUpdateRequest {
  name?: string;
  parentId?: string;
}

export interface DocumentPreparationResponse {
  message: string;
  data: {
    preparationId: string;
    status: "preparing" | "ready" | "failed";
    documentsCount: number;
  };
}

export interface DocumentSignatureResponse {
  message: string;
  data: {
    signatureRequestId: string;
    status: "sent" | "pending" | "failed";
    signersCount: number;
    signers: Array<{
      email: string;
      name?: string;
      role?: string;
      status: "pending" | "sent" | "signed";
    }>;
  };
}

export interface DocumentSignatureSQSResponse {
  message: string;
  data: string;
  error: null;
  validationErrors: null;
}

export interface SignatureStatusResponse {
  message: string;
  data: number;
  error: null;
  validationErrors: null;
}
