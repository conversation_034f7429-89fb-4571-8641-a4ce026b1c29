import React from "react";
import {
  ExternalIncorporationFormData,
  ExternalIncorporationStep,
} from "./types";
import CompanyInfoStep from "./steps/CompanyInfoStep";
import StakeholdersStep from "./steps/StakeholdersStep";
import StockOptionsStep from "./steps/StockOptionsStep";
import TechContributionsStep from "./steps/TechContributionsStep";
import DocumentsStep from "./steps/DocumentsStep";
import RegisteredAgentStep from "./steps/RegisteredAgentStep";
import ReviewStep from "./steps/ReviewStep";

interface ExternalIncorporationStepRendererProps {
  currentStep: ExternalIncorporationStep;
  formData: ExternalIncorporationFormData;
  updateFormData: (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => void;
  confirmationChecked?: boolean;
  onConfirmationChange?: (checked: boolean) => void;
}

const ExternalIncorporationStepRenderer: React.FC<
  ExternalIncorporationStepRendererProps
> = ({
  currentStep,
  formData,
  updateFormData,
  confirmationChecked,
  onConfirmationChange,
}) => {
  switch (currentStep) {
    case "company-info":
      return (
        <CompanyInfoStep formData={formData} updateFormData={updateFormData} />
      );
    case "stakeholders":
      return (
        <StakeholdersStep formData={formData} updateFormData={updateFormData} />
      );
    case "stock-options":
      return (
        <StockOptionsStep formData={formData} updateFormData={updateFormData} />
      );
    case "tech-contributions":
      return (
        <TechContributionsStep
          formData={formData}
          updateFormData={updateFormData}
        />
      );
    case "documents":
      return (
        <DocumentsStep formData={formData} updateFormData={updateFormData} />
      );
    case "registered-agent":
      return (
        <RegisteredAgentStep
          formData={formData}
          updateFormData={updateFormData}
        />
      );
    case "review":
      return (
        <ReviewStep
          formData={formData}
          confirmationChecked={confirmationChecked}
          onConfirmationChange={onConfirmationChange}
        />
      );
    default:
      return <div>Step not found</div>;
  }
};

export default ExternalIncorporationStepRenderer;
