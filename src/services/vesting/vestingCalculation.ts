import { VestingScheduleData } from "./types";
import { differenceInMonths } from "date-fns";

/**
 * Calculate vested shares for a specific date
 */
export function calculateVestedSharesAtDate(
  schedule: VestingScheduleData,
  targetDate: Date
): number {
  const monthsElapsed = differenceInMonths(
    targetDate,
    schedule.vesting_start_date
  );

  // If we haven't reached vesting start date yet
  if (monthsElapsed < 0) {
    return 0;
  }

  // Standard schedule (with cliff)
  if (schedule.schedule_type === "Standard") {
    // Before cliff period
    if (monthsElapsed < schedule.cliff_months) {
      return 0;
    }

    // At cliff exactly
    if (monthsElapsed === schedule.cliff_months) {
      return Math.round(schedule.total_shares * 0.25); // 25% at cliff
    }

    // After cliff, before end of vesting period
    if (
      monthsElapsed > schedule.cliff_months &&
      monthsElapsed < schedule.vesting_period_months
    ) {
      const cliffPercentage = 0.25; // 25% at cliff
      const remainingPercentage = 0.75; // Remaining 75%
      const remainingMonths =
        schedule.vesting_period_months - schedule.cliff_months;
      const additionalMonths = monthsElapsed - schedule.cliff_months;

      const additionalPercentage =
        (additionalMonths / remainingMonths) * remainingPercentage;
      return Math.round(
        schedule.total_shares * (cliffPercentage + additionalPercentage)
      );
    }

    // After full vesting period
    return schedule.total_shares;
  }

  // Monthly schedule (no cliff)
  else if (schedule.schedule_type === "Monthly") {
    // Before end of vesting period
    if (monthsElapsed < schedule.vesting_period_months) {
      // Linear monthly vesting - each month vests 1/48th of the shares
      const monthlyVesting =
        schedule.total_shares / schedule.vesting_period_months;
      return Math.round(monthlyVesting * monthsElapsed);
    }

    // After full vesting period
    return schedule.total_shares;
  }

  return 0;
}
