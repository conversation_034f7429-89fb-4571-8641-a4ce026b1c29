import React from "react";
import { Button } from "@/components/ui/button";
import TaskItem from "./TaskItem";
import { Task } from "./types";

interface TaskListProps {
  tasks: { [key: string]: Task };
  onTaskComplete: (id: string) => Promise<void>;
  onMarkAllComplete: () => Promise<void>;
  renderDialogContent?: (task: Task) => React.ReactNode;
  allTasksComplete?: boolean;
  isLoading?: boolean;
}

const TaskList: React.FC<TaskListProps> = ({
  tasks,
  onTaskComplete,
  onMarkAllComplete,
  renderDialogContent,
  allTasksComplete = false,
  isLoading = false,
}) => {
  // Use the passed allTasksComplete prop or calculate it locally as fallback
  const tasksComplete =
    allTasksComplete || Object.values(tasks).every((task) => task.isComplete);

  // Task completion is tracked via API and React Query cache
  // All state is managed in memory and through server data

  return (
    <div>
      <ul className="space-y-4">
        {Object.values(tasks).map((task) => (
          <TaskItem
            key={task.id}
            task={task}
            onComplete={onTaskComplete}
            renderDialogContent={renderDialogContent}
          />
        ))}
      </ul>
      {!tasksComplete && (
        <div className="mt-6">
          <Button onClick={onMarkAllComplete} disabled={isLoading}>
            {isLoading ? "Completing..." : "Mark All as Complete"}
          </Button>
        </div>
      )}
      {tasksComplete && (
        <div className="mt-6 text-center">
          <p className="text-green-500 font-medium">
            Congratulations, you've completed all essential steps!
          </p>
        </div>
      )}
    </div>
  );
};

export default TaskList;
