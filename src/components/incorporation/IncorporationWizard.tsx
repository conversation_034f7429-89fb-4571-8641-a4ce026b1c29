import React from "react";
import { Card } from "@/components/common/Card";
import WizardHeader, { WIZARD_STEPS } from "./WizardHeader";
import WizardContent from "./WizardContent";
import WizardFooter from "./WizardFooter";
import { useIncorporationWizard } from "./hooks/useIncorporationWizard";

const IncorporationWizard: React.FC = () => {
  const {
    step,
    formData,
    loading,
    nameAvailable,
    checkingName,
    updateFormData,
    handleNextStep,
    handlePreviousStep,
    handleSubmit,
    checkCompanyName,
  } = useIncorporationWizard();

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <WizardHeader currentStep={step} />

      <WizardContent
        currentStep={step}
        formData={formData}
        updateFormData={updateFormData}
        nameAvailable={nameAvailable}
        checkingName={checkingName}
        checkCompanyName={checkCompanyName}
      />

      <WizardFooter
        currentStep={step}
        loading={loading}
        onPrevious={handlePreviousStep}
        onNext={handleNextStep}
        onSubmit={handleSubmit}
      />
    </Card>
  );
};

export default IncorporationWizard;
