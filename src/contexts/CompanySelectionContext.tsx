import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";
import { useCompanies } from "@/integrations/legal-concierge/hooks/useCompanies";
import { Company } from "@/integrations/legal-concierge/types";

interface CompanySelectionState {
  isCompanySelected: boolean;
  isCompanySelectionLoading: boolean;
  needsCompanySelection: boolean;
  hasMultipleCompanies: boolean;
  companies: Company[];
}

interface CompanySelectionContextType extends CompanySelectionState {
  markCompanySelected: () => void;
  markCompanySelectionNeeded: () => void;
}

const CompanySelectionContext =
  createContext<CompanySelectionContextType | null>(null);

export const useCompanySelection = () => {
  const context = useContext(CompanySelectionContext);
  if (!context) {
    throw new Error(
      "useCompanySelection must be used within a CompanySelectionProvider"
    );
  }
  return context;
};

export const CompanySelectionProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const { data: companies, isLoading: isCompaniesLoading } = useCompanies(
    user?.companyId == null
  );

  const [state, setState] = useState<CompanySelectionState>({
    isCompanySelected: false,
    isCompanySelectionLoading: true,
    needsCompanySelection: false,
    hasMultipleCompanies: false,
    companies: [],
  });

  // Update state based on auth and company data
  useEffect(() => {
    if (authLoading || isCompaniesLoading) {
      setState((prev) => ({
        ...prev,
        isCompanySelectionLoading: true,
      }));
      return;
    }

    // If user is not authenticated, reset state
    if (!user) {
      setState({
        isCompanySelected: false,
        isCompanySelectionLoading: false,
        needsCompanySelection: false,
        hasMultipleCompanies: false,
        companies: [],
      });
      return;
    }

    // If user already has a company selected
    if (user.companyId) {
      setState((prev) => ({
        ...prev,
        isCompanySelected: true,
        isCompanySelectionLoading: false,
        needsCompanySelection: false,
        hasMultipleCompanies: (companies?.length || 0) > 1,
        companies: companies || [],
      }));
      return;
    }

    // If user doesn't have a company selected
    const companyList = companies || [];
    setState((prev) => ({
      ...prev,
      isCompanySelected: false,
      isCompanySelectionLoading: false,
      needsCompanySelection: companyList.length > 0,
      hasMultipleCompanies: companyList.length > 1,
      companies: companyList,
    }));
  }, [user, companies, authLoading, isCompaniesLoading]);

  const markCompanySelected = () => {
    setState((prev) => ({
      ...prev,
      isCompanySelected: true,
      needsCompanySelection: false,
    }));
  };

  const markCompanySelectionNeeded = () => {
    setState((prev) => ({
      ...prev,
      isCompanySelected: false,
      needsCompanySelection: true,
    }));
  };

  return (
    <CompanySelectionContext.Provider
      value={{
        ...state,
        markCompanySelected,
        markCompanySelectionNeeded,
      }}
    >
      {children}
    </CompanySelectionContext.Provider>
  );
};
