import { QuestionnaireFormData } from "../../../types";
import { Officer } from "../../../types/Officer";
import { format, addMonths, differenceInMonths } from "date-fns";
import { vestingService, VestingScheduleData } from "@/services/vesting";

// Helper to calculate vesting based on schedule type and months elapsed
export const calculateVestingForMonth = (
  schedule: string,
  monthsElapsed: number,
  totalShares: number,
  cliffMonths: number = 12
): number => {
  // Standard schedule (4 years, 1 year cliff)
  if (schedule === "Standard") {
    // Before cliff - no shares vest
    if (monthsElapsed < cliffMonths) {
      return 0;
    }
    // At cliff - 25% vests immediately
    else if (monthsElapsed === cliffMonths) {
      return Math.round(totalShares * 0.25);
    }
    // After cliff - monthly vesting of remaining 75%
    else if (monthsElapsed > cliffMonths && monthsElapsed <= 48) {
      // 25% at cliff, then remaining 75% spread over 36 months
      const cliffAmount = totalShares * 0.25;
      const monthlyAmount = (totalShares * 0.75) / (48 - cliffMonths);
      const additionalMonths = monthsElapsed - cliffMonths;
      return Math.round(cliffAmount + monthlyAmount * additionalMonths);
    }
    // After 4 years - fully vested
    else {
      return totalShares;
    }
  }
  // Monthly schedule (4 years, no cliff)
  else if (schedule === "Monthly") {
    if (monthsElapsed <= 48) {
      // For monthly schedule, we vest 1/48th of total shares each month
      // This creates a linear increase with no cliff
      const monthlyAmount = totalShares / 48;
      return Math.round(monthlyAmount * monthsElapsed);
    } else {
      return totalShares;
    }
  }

  return 0;
};

// Generate vesting timeline for each director
export const generateVestingTimeline = (
  director: Officer,
  formData: QuestionnaireFormData,
  calculateShares: (percentage: number) => number,
  vestingStartDate: Date = new Date()
) => {
  const vestingInfo = formData.vestingInfo.find((v) => v.id === director.id);
  if (!vestingInfo?.vestingSchedule) return null;

  const totalShares = calculateShares(director.stockOwnership);
  const timeline = [];

  // Generate 49 months of data (0-48 months, inclusive)
  for (let month = 0; month <= 48; month++) {
    const date = addMonths(vestingStartDate, month);
    const formattedDate = format(date, "MMM yyyy");

    const cliffMonths = vestingInfo.vestingSchedule === "Standard" ? 12 : 0;
    const vestedShares = calculateVestingForMonth(
      vestingInfo.vestingSchedule,
      month,
      totalShares,
      cliffMonths
    );

    timeline.push({
      month,
      date: formattedDate,
      shares: vestedShares,
      percentage: Math.round((vestedShares / totalShares) * 100),
    });
  }

  return {
    director: director.name || "Unnamed",
    schedule: vestingInfo.vestingSchedule,
    data: timeline,
  };
};

// Create a unified dataset for the chart with significant months
export const createChartData = (
  vestingTimelines: any[],
  vestingStartDate: Date = new Date(),
  significantMonths: number[] = [0, 6, 12, 18, 24, 30, 36, 42, 48]
) => {
  return significantMonths.map((month) => {
    const date = addMonths(vestingStartDate, month);
    const dataPoint: any = {
      month,
      label: format(date, "MMM yyyy"),
    };

    // Add each director's vested shares at this month
    vestingTimelines.forEach((timeline) => {
      if (!timeline) return;
      const monthData = timeline.data.find((d: any) => d.month === month);
      if (monthData) {
        dataPoint[timeline.director] = monthData.shares;
      }
    });

    return dataPoint;
  });
};

// Find maximum shares for Y-axis scale
export const findMaxSharesValue = (vestingTimelines: any[]) => {
  return Math.max(
    ...vestingTimelines.flatMap((timeline) =>
      timeline ? timeline.data.map((d: any) => d.shares) : [0]
    ),
    1 // Ensure at least 1 for empty data
  );
};

// Format vesting data for Supabase storage
export const prepareVestingDataForStorage = (
  directors: Officer[],
  vestingInfo: any[],
  issuedShares: number,
  incorporationDate: Date | null = null
): VestingScheduleData[] => {
  return directors
    .filter((director) => {
      const vInfo = vestingInfo.find((v) => v.id === director.id);
      return vInfo?.isVester && vInfo?.vestingSchedule;
    })
    .map((director) => {
      const vInfo = vestingInfo.find((v) => v.id === director.id);
      const totalShares = Math.round(
        (director.stockOwnershipPercentage / 100) * issuedShares
      );

      // Use incorporation date as vesting start date, or today if not available
      const startDate = incorporationDate || new Date();

      return {
        director_id: director.id,
        director_name: director.name || "Unnamed",
        schedule_type: vInfo?.vestingSchedule || "Standard",
        vesting_start_date: startDate,
        total_shares: totalShares,
        cliff_months: vInfo?.vestingSchedule === "Standard" ? 12 : 0,
        vesting_period_months: 48, // 4 years standard
        acceleration: vInfo?.acceleration || "None",
      };
    });
};

// Load vesting data from Supabase
export const loadVestingData = async (): Promise<VestingScheduleData[]> => {
  return await vestingService.getVestingSchedules();
};
