import { APIClient } from "@/integrations/legal-concierge/client";
import { TechnologyRequest } from "@/integrations/legal-concierge/types/Technology";
import { IntellectualPropertyRequest } from "@/integrations/legal-concierge/types/IntellectualProperty";
import { QuestionnaireFormData } from "./types";

// Save tech assignments to legal-concierge API
export const saveTechAssignments = async (
  _userId: string, // Keeping parameter for API consistency but not using it
  companyId: string,
  formData: QuestionnaireFormData
): Promise<void> => {
  try {
    const api = new APIClient();

    // First, save the overall company technology description
    // Use the actual company tech description from formData
    const technologyRequest: TechnologyRequest = {
      technologyDescription: formData.companyTech || null,
    };

    await api.updateTechnology(companyId, technologyRequest);
  } catch (error) {
    console.error("Error saving tech assignments:", error);
    throw error;
  }
};
