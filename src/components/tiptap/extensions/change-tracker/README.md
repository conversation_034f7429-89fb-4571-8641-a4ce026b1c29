# TipTap Change Tracking Extension

A simple document change tracking extension for TipTap editor based on ProseMirror's change tracking capabilities.

## Features

- **Document History**: Track all changes made to the document with commit messages and timestamps
- **Visual Highlighting**: See additions in green and deletions with strikethrough
- **Keyboard Shortcuts**: Save changes quickly with Ctrl+S
- **Commit Management**: Save changes with descriptive messages
- **History Panel**: View and navigate through document history
- **Hover Highlighting**: Highlight specific commits when hovering over history items

## Installation

The extension is already included in this project. To use it in your TipTap editor:

```typescript
import { useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import TrackChangeExtension from './extensions/change-tracker';

const editor = useEditor({
  extensions: [
    StarterKit,
    TrackChangeExtension.configure({
      enabled: true,
      userId: 'user-123',
      userName: '<PERSON>',
    }),
  ],
  content: '<p>Your document content...</p>',
});
```

## Configuration Options

```typescript
TrackChangeExtension.configure({
  enabled: boolean,     // Enable/disable change tracking (default: true)
  userId?: string,      // User ID for attribution (optional)
  userName?: string,    // User name for attribution (optional)
})
```

## Commands

The extension provides several commands you can use:

```typescript
// Save changes with a message
editor.commands.commitChanges('Fixed typos in introduction');

// Save document (alias for commitChanges)
editor.commands.saveDocument('Updated content');

// Check if there are unsaved changes
const hasChanges = editor.commands.hasUncommittedChanges();

// Get current track state
const trackState = editor.commands.getTrackState();

// Highlight a specific commit
editor.commands.highlightCommit(commit);

// Clear highlighting
editor.commands.highlightCommit(null);
```

## Utility Functions

```typescript
import { 
  hasUncommittedChanges, 
  saveDocument, 
  addSaveShortcut 
} from './extensions/change-tracker/utils';

// Check for unsaved changes
const hasChanges = hasUncommittedChanges(editor);

// Save document
const success = saveDocument(editor, 'My commit message');

// Add Ctrl+S keyboard shortcut
const cleanup = addSaveShortcut(editor);
// Call cleanup() when component unmounts
```

## Components

### HistoryPanel

A React component for displaying document history:

```typescript
import { HistoryPanel } from './extensions/change-tracker/HistoryPanel';

<HistoryPanel
  editor={editor}
  isOpen={isHistoryPanelOpen}
  onClose={() => setIsHistoryPanelOpen(false)}
/>
```

## Styling

The extension includes default CSS styles for change visualization:

- **Insertions**: Green background (`#d4edda`)
- **Deletions**: Red background with strikethrough (`#f8d7da`)
- **Highlights**: Yellow background for commit highlighting (`#fff3cd`)

Import the styles in your component:

```typescript
import './extensions/change-tracker/styles.css';
```

## Example Usage

See `example.tsx` for a complete working example that demonstrates:

- Setting up the editor with change tracking
- Adding keyboard shortcuts
- Displaying save status
- Using the history panel
- Visual change highlighting

## How It Works

The extension is built on top of ProseMirror's change tracking system:

1. **TrackState**: Maintains document history and blame map
2. **Commits**: Store snapshots of changes with messages and timestamps
3. **Blame Map**: Tracks which parts of the document belong to which commits
4. **Plugins**: Handle state management and visual decorations

## Data Structure

```typescript
class Commit {
  message: string;    // Commit message
  time: Date;        // Timestamp
  steps: any[];      // ProseMirror steps (for undo/redo)
  maps: any[];       // Position mappings
}

class TrackState {
  blameMap: Span[];           // Document attribution
  commits: Commit[];          // History of commits
  uncommittedSteps: any[];    // Pending changes
  uncommittedMaps: any[];     // Pending mappings
}
```

## Keyboard Shortcuts

- **Ctrl+S** (or Cmd+S on Mac): Save current changes

## Browser Storage

Currently, the extension stores change history in memory only. For persistent storage, you would need to:

1. Save commits to localStorage or a backend API
2. Restore state when initializing the editor
3. Handle synchronization between multiple users (for collaborative editing)

## Limitations

- History is stored in memory only (not persistent)
- No collaborative editing support (single user)
- No conflict resolution for simultaneous edits
- Simple visual highlighting (not as advanced as Google Docs)

## Future Enhancements

- Persistent storage (localStorage/backend)
- Collaborative editing support
- Advanced visual diff highlighting
- Commit reverting functionality
- Export/import of document history
- User avatars and attribution
- Real-time change notifications
