import React, { useState } from "react";
import { X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface CommentReplyFormProps {
  commentId: string;
  position: any;
  onCancel: () => void;
}

const CommentReplyForm: React.FC<CommentReplyFormProps> = ({
  commentId,
  position,
  onCancel,
}) => {
  const [replyText, setReplyText] = useState("");

  const handleReply = async () => {
    if (!replyText.trim()) return;

    try {
      // Get current user
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData?.user?.id;
      const userName = userData?.user?.email?.split("@")[0] || "Anonymous";

      if (!userId) {
        toast.error("You must be logged in to reply");
        return;
      }

      // Create a new comment that references the original one
      const { error } = await supabase.from("document_comments").insert({
        user_id: userId,
        user_name: userName,
        document_id: `reply-to-${commentId}`,
        content: replyText,
        position: position,
        reply_to: commentId,
      });

      if (error) {
        console.error("Error adding reply:", error);
        toast.error("Failed to add reply");
        return;
      }

      toast.success("Reply added");
      setReplyText("");
      onCancel();

      // In a real implementation, we would update the UI to show the new reply
      // For now, we'll just prompt the user to refresh
      setTimeout(() => window.location.reload(), 1500);
    } catch (error) {
      console.error("Error in handleReply:", error);
      toast.error("Failed to add reply");
    }
  };

  return (
    <div className="mt-2 flex gap-2">
      <Input
        placeholder="Write a reply..."
        value={replyText}
        onChange={(e) => setReplyText(e.target.value)}
        className="text-xs h-8"
        size={50}
      />
      <div className="flex gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={handleReply}
          className="h-8 text-xs"
        >
          Reply
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="h-8 w-8 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
};

export default CommentReplyForm;
