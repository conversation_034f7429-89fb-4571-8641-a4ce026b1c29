import React from "react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { calculateOptionPlanShares } from "../../utils/shareCalculations";

interface StockOptionPlanSectionProps {
  includeStockOptionPlan: boolean;
  stockOptionPlanPercentage: number;
  issuedShares: number;
  onToggleOptionPlan: (checked: boolean) => void;
  onUpdatePercentage: (percentage: number) => void;
}

const StockOptionPlanSection: React.FC<StockOptionPlanSectionProps> = ({
  includeStockOptionPlan,
  stockOptionPlanPercentage,
  issuedShares,
  onToggleOptionPlan,
  onUpdatePercentage,
}) => {
  return (
    <div className="border-t pt-6 mt-6">
      <div className="flex items-center space-x-2 mb-4">
        <Checkbox
          id="include-stock-option-plan"
          checked={includeStockOptionPlan}
          onCheckedChange={(checked) => {
            onToggleOptionPlan(checked === true);
          }}
        />
        <Label
          htmlFor="include-stock-option-plan"
          className="text-base font-medium cursor-pointer"
        >
          Include a stock option plan
        </Label>
      </div>

      {includeStockOptionPlan && (
        <div className="ml-6 space-y-2">
          <Label htmlFor="stock-option-plan-percentage">
            Stock Option Plan %
          </Label>
          <div className="relative w-32">
            <Input
              id="stock-option-plan-percentage"
              type="number"
              value={stockOptionPlanPercentage}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                if (!isNaN(value) && value >= 0 && value <= 100) {
                  onUpdatePercentage(value);
                }
              }}
              className="pr-7"
              step="0.01"
              min="0"
              max="100"
            />
            <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
              %
            </span>
          </div>

          <div className="mt-4 p-3 bg-gray-100 rounded-md">
            <div className="flex justify-between mb-2">
              <span>Stock Option Plan Shares:</span>
              <span>
                {calculateOptionPlanShares(
                  stockOptionPlanPercentage,
                  issuedShares
                ).toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between font-medium">
              <span>Total Shares:</span>
              <span>{issuedShares.toLocaleString()}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockOptionPlanSection;
