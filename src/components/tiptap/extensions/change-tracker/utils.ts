import { Editor } from "@tiptap/core";

// Utility functions for change tracking

/**
 * Get all commits from the track state
 */
export function getCommits(editor: Editor) {
  const trackState = editor.commands.getTrackState();
  return trackState ? [] : []; // Will be populated when getTrackState returns actual state
}

/**
 * Check if there are uncommitted changes
 */
export function hasUncommittedChanges(editor: Editor): boolean {
  return editor.commands.hasUncommittedChanges();
}

/**
 * Save document with optional message
 */
export function saveDocument(editor: Editor, message?: string): boolean {
  return editor.commands.saveDocument(message);
}

/**
 * Commit changes with a message
 */
export function commitChanges(editor: Editor, message: string): boolean {
  return editor.commands.commitChanges(message);
}

/**
 * Format timestamp for display
 */
export function formatTimestamp(date: Date): string {
  return date.toLocaleString();
}

/**
 * Generate a default commit message
 */
export function generateDefaultCommitMessage(): string {
  const now = new Date();
  return `Changes saved at ${now.toLocaleTimeString()}`;
}

/**
 * Extract meaningful content from commit steps to generate a descriptive title
 */
export function extractCommitTitle(commit: any): string {
  // If it's a custom message (not auto-generated), use it but truncate if too long
  if (
    !commit.message.includes("Saved at") &&
    !commit.message.includes("Changes saved")
  ) {
    return commit.message.length > 30
      ? commit.message.substring(0, 30) + "..."
      : commit.message;
  }

  // Try to extract meaningful content from the commit steps
  if (commit.steps && commit.steps.length > 0) {
    for (const step of commit.steps) {
      // Check for text insertion/replacement steps
      if (step.jsonID === "replace" && step.slice) {
        // Extract text content from the slice
        let textContent = "";
        if (step.slice.content && step.slice.content.content) {
          step.slice.content.content.forEach((node: any) => {
            if (node.text) {
              textContent += node.text;
            } else if (node.content) {
              // Handle nested content
              node.content.forEach((childNode: any) => {
                if (childNode.text) {
                  textContent += childNode.text;
                }
              });
            }
          });
        }

        if (textContent.trim()) {
          // Return first few words of the added text
          const words = textContent.trim().split(/\s+/).slice(0, 4);
          return words.length > 0
            ? `Added "${words.join(" ")}..."`
            : "Text changes";
        }
      }

      // Check for other step types
      if (step.jsonID === "addMark") {
        return "Formatting changes";
      }

      if (step.jsonID === "removeMark") {
        return "Removed formatting";
      }
    }
  }

  // Fallback for unrecognized changes
  return "Document changes";
}

/**
 * Add keyboard shortcut for saving (Ctrl+S)
 */
export function addSaveShortcut(editor: Editor) {
  const handleKeyDown = (event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === "s") {
      event.preventDefault();
      saveDocument(editor, generateDefaultCommitMessage());
    }
  };

  document.addEventListener("keydown", handleKeyDown);

  // Return cleanup function
  return () => {
    document.removeEventListener("keydown", handleKeyDown);
  };
}
