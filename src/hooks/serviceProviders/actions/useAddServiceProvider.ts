import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { toast } from "sonner";
import { ServiceProvider, PromisedGrant } from "@/types/serviceProvider";
import { validateShares } from "../utils";
import { serviceProviderService } from "@/services/serviceProviderService";
import { promisedGrantService } from "@/services/promisedGrantService";

export function useAddServiceProvider(
  serviceProviders: ServiceProvider[],
  setServiceProviders: React.Dispatch<React.SetStateAction<ServiceProvider[]>>,
  promisedGrants: PromisedGrant[],
  setPromisedGrants: React.Dispatch<React.SetStateAction<PromisedGrant[]>>,
  companyEquity: any,
  setCompanyEquity: React.Dispatch<React.SetStateAction<any>>,
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
) {
  const addServiceProvider = async (
    provider: Omit<ServiceProvider, "id" | "status" | "documents">
  ): Promise<boolean> => {
    setLoading(true);
    try {
      // Validate shares allocation
      if (provider.grantType !== "None" && provider.shares) {
        const isValidShares = validateShares(
          provider.shares,
          provider.grantType as "Option" | "Restricted Stock",
          companyEquity
        );

        if (!isValidShares) {
          if (provider.grantType === "Option") {
            toast(
              "Insufficient Option Shares - The number of shares exceeds the amount currently authorized under the stock option plan. Please refer to the 'Increase Stock Option Plan' option."
            );
          } else {
            toast(
              "Insufficient Authorized Shares - The number of shares exceeds the amount of shares currently authorized. Please refer to the 'Authorize Additional Shares' option."
            );
          }
          setLoading(false);
          return false;
        }
      }

      // Save to Supabase
      const savedProvider = await serviceProviderService.addServiceProvider({
        ...provider,
        id: uuidv4(),
        status: "Pending",
        documents: [],
      });

      if (!savedProvider) {
        setLoading(false);
        return false;
      }

      // Add to local state
      setServiceProviders((prev) => [...prev, savedProvider]);

      // If the provider has a grant, add a promised grant
      if (provider.grantType !== "None" && provider.shares) {
        const newGrant: Omit<PromisedGrant, "id"> = {
          serviceProviderId: savedProvider.id,
          serviceProviderName: provider.name,
          serviceProviderType: provider.type,
          vestingCommencementDate: provider.startDate,
          grantType: provider.grantType,
          optionType: provider.optionType,
          shares: provider.shares,
          vestingSchedule:
            provider.vestingSchedule === "Standard"
              ? provider.type === "Employee"
                ? "4 years with 1 year cliff"
                : "2 years monthly"
              : `Custom (${provider.vestingPeriod} months, ${provider.cliff} months cliff)`,
          status: "Promised",
        };

        // Save promised grant to Supabase
        const savedGrant =
          await promisedGrantService.savePromisedGrant(newGrant);

        if (savedGrant) {
          setPromisedGrants((prev) => [...prev, savedGrant]);
        }

        // Update company equity
        if (provider.grantType === "Option" && companyEquity.stockOptionPlan) {
          setCompanyEquity((prev) => ({
            ...prev,
            stockOptionPlan: {
              ...prev.stockOptionPlan!,
              allocatedShares:
                prev.stockOptionPlan!.allocatedShares + provider.shares!,
            },
          }));
        } else if (provider.grantType === "Restricted Stock") {
          setCompanyEquity((prev) => ({
            ...prev,
            issuedShares: prev.issuedShares + provider.shares!,
          }));
        }
      }

      toast(
        "Service Provider Added - " +
          provider.name +
          " has been added as a " +
          provider.type.toLowerCase() +
          "."
      );

      setLoading(false);
      return true;
    } catch (error) {
      console.error("Error adding service provider:", error);
      toast("Error - Failed to add service provider. Please try again.");
      setLoading(false);
      return false;
    }
  };

  return addServiceProvider;
}
