import React from "react";
import { QuestionnaireFormData } from "../../../types";
import { Card, CardContent } from "@/components/common/Card";
import { Check } from "lucide-react";

interface OtherInfoSectionProps {
  formData: QuestionnaireFormData;
}

const OtherInfoSection: React.FC<OtherInfoSectionProps> = ({ formData }) => {
  // Get registered agent name from the agent object
  const getAgentName = (
    agent: {
      id: string;
      name: string;
      rate?: string;
      description?: string;
      rateDescription?: string;
    } | null
  ) => {
    if (!agent) return "Not selected";
    return agent.name || "Unknown";
  };

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-3 flex items-center">
          <Check size={18} className="text-green-500 mr-2" />
          Other Information
        </h3>
        <div className="space-y-4">
          <div>
            <p className="text-sm text-gray-500">Company Technology</p>
            <p className="whitespace-pre-line">
              {formData.companyTech || "Not provided"}
            </p>
          </div>

          <div>
            <p className="text-sm text-gray-500">Registered Agent</p>
            <p>{getAgentName(formData.registeredAgent)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OtherInfoSection;
