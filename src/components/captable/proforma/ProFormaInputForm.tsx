import React from "react";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { UseFormReturn } from "react-hook-form";
import { ProFormaFormValues } from "./useProFormaForm";

interface ProFormaInputFormProps {
  form: UseFormReturn<ProFormaFormValues>;
  handleValuationChange: (
    field: keyof ProFormaFormValues,
    value: string
  ) => void;
  valuationMode: "pre-money" | "post-money";
  handleValuationModeChange: (mode: "pre-money" | "post-money") => void;
}

const ProFormaInputForm: React.FC<ProFormaInputFormProps> = ({
  form,
  handleValuationChange,
  valuationMode,
  handleValuationModeChange,
}) => {
  // Format value with commas
  const formatWithCommas = (value: string): string => {
    // Remove any existing commas or non-digit characters except for decimal point
    const cleanValue = value.replace(/,/g, "");
    // If it's empty or not a valid number, return as is
    if (cleanValue === "" || isNaN(Number(cleanValue))) {
      return cleanValue;
    }

    // Format the number with commas
    return Number(cleanValue).toLocaleString("en-US");
  };

  // Handle changes with comma formatting
  const handleInputChange = (
    field: keyof ProFormaFormValues,
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (...event: any[]) => void
  ) => {
    // Get cursor position before formatting
    const cursorPosition = e.target.selectionStart;
    const prevValue = e.target.value;

    // Format the value with commas
    const formattedValue = formatWithCommas(e.target.value);

    // Update the field value
    onChange(formattedValue);

    // Call the parent handler with the raw numeric value
    handleValuationChange(field, formattedValue);

    // Adjust cursor position after formatting
    if (cursorPosition !== null) {
      // Calculate new cursor position (accommodate added/removed commas)
      const addedCommas = (formattedValue.match(/,/g) || []).length;
      const prevCommas = (prevValue.match(/,/g) || []).length;
      const cursorOffset = addedCommas - prevCommas;

      setTimeout(() => {
        e.target.setSelectionRange(
          cursorPosition + cursorOffset,
          cursorPosition + cursorOffset
        );
      }, 0);
    }
  };

  return (
    <Form {...form}>
      <div className="space-y-4 mb-6">
        <div className="mb-4">
          <FormLabel className="mb-2 block">Valuation Mode</FormLabel>
          <ToggleGroup
            type="single"
            value={valuationMode}
            onValueChange={(value) => {
              if (value)
                handleValuationModeChange(value as "pre-money" | "post-money");
            }}
            className="w-full md:w-auto"
          >
            <ToggleGroupItem
              value="pre-money"
              className="flex-1 md:flex-initial"
            >
              Fixed Pre-Money
            </ToggleGroupItem>
            <ToggleGroupItem
              value="post-money"
              className="flex-1 md:flex-initial"
            >
              Fixed Post-Money
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <FormField
            control={form.control}
            name="investmentAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Investment Amount</FormLabel>
                <FormControl>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                      $
                    </span>
                    <Input
                      {...field}
                      className="pl-7"
                      onChange={(e) =>
                        handleInputChange("investmentAmount", e, field.onChange)
                      }
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="preMoneyValuation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Pre-Money Valuation</FormLabel>
                <FormControl>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                      $
                    </span>
                    <Input
                      {...field}
                      className="pl-7"
                      onChange={(e) =>
                        handleInputChange(
                          "preMoneyValuation",
                          e,
                          field.onChange
                        )
                      }
                      disabled={valuationMode === "post-money"}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="postMoneyValuation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Post-Money Valuation</FormLabel>
                <FormControl>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                      $
                    </span>
                    <Input
                      {...field}
                      className="pl-7"
                      onChange={(e) =>
                        handleInputChange(
                          "postMoneyValuation",
                          e,
                          field.onChange
                        )
                      }
                      disabled={valuationMode === "pre-money"}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="dilutedPoolPercentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Diluted Option Pool %</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      className="pr-7"
                      onChange={(e) => {
                        field.onChange(e);
                        handleValuationChange(
                          "dilutedPoolPercentage",
                          e.target.value
                        );
                      }}
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
                      %
                    </span>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </Form>
  );
};

export default ProFormaInputForm;
