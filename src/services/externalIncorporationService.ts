import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface ExternalIncorporation {
  id?: string;
  user_id?: string;
  company_name: string;
  company_address: any;
  authorized_shares: number;
  issued_shares: number;
  par_value_per_share: number;
  stock_option_plan: boolean;
  stock_option_plan_percentage?: number;
  registered_agent_name: string;
  registered_agent_address: string;
  registered_agent_email?: string;
  registered_agent_phone?: string;
  incorporation_date: string;
}

export interface ExternalStakeholder {
  id?: string;
  external_incorporation_id: string;
  name: string;
  is_director: boolean;
  officer_titles: string[];
  email?: string;
  address?: string;
  stock_ownership_percentage: number;
  shares_amount: number;
  has_vesting: boolean;
  vesting_schedule?: string;
  acceleration?: string;
}

export interface ExternalTechContribution {
  id?: string;
  external_incorporation_id: string;
  stakeholder_id: string;
  description: string;
}

export interface ExternalDocument {
  id?: string;
  external_incorporation_id: string;
  document_type: string;
  document_url: string;
  document_name: string;
  is_required: boolean;
}

export const externalIncorporationService = {
  async saveExternalIncorporation(
    incorporation: ExternalIncorporation
  ): Promise<ExternalIncorporation | null> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("You must be logged in to save company details");
        return null;
      }

      const { data, error } = await supabase
        .from("external_incorporations")
        .insert({
          ...incorporation,
          user_id: user.id,
        })
        .select();

      if (error) {
        console.error("Error saving external incorporation:", error);
        toast.error("Failed to save company information");
        return null;
      }

      return data[0] as ExternalIncorporation;
    } catch (error) {
      console.error("Error in saveExternalIncorporation:", error);
      toast.error("An error occurred while saving company information");
      return null;
    }
  },

  async saveStakeholder(
    stakeholder: ExternalStakeholder
  ): Promise<ExternalStakeholder | null> {
    try {
      const { data, error } = await supabase
        .from("external_stakeholders")
        .insert(stakeholder)
        .select();

      if (error) {
        console.error("Error saving stakeholder:", error);
        toast.error("Failed to save stakeholder information");
        return null;
      }

      return data[0] as ExternalStakeholder;
    } catch (error) {
      console.error("Error in saveStakeholder:", error);
      toast.error("An error occurred while saving stakeholder information");
      return null;
    }
  },

  async saveTechContribution(
    contribution: ExternalTechContribution
  ): Promise<ExternalTechContribution | null> {
    try {
      const { data, error } = await supabase
        .from("external_tech_contributions")
        .insert(contribution)
        .select();

      if (error) {
        console.error("Error saving tech contribution:", error);
        toast.error("Failed to save technology contribution");
        return null;
      }

      return data[0] as ExternalTechContribution;
    } catch (error) {
      console.error("Error in saveTechContribution:", error);
      toast.error("An error occurred while saving technology contribution");
      return null;
    }
  },

  async saveDocument(
    document: ExternalDocument
  ): Promise<ExternalDocument | null> {
    try {
      const { data, error } = await supabase
        .from("external_documents")
        .insert(document)
        .select();

      if (error) {
        console.error("Error saving document:", error);
        toast.error("Failed to save document information");
        return null;
      }

      return data[0] as ExternalDocument;
    } catch (error) {
      console.error("Error in saveDocument:", error);
      toast.error("An error occurred while saving document information");
      return null;
    }
  },

  async uploadDocument(
    file: File,
    userId: string,
    fileName: string
  ): Promise<string | null> {
    try {
      const filePath = `${userId}/${fileName}`;

      const { data, error } = await supabase.storage
        .from("external_incorporation_docs")
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: true,
        });

      if (error) {
        console.error("Error uploading document:", error);
        toast.error("Failed to upload document");
        return null;
      }

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage
        .from("external_incorporation_docs")
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (error) {
      console.error("Error in uploadDocument:", error);
      toast.error("An error occurred while uploading document");
      return null;
    }
  },

  async getExternalIncorporationForUser(): Promise<ExternalIncorporation | null> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return null;
      }

      const { data, error } = await supabase
        .from("external_incorporations")
        .select("*")
        .eq("user_id", user.id)
        .maybeSingle();

      if (error) {
        console.error("Error fetching external incorporation:", error);
        return null;
      }

      return data as ExternalIncorporation;
    } catch (error) {
      console.error("Error in getExternalIncorporationForUser:", error);
      return null;
    }
  },

  async getStakeholdersForIncorporation(
    incorporationId: string
  ): Promise<ExternalStakeholder[]> {
    try {
      const { data, error } = await supabase
        .from("external_stakeholders")
        .select("*")
        .eq("external_incorporation_id", incorporationId);

      if (error) {
        console.error("Error fetching stakeholders:", error);
        return [];
      }

      return data as ExternalStakeholder[];
    } catch (error) {
      console.error("Error in getStakeholdersForIncorporation:", error);
      return [];
    }
  },

  async markIncorporationAsCompleted(): Promise<boolean> {
    try {
      // Store in localStorage that the external incorporation was completed
      localStorage.setItem("externalIncorporationCompleted", "true");

      // In a real-world scenario, you might want to update a status field in the database
      return true;
    } catch (error) {
      console.error("Error marking incorporation as completed:", error);
      return false;
    }
  },
};
