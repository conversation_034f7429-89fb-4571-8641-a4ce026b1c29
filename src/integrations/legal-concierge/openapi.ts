const openapi = {
  openapi: "3.0.4",
  info: {
    title: "FoundersFormBackend",
    version: "1.0",
  },
  paths: {
    "/api/auth/send-invite": {
      post: {
        tags: ["Auth"],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/SendRegisterInviteRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/SendRegisterInviteRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/SendRegisterInviteRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/register": {
      post: {
        tags: ["Auth"],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/RegisterRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/RegisterRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/RegisterRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/login": {
      post: {
        tags: ["Auth"],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/LoginRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/LoginRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/LoginRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/verify-otp-get-token": {
      post: {
        tags: ["Auth"],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/VerifyOtpRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/VerifyOtpRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/VerifyOtpRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/select-company": {
      post: {
        tags: ["Auth"],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/SelectCompanyRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/SelectCompanyRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/SelectCompanyRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/companies": {
      get: {
        tags: ["Auth"],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/companies/{id}/collaborators": {
      get: {
        tags: ["Auth"],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/collaborators/{id}": {
      delete: {
        tags: ["Auth"],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            schema: {
              type: "string",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/me": {
      get: {
        tags: ["Auth"],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/refresh": {
      post: {
        tags: ["Auth"],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/logout": {
      post: {
        tags: ["Auth"],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/forgot-password": {
      post: {
        tags: ["Auth"],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/ForgotPasswordRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/ForgotPasswordRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/ForgotPasswordRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/auth/reset-password": {
      post: {
        tags: ["Auth"],
        requestBody: {
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/ResetPasswordRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/ResetPasswordRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/ResetPasswordRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/{id}": {
      get: {
        tags: ["Document"],
        summary: "Lists all the document of a company by id",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "Id of the company",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/generate/{id}": {
      post: {
        tags: ["Document"],
        summary: "Generates document for the company",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "Id of the company",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/documents/token": {
      post: {
        tags: ["Document"],
        summary:
          "Generating the document token for viewing in document server i.e. docs.foundersform.com",
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                type: "object",
                additionalProperties: {},
              },
            },
            "text/json": {
              schema: {
                type: "object",
                additionalProperties: {},
              },
            },
            "application/*+json": {
              schema: {
                type: "object",
                additionalProperties: {},
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/onlyoffice/callback": {
      post: {
        tags: ["Document"],
        summary: "Callback url to be put for the document server config",
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {},
            },
            "text/json": {
              schema: {},
            },
            "application/*+json": {
              schema: {},
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies": {
      get: {
        tags: ["Form"],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}": {
      get: {
        tags: ["Form"],
        summary: "Get company details",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/name": {
      put: {
        tags: ["Form"],
        summary: "Update company name [Section: Company Name]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/CompanyNameRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/CompanyNameRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/CompanyNameRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/address": {
      put: {
        tags: ["Form"],
        summary: "Update company address [Section: Company Address]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/CompanyAddressRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/CompanyAddressRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/CompanyAddressRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/authorizedshares": {
      put: {
        tags: ["Form"],
        summary: "Update authorized shares [Section: Authorized shares]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/AuthorizedSharesRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/AuthorizedSharesRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/AuthorizedSharesRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/officers": {
      post: {
        tags: ["Form"],
        summary: "Add officer for a company [Section: Director and Officers]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/OfficerRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/OfficerRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/OfficerRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/officers/{id}": {
      put: {
        tags: ["Form"],
        summary:
          "Update the officer info for a company [Section: Director and Officers]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/OfficerUpdateRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/OfficerUpdateRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/OfficerUpdateRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
      delete: {
        tags: ["Form"],
        summary:
          "Delete officer for a company [Section: Director and Officers]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/completeofficerssection": {
      post: {
        tags: ["Form"],
        summary:
          "Complete the officers section by posting below. [Section: Director and Officers]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/stockoption": {
      put: {
        tags: ["Form"],
        summary: "Update stock options [Section: Stock Plan]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/StockOptionPlanRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/StockOptionPlanRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/StockOptionPlanRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/vesting": {
      put: {
        tags: ["Form"],
        summary: "Update vesting info [Section: Vesting]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/VestingRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/VestingRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/VestingRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/officers/{id}/ips": {
      post: {
        tags: ["Form"],
        summary: "Add IP for an officer [Section: Technology]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/IntellectualPropertyRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/IntellectualPropertyRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/IntellectualPropertyRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/ips/{id}": {
      put: {
        tags: ["Form"],
        summary: "Update IP info for an officer [Section: Technology]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/IntellectualPropertyUpdateRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/IntellectualPropertyUpdateRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/IntellectualPropertyUpdateRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
      delete: {
        tags: ["Form"],
        summary: "Delete Ip info for an officer [Section: Technology]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/technology": {
      put: {
        tags: ["Form"],
        summary: "Update technology for a company [Section: Technology]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/TechnologyRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/TechnologyRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/TechnologyRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/agent": {
      get: {
        tags: ["Form"],
        summary: "Get all agents [Section: Registered Agent]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
      post: {
        tags: ["Form"],
        summary:
          "Update registered agent for a company [Section: Registered Agent]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        requestBody: {
          description: "",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/RegisteredAgentRequest",
              },
            },
            "text/json": {
              schema: {
                $ref: "#/components/schemas/RegisteredAgentRequest",
              },
            },
            "application/*+json": {
              schema: {
                $ref: "#/components/schemas/RegisteredAgentRequest",
              },
            },
          },
        },
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
    "/api/companies/{id}/comfirm": {
      put: {
        tags: ["Form"],
        summary: "Confirm form for questionairre [Section: Review]",
        parameters: [
          {
            name: "id",
            in: "path",
            description: "",
            required: true,
            schema: {
              type: "string",
              format: "uuid",
            },
          },
        ],
        responses: {
          "200": {
            description: "OK",
          },
        },
      },
    },
  },
  components: {
    schemas: {
      AuthorizedSharesRequest: {
        type: "object",
        properties: {
          numberOfAuthorizedShares: {
            type: "integer",
            format: "int64",
            nullable: true,
          },
          numberOfAuthorizedSharesToBeIssuedInitially: {
            type: "integer",
            format: "int64",
            nullable: true,
          },
          parValuePerShare: {
            type: "number",
            format: "double",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      CompanyAddressRequest: {
        type: "object",
        properties: {
          streetAddress: {
            type: "string",
            nullable: true,
          },
          streetAddress2: {
            type: "string",
            nullable: true,
          },
          city: {
            type: "string",
            nullable: true,
          },
          state: {
            type: "string",
            nullable: true,
          },
          zipCode: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      CompanyNameRequest: {
        type: "object",
        properties: {
          companyName: {
            type: "string",
            nullable: true,
          },
          isIncorporatedToday: {
            type: "boolean",
            nullable: true,
          },
          incorporationDate: {
            type: "string",
            format: "date",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      ForgotPasswordRequest: {
        type: "object",
        properties: {
          email: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      IntellectualPropertyRequest: {
        type: "object",
        properties: {
          name: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      IntellectualPropertyUpdateRequest: {
        type: "object",
        properties: {
          name: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      LoginRequest: {
        type: "object",
        properties: {
          email: {
            type: "string",
            nullable: true,
          },
          password: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      OfficerOwnershipRequest: {
        type: "object",
        properties: {
          officerId: {
            type: "string",
            format: "uuid",
            nullable: true,
          },
          ownershipPercentage: {
            type: "number",
            format: "double",
            nullable: true,
          },
          amountOfShares: {
            type: "integer",
            format: "int64",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      OfficerRequest: {
        type: "object",
        properties: {
          name: {
            type: "string",
            nullable: true,
          },
          emailAddress: {
            type: "string",
            nullable: true,
          },
          contactAddress: {
            type: "string",
            nullable: true,
          },
          stockOwnership: {
            type: "number",
            format: "double",
            nullable: true,
          },
          amountOfShares: {
            type: "integer",
            format: "int64",
            nullable: true,
          },
          isDirector: {
            type: "boolean",
            nullable: true,
          },
          isCEO: {
            type: "boolean",
          },
          isPresident: {
            type: "boolean",
          },
          isSecretary: {
            type: "boolean",
          },
          isTreasurer: {
            type: "boolean",
          },
          notApplicable: {
            type: "boolean",
          },
        },
        additionalProperties: false,
      },
      OfficerUpdateRequest: {
        type: "object",
        properties: {
          name: {
            type: "string",
            nullable: true,
          },
          emailAddress: {
            type: "string",
            nullable: true,
          },
          contactAddress: {
            type: "string",
            nullable: true,
          },
          stockOwnership: {
            type: "number",
            format: "double",
            nullable: true,
          },
          amountOfShares: {
            type: "integer",
            format: "int64",
            nullable: true,
          },
          isDirector: {
            type: "boolean",
            nullable: true,
          },
          isCEO: {
            type: "boolean",
          },
          isPresident: {
            type: "boolean",
          },
          isSecretary: {
            type: "boolean",
          },
          isTreasurer: {
            type: "boolean",
          },
          notApplicable: {
            type: "boolean",
          },
        },
        additionalProperties: false,
      },
      RegisterRequest: {
        type: "object",
        properties: {
          fullName: {
            type: "string",
            nullable: true,
          },
          email: {
            type: "string",
            nullable: true,
          },
          password: {
            type: "string",
            nullable: true,
          },
          phoneNumber: {
            type: "string",
            nullable: true,
          },
          token: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      RegisteredAgentRequest: {
        type: "object",
        properties: {
          agentId: {
            type: "string",
            format: "uuid",
          },
        },
        additionalProperties: false,
      },
      ResetPasswordRequest: {
        type: "object",
        properties: {
          email: {
            type: "string",
            nullable: true,
          },
          token: {
            type: "string",
            nullable: true,
          },
          newPassword: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      RoleConstants: {
        enum: ["ADMIN", "USER", "OWNER", "COLLABORATOR", "VIEWER"],
        type: "string",
      },
      SelectCompanyRequest: {
        type: "object",
        properties: {
          companyId: {
            type: "string",
            format: "uuid",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      SendRegisterInviteRequest: {
        type: "object",
        properties: {
          email: {
            type: "string",
            nullable: true,
          },
          role: {
            $ref: "#/components/schemas/RoleConstants",
          },
        },
        additionalProperties: false,
      },
      StockOptionPlanRequest: {
        type: "object",
        properties: {
          officersOwnership: {
            type: "array",
            items: {
              $ref: "#/components/schemas/OfficerOwnershipRequest",
            },
            nullable: true,
          },
          includeStockOptionPlan: {
            type: "boolean",
            nullable: true,
          },
          stockOptionPlanPercentage: {
            type: "number",
            format: "double",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      TechnologyRequest: {
        type: "object",
        properties: {
          technologyDescription: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      VerifyOtpRequest: {
        type: "object",
        properties: {
          otp: {
            type: "string",
            nullable: true,
          },
          email: {
            type: "string",
            nullable: true,
          },
        },
        additionalProperties: false,
      },
      VestingAcceleration: {
        enum: ["NONE", "SINGLETRIGGER", "DOUBLETRIGGER"],
        type: "string",
      },
      VestingRequest: {
        type: "object",
        properties: {
          vestingUnits: {
            type: "array",
            items: {
              $ref: "#/components/schemas/VestingUnit",
            },
            nullable: true,
          },
          isVestingConfirmed: {
            type: "boolean",
          },
        },
        additionalProperties: false,
      },
      VestingSchedule: {
        enum: ["FOURYEARSONEYEARCLIFF", "FOURYEARSNOCLIFF"],
        type: "string",
      },
      VestingUnit: {
        type: "object",
        properties: {
          officerId: {
            type: "string",
            format: "uuid",
          },
          isVester: {
            type: "boolean",
          },
          vestingSchedule: {
            $ref: "#/components/schemas/VestingSchedule",
          },
          acceleration: {
            $ref: "#/components/schemas/VestingAcceleration",
          },
        },
        additionalProperties: false,
      },
    },
  },
};
