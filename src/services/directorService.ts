import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Director {
  id?: string;
  company_id: string;
  user_id?: string;
  name: string;
  appointment_date: string;
  resigned: boolean;
  resignation_date?: string | null;
  stockholder_approval: boolean;
  created_at?: string;
  updated_at?: string;
}

export const directorService = {
  async getDirectorsForCompany(companyId: string): Promise<Director[]> {
    try {
      const { data, error } = await supabase
        .from("directors")
        .select("*")
        .eq("company_id", companyId);

      if (error) {
        console.error("Error fetching directors:", error);
        return [];
      }

      return data as Director[];
    } catch (error) {
      console.error("Error in getDirectors:", error);
      return [];
    }
  },

  async addDirector(director: Director): Promise<Director | null> {
    try {
      const userId = (await supabase.auth.getUser()).data.user?.id;

      if (!userId) {
        toast.error("You must be logged in to add a director");
        return null;
      }

      const { data, error } = await supabase
        .from("directors")
        .insert({ ...director, user_id: userId })
        .select();

      if (error) {
        console.error("Error adding director:", error);
        toast.error("Failed to add director");
        return null;
      }

      toast.success("Director added successfully");
      return data[0] as Director;
    } catch (error) {
      console.error("Error in addDirector:", error);
      toast.error("An error occurred while adding the director");
      return null;
    }
  },

  async updateDirector(
    id: string,
    updates: Partial<Director>
  ): Promise<Director | null> {
    try {
      const { data, error } = await supabase
        .from("directors")
        .update(updates)
        .eq("id", id)
        .select();

      if (error) {
        console.error("Error updating director:", error);
        toast.error("Failed to update director");
        return null;
      }

      toast.success("Director updated successfully");
      return data[0] as Director;
    } catch (error) {
      console.error("Error in updateDirector:", error);
      toast.error("An error occurred while updating the director");
      return null;
    }
  },

  async deleteDirector(id: string): Promise<boolean> {
    try {
      const { error } = await supabase.from("directors").delete().eq("id", id);

      if (error) {
        console.error("Error deleting director:", error);
        toast.error("Failed to delete director");
        return false;
      }

      toast.success("Director deleted successfully");
      return true;
    } catch (error) {
      console.error("Error in deleteDirector:", error);
      toast.error("An error occurred while deleting the director");
      return false;
    }
  },
};
