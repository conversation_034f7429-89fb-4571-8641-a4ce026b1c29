import React from "react";
import BaseServiceProviderDialog from "./BaseServiceProviderDialog";

interface ContractorDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContractorDialog: React.FC<ContractorDialogProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <BaseServiceProviderDialog
      isOpen={isOpen}
      onClose={onClose}
      type="Contractor"
      title="Onboard Independent Contractor/Consultant"
      description="Add a new contractor or consultant to your company and set up their equity grant."
      confirmTitle="Confirm Contractor Details"
      confirmDescription="Review the information before finalizing."
      successTitle="Contractor Onboarding Initiated"
      successDescription="The contractor has been added successfully and will receive onboarding documentation."
    />
  );
};

export default ContractorDialog;
