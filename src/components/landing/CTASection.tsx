import React from "react";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Button from "@/components/common/Button";
import { Card, CardContent } from "@/components/common/Card";

const CTASection: React.FC = () => {
  return (
    <section className="py-24 bg-white">
      <div className="container px-4 md:px-8">
        <div className="relative max-w-4xl mx-auto">
          <div className="absolute inset-0 bg-gradient-to-r from-legal-50 to-blue-50 rounded-2xl transform -rotate-1"></div>
          <div className="absolute inset-0 bg-white/50 backdrop-blur-sm rounded-2xl"></div>
          <Card className="border-legal-100 relative z-10">
            <CardContent className="p-8 md:p-12">
              <div className="text-center">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  Ready to form your company?
                </h2>
                <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                  Start your incorporation journey today and have your company
                  formed with professional legal documents in minutes.
                </p>
                <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                  <Button
                    size="lg"
                    className="group"
                    icon={
                      <ArrowRight
                        className="transition-transform group-hover:translate-x-1"
                        size={18}
                      />
                    }
                    iconPosition="right"
                  >
                    <Link to="/login">Sign In</Link>
                  </Button>
                  <Button size="lg" variant="outline">
                    <Link to="/login">Contact Sales</Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
