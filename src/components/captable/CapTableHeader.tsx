import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, PlusCircle } from "lucide-react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { CapTableSummary } from "@/types/capTable";

interface CapTableHeaderProps {
  onImportClick: () => void;
  onCreateNewClick: () => void;
}

const CapTableHeader: React.FC<CapTableHeaderProps> = ({
  onImportClick,
  onCreateNewClick,
}) => {
  return (
    <AnimatedTransition>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Cap Table</h1>
          <p className="text-gray-600 mt-1">
            View and manage your company's equity structure and ownership.
          </p>
        </div>

        <div className="flex gap-2 mt-4 md:mt-0">
          <Button
            variant="outline"
            onClick={onImportClick}
            className="flex items-center gap-2"
          >
            <FileText size={16} />
            Import Data
          </Button>
          <Button
            onClick={onCreateNewClick}
            className="flex items-center gap-2"
          >
            <PlusCircle size={16} />
            New Cap Table
          </Button>
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default CapTableHeader;
