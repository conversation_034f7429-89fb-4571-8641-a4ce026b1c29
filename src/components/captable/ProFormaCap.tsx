import React, { useMemo } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  CapTableSummary,
  ProFormaInvestment,
  Shareholder,
} from "@/types/capTable";

interface ProFormaCapProps {
  capTableData: CapTableSummary;
  proFormaInvestment?: ProFormaInvestment;
}

const ProFormaCap: React.FC<ProFormaCapProps> = ({
  capTableData,
  proFormaInvestment,
}) => {
  // Calculate pro forma cap table
  const proFormaCapTable = useMemo(() => {
    if (!proFormaInvestment) return null;

    // Calculate new shares to be issued
    const newShares =
      proFormaInvestment.amount / proFormaInvestment.pricePerShare;
    const newTotalShares = capTableData.totalShares + newShares;

    // Calculate new fully diluted shares
    const newTotalFullyDiluted = capTableData.totalFullyDiluted + newShares;

    // Update shareholder percentages
    const updatedShareholders = capTableData.shareholders.map((s) => {
      // Calculate new ownership percentages
      const newPercentage = (s.totalShares / newTotalShares) * 100;
      const newFullyDilutedPercentage =
        (s.totalShares / newTotalFullyDiluted) * 100;

      return {
        ...s,
        percentage: newPercentage,
        fullyDilutedPercentage: newFullyDilutedPercentage,
      };
    });

    // Add the new investor
    const newInvestor: Shareholder = {
      id: "new-investor",
      name: proFormaInvestment.name,
      role: "Investor",
      holdings: {
        [proFormaInvestment.shareClass]: newShares,
      },
      totalShares: newShares,
      percentage: (newShares / newTotalShares) * 100,
      fullyDilutedPercentage: (newShares / newTotalFullyDiluted) * 100,
    };

    return {
      totalShares: newTotalShares,
      totalFullyDiluted: newTotalFullyDiluted,
      shareholders: [...updatedShareholders, newInvestor],
      preMoneyValuation:
        capTableData.totalShares * proFormaInvestment.pricePerShare,
      postMoneyValuation: newTotalShares * proFormaInvestment.pricePerShare,
      newShares,
    };
  }, [capTableData, proFormaInvestment]);

  // Helper function to format numbers
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // Helper function to format currencies
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (!proFormaInvestment || !proFormaCapTable) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pro Forma Cap Table</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">
            No investment data available. Please add a new investment to see pro
            forma cap table.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Pro Forma Cap Table: {proFormaInvestment.name}</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="text-gray-400 hover:text-gray-600">
                  <Info size={16} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  This table shows how your cap table would look after the
                  proposed investment round.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">
              Pre-Money Valuation
            </div>
            <div className="text-2xl font-bold">
              {formatCurrency(proFormaCapTable.preMoneyValuation)}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Investment Amount</div>
            <div className="text-2xl font-bold">
              {formatCurrency(proFormaInvestment.amount)}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">
              Post-Money Valuation
            </div>
            <div className="text-2xl font-bold">
              {formatCurrency(proFormaCapTable.postMoneyValuation)}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">New Shares Issued</div>
            <div className="text-2xl font-bold">
              {formatNumber(proFormaCapTable.newShares)}
            </div>
          </div>
        </div>

        <Tabs defaultValue="standard" className="w-full">
          <TabsList className="grid grid-cols-2 w-48 mb-4">
            <TabsTrigger value="standard">Standard</TabsTrigger>
            <TabsTrigger value="fully-diluted">Fully Diluted</TabsTrigger>
          </TabsList>

          <TabsContent value="standard">
            <div className="rounded-md border overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Shareholder</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Total Shares</TableHead>
                    <TableHead>Ownership %</TableHead>
                    <TableHead>Current Ownership %</TableHead>
                    <TableHead>Change</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {proFormaCapTable.shareholders.map((shareholder) => {
                    // Find the original shareholder to compare
                    const originalShareholder = capTableData.shareholders.find(
                      (s) => s.id === shareholder.id
                    );
                    const originalPercentage = originalShareholder
                      ? originalShareholder.percentage
                      : 0;
                    const percentageChange =
                      shareholder.percentage - originalPercentage;

                    return (
                      <TableRow key={shareholder.id}>
                        <TableCell className="font-medium">
                          {shareholder.name}
                        </TableCell>
                        <TableCell>{shareholder.role}</TableCell>
                        <TableCell>
                          {formatNumber(shareholder.totalShares)}
                        </TableCell>
                        <TableCell>
                          {shareholder.percentage.toFixed(2)}%
                        </TableCell>
                        <TableCell>
                          {originalPercentage
                            ? originalPercentage.toFixed(2) + "%"
                            : "New"}
                        </TableCell>
                        <TableCell
                          className={
                            percentageChange < 0
                              ? "text-red-500"
                              : "text-green-500"
                          }
                        >
                          {percentageChange !== 0
                            ? (percentageChange > 0 ? "+" : "") +
                              percentageChange.toFixed(2) +
                              "%"
                            : "0%"}
                        </TableCell>
                      </TableRow>
                    );
                  })}

                  {/* Totals row */}
                  <TableRow className="bg-muted/50">
                    <TableCell colSpan={2} className="font-medium">
                      Total
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatNumber(proFormaCapTable.totalShares)}
                    </TableCell>
                    <TableCell className="font-medium">100.00%</TableCell>
                    <TableCell className="font-medium">100.00%</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="fully-diluted">
            <div className="rounded-md border overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Shareholder</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Total Shares</TableHead>
                    <TableHead>Fully Diluted %</TableHead>
                    <TableHead>Current Fully Diluted %</TableHead>
                    <TableHead>Change</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {proFormaCapTable.shareholders.map((shareholder) => {
                    // Find the original shareholder to compare
                    const originalShareholder = capTableData.shareholders.find(
                      (s) => s.id === shareholder.id
                    );
                    const originalPercentage = originalShareholder
                      ? originalShareholder.fullyDilutedPercentage
                      : 0;
                    const percentageChange =
                      shareholder.fullyDilutedPercentage - originalPercentage;

                    return (
                      <TableRow key={shareholder.id}>
                        <TableCell className="font-medium">
                          {shareholder.name}
                        </TableCell>
                        <TableCell>{shareholder.role}</TableCell>
                        <TableCell>
                          {formatNumber(shareholder.totalShares)}
                        </TableCell>
                        <TableCell>
                          {shareholder.fullyDilutedPercentage.toFixed(2)}%
                        </TableCell>
                        <TableCell>
                          {originalPercentage
                            ? originalPercentage.toFixed(2) + "%"
                            : "New"}
                        </TableCell>
                        <TableCell
                          className={
                            percentageChange < 0
                              ? "text-red-500"
                              : "text-green-500"
                          }
                        >
                          {percentageChange !== 0
                            ? (percentageChange > 0 ? "+" : "") +
                              percentageChange.toFixed(2) +
                              "%"
                            : "0%"}
                        </TableCell>
                      </TableRow>
                    );
                  })}

                  {/* Totals row */}
                  <TableRow className="bg-muted/50">
                    <TableCell colSpan={2} className="font-medium">
                      Total
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatNumber(proFormaCapTable.totalFullyDiluted)}
                    </TableCell>
                    <TableCell className="font-medium">100.00%</TableCell>
                    <TableCell className="font-medium">100.00%</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ProFormaCap;
