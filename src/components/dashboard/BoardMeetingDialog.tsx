import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";

import DateTimeSelector from "./board-meeting/DateTimeSelector";
import MeetingPurposeInput from "./board-meeting/MeetingPurposeInput";
import FileUploadSection from "./board-meeting/FileUploadSection";
import ScheduledMeetingDisplay from "./board-meeting/ScheduledMeetingDisplay";
import useBoardMeetingForm from "./board-meeting/useBoardMeetingForm";

interface BoardMeetingDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const BoardMeetingDialog: React.FC<BoardMeetingDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const {
    meetingDate,
    setMeetingDate,
    meetingTime,
    meetingTimezone,
    meetingPurpose,
    setMeetingPurpose,
    agendaFile,
    boardMaterials,
    scheduledMeeting,
    formErrors,
    handleTimeChange,
    handleTimezoneChange,
    handleAgendaFileChange,
    handleBoardMaterialsChange,
    handleScheduleMeeting,
    handleCancelMeeting,
  } = useBoardMeetingForm(isOpen);

  const onScheduleMeeting = () => {
    const scheduledDate = handleScheduleMeeting();

    if (scheduledDate) {
      // Show success toast
      toast({
        title: "Board Meeting Scheduled",
        description:
          "An invitation has been sent to the directors of the Company. A member of our team will be present to take notes of the meeting.",
      });

      // In a real application, here's where you'd send the calendar invite
      console.log("Sending calendar invite for:", {
        date: scheduledDate,
        timezone: meetingTimezone,
        purpose: meetingPurpose,
        agenda: agendaFile?.name,
        materials: boardMaterials.map((file) => file.name),
      });

      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle>Schedule Board Meeting</DialogTitle>
          <DialogDescription className="text-xs">
            Complete the form below to schedule a board meeting
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-4 py-2">
          {/* Currently Scheduled Meeting */}
          <ScheduledMeetingDisplay
            scheduledMeeting={scheduledMeeting}
            handleCancelMeeting={handleCancelMeeting}
          />

          {/* Meeting Date and Time */}
          <DateTimeSelector
            meetingDate={meetingDate}
            setMeetingDate={setMeetingDate}
            meetingTime={meetingTime}
            onTimeChange={handleTimeChange}
            meetingTimezone={meetingTimezone}
            onTimezoneChange={handleTimezoneChange}
            formErrors={formErrors}
          />

          {/* Meeting Purpose */}
          <MeetingPurposeInput
            meetingPurpose={meetingPurpose}
            setMeetingPurpose={setMeetingPurpose}
            formErrors={formErrors}
          />

          {/* File Uploads */}
          <FileUploadSection
            agendaFile={agendaFile}
            handleAgendaFileChange={handleAgendaFileChange}
            boardMaterials={boardMaterials}
            handleBoardMaterialsChange={handleBoardMaterialsChange}
            formErrors={formErrors}
          />
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2 pt-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full sm:w-auto h-9 text-sm"
            size="sm"
          >
            Cancel
          </Button>
          <Button
            onClick={onScheduleMeeting}
            className="w-full sm:w-auto h-9 text-sm"
            disabled={!meetingDate || !meetingPurpose || !agendaFile}
            size="sm"
          >
            Schedule Meeting
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BoardMeetingDialog;
