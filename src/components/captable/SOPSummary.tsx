import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { differenceInMonths } from "date-fns";
import {
  CapTableSummary,
  SOPSummary as SOPSummaryType,
  OptionGrant,
} from "@/types/capTable";
import { Info, Pencil, Save, X } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SOPSummaryProps {
  sopData?: SOPSummaryType;
  capTableData: CapTableSummary;
}

const SOPSummary: React.FC<SOPSummaryProps> = ({ sopData, capTableData }) => {
  const { toast } = useToast();
  const [grants, setGrants] = useState<OptionGrant[]>(sopData?.grants || []);

  if (!sopData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Stock Option Pool</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">
            No stock option pool data available. Set up your stock option pool
            to view this summary.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Calculate vesting percentage for each grant
  const calculateVestedPercentage = (grant: OptionGrant) => {
    const today = new Date();
    const monthsVested = differenceInMonths(today, grant.vestingStart);

    // Not started vesting yet
    if (monthsVested < 0) return 0;

    // Before cliff
    if (monthsVested < grant.cliff) return 0;

    // After vesting period
    if (monthsVested >= grant.vestingPeriod) return 100;

    // During vesting
    const percentageVested = (monthsVested / grant.vestingPeriod) * 100;
    return Math.round(percentageVested);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Toggle edit mode for a grant
  const toggleEditMode = (id: string) => {
    setGrants((prevGrants) =>
      prevGrants.map((grant) =>
        grant.id === id ? { ...grant, isEditing: !grant.isEditing } : grant
      )
    );
  };

  // Handle input change for editing
  const handleInputChange = (
    id: string,
    field: keyof OptionGrant,
    value: any
  ) => {
    setGrants((prevGrants) =>
      prevGrants.map((grant) =>
        grant.id === id ? { ...grant, [field]: value } : grant
      )
    );
  };

  // Save edited grant
  const saveGrant = (id: string) => {
    // In a real application, you would save this to your backend
    // For now, we'll just update the state and show a toast
    toggleEditMode(id);
    toast({
      title: "Grant updated",
      description: "The option grant has been successfully updated.",
    });
  };

  // Cancel editing
  const cancelEdit = (id: string) => {
    // Reset to original data from props
    setGrants(sopData.grants);
    toast({
      description: "Edit canceled",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Stock Option Pool</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="text-gray-400 hover:text-gray-600">
                  <Info size={16} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  Your stock option pool summary shows allocated options,
                  remaining pool, and vesting progress for each recipient.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Total Pool Size</div>
            <div className="text-2xl font-bold">
              {sopData.totalPool.toLocaleString()} shares
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {((sopData.totalPool / capTableData.totalShares) * 100).toFixed(
                2
              )}
              % of outstanding shares
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Allocated Options</div>
            <div className="text-2xl font-bold">
              {sopData.allocated.toLocaleString()} shares
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {((sopData.allocated / sopData.totalPool) * 100).toFixed(2)}% of
              pool allocated
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Remaining Pool</div>
            <div className="text-2xl font-bold">
              {sopData.remaining.toLocaleString()} shares
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {((sopData.remaining / sopData.totalPool) * 100).toFixed(2)}% of
              pool available
            </div>
          </div>
        </div>

        <div className="mb-6">
          <div className="mb-2 flex justify-between items-center">
            <span className="text-sm font-medium">Pool Allocation</span>
            <span className="text-sm text-gray-500">
              {((sopData.allocated / sopData.totalPool) * 100).toFixed(1)}% Used
            </span>
          </div>
          <Progress
            value={(sopData.allocated / sopData.totalPool) * 100}
            className="h-4"
          />
        </div>

        <div className="rounded-md border overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Recipient</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Grant Date</TableHead>
                <TableHead>Options</TableHead>
                <TableHead>% of Pool</TableHead>
                <TableHead>Current Value</TableHead>
                <TableHead>Vesting Progress</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {grants.map((grant) => {
                const percentOfPool =
                  (grant.optionsGranted / sopData.totalPool) * 100;
                const vestedPercentage = calculateVestedPercentage(grant);

                return (
                  <TableRow key={grant.id}>
                    <TableCell className="font-medium">
                      {grant.isEditing ? (
                        <Input
                          value={grant.recipient}
                          onChange={(e) =>
                            handleInputChange(
                              grant.id,
                              "recipient",
                              e.target.value
                            )
                          }
                          className="w-full"
                        />
                      ) : (
                        grant.recipient
                      )}
                    </TableCell>
                    <TableCell>
                      {grant.isEditing ? (
                        <Select
                          value={grant.role}
                          onValueChange={(value) =>
                            handleInputChange(grant.id, "role", value)
                          }
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Employee">Employee</SelectItem>
                            <SelectItem value="Advisor">Advisor</SelectItem>
                            <SelectItem value="Founder">Founder</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      ) : (
                        grant.role
                      )}
                    </TableCell>
                    <TableCell>
                      {grant.isEditing ? (
                        <Input
                          type="date"
                          value={grant.grantDate.toISOString().split("T")[0]}
                          onChange={(e) =>
                            handleInputChange(
                              grant.id,
                              "grantDate",
                              new Date(e.target.value)
                            )
                          }
                          className="w-full"
                        />
                      ) : (
                        grant.grantDate.toLocaleDateString()
                      )}
                    </TableCell>
                    <TableCell>
                      {grant.isEditing ? (
                        <Input
                          type="number"
                          value={grant.optionsGranted}
                          onChange={(e) =>
                            handleInputChange(
                              grant.id,
                              "optionsGranted",
                              parseInt(e.target.value)
                            )
                          }
                          className="w-full"
                        />
                      ) : (
                        grant.optionsGranted.toLocaleString()
                      )}
                    </TableCell>
                    <TableCell>{percentOfPool.toFixed(2)}%</TableCell>
                    <TableCell>
                      {grant.isEditing ? (
                        <Input
                          type="number"
                          value={grant.currentValue}
                          onChange={(e) =>
                            handleInputChange(
                              grant.id,
                              "currentValue",
                              parseInt(e.target.value)
                            )
                          }
                          className="w-full"
                        />
                      ) : (
                        formatCurrency(grant.currentValue)
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 md:w-40">
                          <Progress value={vestedPercentage} className="h-2" />
                        </div>
                        <span className="text-xs">{vestedPercentage}%</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {grant.isEditing ? (
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => saveGrant(grant.id)}
                            className="px-2 h-8"
                          >
                            <Save size={16} className="mr-1" />
                            <span className="sr-only md:not-sr-only md:inline-flex">
                              Save
                            </span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => cancelEdit(grant.id)}
                            className="px-2 h-8"
                          >
                            <X size={16} className="mr-1" />
                            <span className="sr-only md:not-sr-only md:inline-flex">
                              Cancel
                            </span>
                          </Button>
                        </div>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleEditMode(grant.id)}
                          className="px-2 h-8"
                        >
                          <Pencil size={16} className="mr-1" />
                          <span className="sr-only md:not-sr-only md:inline-flex">
                            Edit
                          </span>
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default SOPSummary;
