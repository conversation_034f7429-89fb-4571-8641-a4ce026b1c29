import React, { useState } from "react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { ConvertibleNote } from "@/types/financing";

// Mock data for demonstration
const mockCurrentNote: ConvertibleNote = {
  id: "1",
  dateAuthorized: new Date("2023-03-15"),
  maturityDate: new Date("2025-03-15"),
  authorizedAmount: 500000,
  outstandingPrincipal: 450000,
  interestRate: 5,
  interestType: "Simple Interest",
  valuationCap: 10000000,
  discount: 20,
  mfn: true,
  boardApproved: true,
};

const mockNewNoteTerms: ConvertibleNote = {
  ...mockCurrentNote,
  interestRate: 6, // Higher interest rate
  valuationCap: 8000000, // Lower valuation cap
  discount: 25, // Higher discount
};

const MfnConvertibleNotesSection: React.FC = () => {
  const { toast } = useToast();
  const [notifyDialogOpen, setNotifyDialogOpen] = useState(false);

  const handleNotifyHolders = () => {
    setNotifyDialogOpen(true);
  };

  const handleSendNotifications = () => {
    toast({
      title: "Notifications Sent",
      description:
        "Holders have been notified of their option to amend and restate their convertible notes.",
    });
    setNotifyDialogOpen(false);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">
            MFN for Convertible Notes
          </h3>
          <p className="text-sm text-muted-foreground">
            The Most Favored Nation (MFN) provision in your convertible notes
            may require you to offer updated terms to existing note holders when
            you issue new convertible instruments with more favorable terms.
          </p>
        </div>

        <div className="space-y-8">
          <div>
            <h4 className="text-md font-medium mb-3">
              Current Convertible Note Round
            </h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date Authorized</TableHead>
                  <TableHead>Maturity Date</TableHead>
                  <TableHead>Authorized Amount</TableHead>
                  <TableHead>Outstanding Principal</TableHead>
                  <TableHead>Interest Rate</TableHead>
                  <TableHead>Valuation Cap</TableHead>
                  <TableHead>Discount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>
                    {mockCurrentNote.dateAuthorized
                      ? format(mockCurrentNote.dateAuthorized, "PP")
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    {mockCurrentNote.maturityDate
                      ? format(mockCurrentNote.maturityDate, "PP")
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    ${mockCurrentNote.authorizedAmount.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    ${mockCurrentNote.outstandingPrincipal.toLocaleString()}
                  </TableCell>
                  <TableCell>{mockCurrentNote.interestRate}%</TableCell>
                  <TableCell>
                    ${mockCurrentNote.valuationCap.toLocaleString()}
                  </TableCell>
                  <TableCell>{mockCurrentNote.discount}%</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div>
            <h4 className="text-md font-medium mb-3">
              New Convertible Note Terms
            </h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date Authorized</TableHead>
                  <TableHead>Maturity Date</TableHead>
                  <TableHead>Authorized Amount</TableHead>
                  <TableHead>Outstanding Principal</TableHead>
                  <TableHead>Interest Rate</TableHead>
                  <TableHead>Valuation Cap</TableHead>
                  <TableHead>Discount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>
                    {mockNewNoteTerms.dateAuthorized
                      ? format(mockNewNoteTerms.dateAuthorized, "PP")
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    {mockNewNoteTerms.maturityDate
                      ? format(mockNewNoteTerms.maturityDate, "PP")
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    ${mockNewNoteTerms.authorizedAmount.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    ${mockNewNoteTerms.outstandingPrincipal.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {mockNewNoteTerms.interestRate}%
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      ${mockNewNoteTerms.valuationCap.toLocaleString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {mockNewNoteTerms.discount}%
                    </span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleNotifyHolders}>Notify Holders</Button>
          </div>
        </div>

        <Dialog open={notifyDialogOpen} onOpenChange={setNotifyDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Amended and Restated Convertible Note</DialogTitle>
              <DialogDescription>
                Review the amended and restated convertible note terms before
                notifying holders.
              </DialogDescription>
            </DialogHeader>

            <div className="h-60 border rounded-md p-4 overflow-y-auto bg-gray-50 my-4">
              <h3 className="text-md font-semibold mb-2">
                Amended and Restated Convertible Note
              </h3>
              <p className="text-sm">
                This document would contain the full text of the amended and
                restated convertible note with updated terms as shown in the
                "New Convertible Note Terms" table.
              </p>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setNotifyDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleSendNotifications}>
                Send Notifications
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default MfnConvertibleNotesSection;
