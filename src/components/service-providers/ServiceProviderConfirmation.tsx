import React from "react";
import { ServiceProviderType } from "@/types/serviceProvider";

interface ServiceProviderConfirmationProps {
  data: any;
  type: ServiceProviderType;
}

const ServiceProviderConfirmation: React.FC<
  ServiceProviderConfirmationProps
> = ({ data, type }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium">{type} Name</h3>
          <p className="text-sm">{data.name}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium">Email</h3>
          <p className="text-sm">{data.email}</p>
        </div>
        <div className="col-span-2">
          <h3 className="text-sm font-medium">Address</h3>
          <p className="text-sm">{data.address}</p>
        </div>
        {type !== "Employee" && data.services && (
          <div className="col-span-2">
            <h3 className="text-sm font-medium">Services</h3>
            <p className="text-sm">{data.services}</p>
          </div>
        )}
        <div>
          <h3 className="text-sm font-medium">Grant Type</h3>
          <p className="text-sm">{data.grantType}</p>
        </div>
        {data.grantType !== "None" && (
          <div>
            <h3 className="text-sm font-medium">Shares</h3>
            <p className="text-sm">{data.shares}</p>
          </div>
        )}
        <div>
          <h3 className="text-sm font-medium">Start Date</h3>
          <p className="text-sm">{data.startDate.toLocaleDateString()}</p>
        </div>
        {data.grantType !== "None" && (
          <div>
            <h3 className="text-sm font-medium">Vesting Schedule</h3>
            <p className="text-sm">
              {data.vestingSchedule === "Standard"
                ? type === "Employee"
                  ? "4 years with 1 year cliff"
                  : "2 years monthly"
                : `Custom (${data.vestingPeriod} months, ${data.cliff} months cliff)`}
            </p>
          </div>
        )}
        <div className="col-span-2">
          <h3 className="text-sm font-medium">Compensation</h3>
          <p className="text-sm">
            ${data.compensation}{" "}
            {type === "Employee"
              ? "annually"
              : data.compensationPeriod.toLowerCase()}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ServiceProviderConfirmation;
