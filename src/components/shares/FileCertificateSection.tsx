import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Scroll, ScrollBar } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";

interface FileCertificateSectionProps {
  currentShares: number;
  newTotalShares: number;
  onComplete: () => void;
  onCancel: () => void;
}

const FileCertificateSection: React.FC<FileCertificateSectionProps> = ({
  currentShares,
  newTotalShares,
  onComplete,
  onCancel,
}) => {
  const { toast } = useToast();

  const handleSubmitFiling = () => {
    // In a real app, this would send an email to the registered agent
    toast({
      title: "Email Sent to Registered Agent",
      description:
        "The certificate of amendment has been submitted for filing.",
    });
    onComplete();
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>File Certificate of Amendment</DialogTitle>
        <DialogDescription>
          Submit the Certificate of Amendment to your registered agent for
          filing
        </DialogDescription>
      </DialogHeader>

      <div className="my-4 space-y-4">
        <div className="border rounded-md p-4">
          <h3 className="font-semibold mb-2">Certificate of Amendment</h3>
          <Scroll className="h-40 overflow-auto">
            <div className="p-2 text-sm">
              <p>CERTIFICATE OF AMENDMENT TO THE</p>
              <p>CERTIFICATE OF INCORPORATION OF</p>
              <p>[COMPANY NAME], INC.</p>
              <br />
              <p>
                [COMPANY NAME], INC., a corporation organized and existing under
                and by virtue of the General Corporation Law of the State of
                Delaware (the "Company"), does hereby certify that:
              </p>
              <br />
              <p>
                1. The Board of Directors of the Company has duly adopted
                resolutions proposing to amend the Company's Certificate of
                Incorporation to increase the Company's authorized share capital
                from {currentShares.toLocaleString()} shares of Common Stock to{" "}
                {newTotalShares.toLocaleString()} shares of Common Stock.
              </p>
              <br />
              <p>
                2. This Certificate of Amendment has been duly approved by the
                required vote of stockholders in accordance with Section 242 of
                the Delaware General Corporation Law.
              </p>
              <br />
              <p>
                IN WITNESS WHEREOF, the Company has caused this Certificate of
                Amendment to be signed by a duly authorized officer of the
                Company on [DATE].
              </p>
            </div>
            <ScrollBar />
          </Scroll>
        </div>

        <div className="border rounded-md p-4 italic text-gray-600">
          <p>
            The following email will be sent to your registered agent with you
            CC'd:
          </p>
          <div className="bg-gray-50 p-3 mt-2 rounded-md">
            <p>Subject: Certificate of Amendment Filing - [COMPANY NAME]</p>
            <p className="mt-2">Dear Registered Agent,</p>
            <p className="mt-1">
              Please file the attached certificate of amendment to certificate
              of incorporation of [COMPANY NAME] with Delaware Secretary of
              State on a standard basis.
            </p>
            <p className="mt-1">Thank you.</p>
          </div>
        </div>

        <div className="italic text-gray-600 text-sm mt-4">
          The Certificate of Amendment will be submitted for filing. Please
          upload the certified evidence under Certificate of Amendment folder in
          your documents once received.
        </div>
      </div>

      <DialogFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmitFiling}>Submit Filing</Button>
      </DialogFooter>
    </>
  );
};

export default FileCertificateSection;
