import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";

interface ProFormaSummaryHeaderProps {
  title: string;
}

const ProFormaSummaryHeader: React.FC<ProFormaSummaryHeaderProps> = ({
  title,
}) => {
  return (
    <CardHeader>
      <CardTitle className="flex items-center justify-between">
        <span>Pro Forma Summary: {title}</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button className="text-gray-400 hover:text-gray-600">
                <Info size={16} />
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">
                Pro forma calculations show the effect of a potential investment
                on your company's valuation and equity distribution.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </CardTitle>
    </CardHeader>
  );
};

export default ProFormaSummaryHeader;
