import React, { useMemo } from "react";
import { CapTableSummary, ProFormaInvestment } from "@/types/capTable";

interface ProFormaCalculatorProps {
  capTableData: CapTableSummary;
  localProForma: ProFormaInvestment | undefined;
}

interface ProFormaMetrics {
  amount: number;
  preMoney: number;
  postMoney: number;
  newShares: number;
  newPercentage: number;
  dilutedPoolPercentage: number;
}

export const useProFormaCalculator = ({
  capTableData,
  localProForma,
}: ProFormaCalculatorProps): ProFormaMetrics => {
  return useMemo(() => {
    if (!localProForma) {
      return {
        amount: 0,
        preMoney: 0,
        postMoney: 0,
        newShares: 0,
        newPercentage: 0,
        dilutedPoolPercentage: 0,
      };
    }

    // Use amount from form or original
    const amount = localProForma.isAmountEdited
      ? localProForma.amount
      : localProForma.amount;

    // Use user-entered pre-money valuation or calculate based on shares
    const preMoney = localProForma.isUserEdited
      ? localProForma.preMoneyValuation
      : capTableData.totalShares * localProForma.pricePerShare;

    // Use user-entered post-money valuation or calculate
    const postMoney =
      localProForma.isUserEdited && localProForma.postMoneyValuation
        ? localProForma.postMoneyValuation
        : preMoney + amount;

    const newShares = amount / localProForma.pricePerShare;
    const newPercentage =
      (newShares / (capTableData.totalShares + newShares)) * 100;

    // Use user-entered diluted pool percentage or calculate
    const dilutedPoolPercentage =
      localProForma.dilutedPoolPercentage !== undefined
        ? localProForma.dilutedPoolPercentage
        : calculateDefaultDilutedPoolPercentage(capTableData, localProForma);

    return {
      amount,
      preMoney,
      postMoney,
      newShares,
      newPercentage,
      dilutedPoolPercentage,
    };
  }, [capTableData, localProForma]);
};

// Calculate default diluted pool percentage based on existing option pool
const calculateDefaultDilutedPoolPercentage = (
  capTableData: CapTableSummary,
  localProForma: ProFormaInvestment
): number => {
  const optionPool = capTableData.shareholders.find(
    (s) =>
      s.role === "Other" &&
      Object.keys(s.holdings).includes("Stock Option Pool")
  );

  const optionPoolShares = optionPool ? optionPool.totalShares : 0;

  return (
    (optionPoolShares /
      (capTableData.totalShares +
        localProForma.amount / localProForma.pricePerShare)) *
    100
  );
};

export default useProFormaCalculator;
