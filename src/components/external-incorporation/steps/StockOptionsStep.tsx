import React from "react";
import { ExternalIncorporationFormData } from "../types";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";

interface StockOptionsStepProps {
  formData: ExternalIncorporationFormData;
  updateFormData: (
    field: keyof ExternalIncorporationFormData,
    value: any
  ) => void;
}

const StockOptionsStep: React.FC<StockOptionsStepProps> = ({
  formData,
  updateFormData,
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Does your company have a Stock Option Plan? If so, please provide
          details.
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="has-stock-option-plan"
            checked={formData.hasStockOptionPlan}
            onCheckedChange={(checked) => {
              updateFormData("hasStockOptionPlan", checked === true);
            }}
          />
          <Label
            htmlFor="has-stock-option-plan"
            className="text-base font-medium cursor-pointer"
          >
            Yes, the company has a Stock Option Plan
          </Label>
        </div>

        {formData.hasStockOptionPlan && (
          <div className="ml-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="stock-option-plan-percentage">
                Stock Option Plan %
              </Label>
              <div className="relative w-32">
                <Input
                  id="stock-option-plan-percentage"
                  type="number"
                  value={formData.stockOptionPlanPercentage}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 100) {
                      updateFormData("stockOptionPlanPercentage", value);
                    }
                  }}
                  className="pr-7"
                  step="0.01"
                  min="0"
                  max="100"
                />
                <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">
                  %
                </span>
              </div>
              <p className="text-xs text-gray-500">
                The percentage of your company's equity reserved for the option
                pool.
              </p>
            </div>

            <div className="mt-4 p-3 bg-gray-100 rounded-md">
              <div className="flex justify-between mb-2">
                <span>Stock Option Plan Shares:</span>
                <span>
                  {Math.round(
                    (formData.stockOptionPlanPercentage / 100) *
                      formData.issuedShares
                  ).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockOptionsStep;
