import { useDirectorState } from "./directorState";
import { useDirectorActions } from "./directorActions";
import { useConsentGeneration } from "./consentGeneration";

export const useDirectorsManagement = () => {
  const {
    directors,
    setDirectors,
    boardSize,
    loading,
    setLoading,
    vacancies,
    companyId,
  } = useDirectorState();

  const { addDirector, updateDirector, uploadResignationLetter } =
    useDirectorActions(directors, setDirectors, setLoading, companyId);

  const { generateConsents } = useConsentGeneration(
    directors,
    setDirectors,
    setLoading
  );

  return {
    directors,
    boardSize,
    loading,
    vacancies,
    addDirector,
    updateDirector,
    uploadResignationLetter,
    generateConsents,
  };
};
