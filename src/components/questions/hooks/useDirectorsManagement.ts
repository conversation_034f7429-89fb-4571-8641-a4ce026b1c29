import { v4 as uuidv4 } from "uuid";
import { QuestionnaireFormData, OfficerTitle } from "../types";
import { calculateRemainingPercentage } from "../utils/shareCalculations";
import { Officer } from "../types/Officer";

export const useDirectorsManagement = (
  formData: QuestionnaireFormData,
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void
) => {
  // <PERSON><PERSON> adding a new director/officer/shareholder
  const handleAddDirector = () => {
    // Calculate the remaining percentage after accounting for existing directors and option plan
    let availablePercentage = calculateRemainingPercentage(formData.directors);

    if (formData.includeStockOptionPlan) {
      // If option plan is enabled, we need to account for its percentage
      availablePercentage =
        100 -
        formData.directors.reduce((sum, d) => sum + d.stockOwnership, 0) -
        formData.stockOptionPlanPercentage;
    }

    // Ensure availablePercentage is not negative
    availablePercentage = Math.max(0, availablePercentage);

    const newDirector: Officer = {
      id: uuidv4(),
      companyId: "",
      name: "",
      isDirector: false,
      isCEO: false,
      isPresident: false,
      isSecretary: false,
      isTreasurer: false,
      notApplicable: true,
      emailAddress: "",
      contactAddress: "",
      stockOwnership: availablePercentage,
      amountOfShares:
        availablePercentage > 0
          ? Math.floor((availablePercentage / 100) * formData.issuedShares)
          : 0,
      isVester: false,
      intellectualProperties: [],
      vestingSchedule: "",
      accleration: "",
      isNew: true, // Flag to indicate this is a new officer that needs to be created
    };

    updateFormData("directors", [...formData.directors, newDirector]);
  };

  // Handle removing a director/officer/shareholder
  const handleRemoveDirector = (id: string) => {
    const removedDirector = formData.directors.find((d) => d.id === id);
    const updatedDirectors = formData.directors.filter((d) => d.id !== id);

    if (removedDirector && updatedDirectors.length > 0) {
      // Redistribute the removed director's percentage proportionally among remaining directors
      const percentageToRedistribute = removedDirector.stockOwnership;

      if (percentageToRedistribute > 0) {
        const currentTotal = updatedDirectors.reduce(
          (sum, d) => sum + d.stockOwnership,
          0
        );

        // Distribute proportionally to existing percentages
        if (currentTotal > 0) {
          const updatedWithRedistribution = updatedDirectors.map((d) => {
            const proportion = d.stockOwnership / currentTotal;
            const newPercentage =
              d.stockOwnership + percentageToRedistribute * proportion;
            return {
              ...d,
              stockOwnership: Number(newPercentage.toFixed(2)),
            };
          });

          updateFormData("directors", updatedWithRedistribution);
          return;
        }
      }
    }

    // Default case if no redistribution happens
    updateFormData("directors", updatedDirectors);
  };

  // Update director field
  const updateDirectorField = (
    id: string,
    field: keyof Officer,
    value: any
  ) => {
    const updatedDirectors = formData.directors.map((director) => {
      if (director.id === id) {
        return {
          ...director,
          [field]: value,
        };
      }
      return director;
    });
    updateFormData("directors", updatedDirectors);
  };

  // Helper function to check if an exclusive title is already taken by another officer
  const isTitleTakenByOther = (
    currentId: string,
    title: OfficerTitle
  ): boolean => {
    // Not Applicable can be assigned to multiple officers
    if (title === "Not Applicable") return false;

    // Check if any other officer already has this exclusive title
    // CEO, President, Secretary, and Treasurer are exclusive (only one officer can have each)
    return formData.directors.some((director) => {
      if (director.id === currentId) return false; // Skip the current officer

      switch (title) {
        case "CEO":
          return director.isCEO;
        case "President":
          return director.isPresident;
        case "Secretary":
          return director.isSecretary;
        case "Treasurer":
          return director.isTreasurer;
        default:
          return false;
      }
    });
  };

  // Toggle officer title
  const toggleOfficerTitle = (id: string, title: OfficerTitle) => {
    const director = formData.directors.find((d) => d.id === id);
    if (!director) return;

    // Check if trying to assign an exclusive title that's already taken
    const currentlyHasTitle =
      (title === "CEO" && director.isCEO) ||
      (title === "President" && director.isPresident) ||
      (title === "Secretary" && director.isSecretary) ||
      (title === "Treasurer" && director.isTreasurer) ||
      (title === "Not Applicable" && director.notApplicable);

    // If trying to assign (not remove) an exclusive title that's already taken, prevent it
    if (!currentlyHasTitle && isTitleTakenByOther(id, title)) {
      console.warn(`Cannot assign ${title} - already taken by another officer`);
      return;
    }

    // Create a copy of the director with the updated values
    const updatedValues = { ...director };

    // Update the individual boolean flags based on the title
    if (title === "CEO") {
      updatedValues.isCEO = !director.isCEO;
      if (updatedValues.isCEO) {
        updatedValues.notApplicable = false;
      }
    } else if (title === "President") {
      updatedValues.isPresident = !director.isPresident;
      if (updatedValues.isPresident) {
        updatedValues.notApplicable = false;
      }
    } else if (title === "Secretary") {
      updatedValues.isSecretary = !director.isSecretary;
      if (updatedValues.isSecretary) {
        updatedValues.notApplicable = false;
      }
    } else if (title === "Treasurer") {
      updatedValues.isTreasurer = !director.isTreasurer;
      if (updatedValues.isTreasurer) {
        updatedValues.notApplicable = false;
      }
    } else if (title === "Not Applicable") {
      updatedValues.notApplicable = !director.notApplicable;
      if (updatedValues.notApplicable) {
        updatedValues.isCEO = false;
        updatedValues.isPresident = false;
        updatedValues.isSecretary = false;
        updatedValues.isTreasurer = false;
      }
    }

    // Only set notApplicable to true if all other titles are off and notApplicable is not already true
    if (
      !updatedValues.isCEO &&
      !updatedValues.isPresident &&
      !updatedValues.isSecretary &&
      !updatedValues.isTreasurer &&
      !updatedValues.notApplicable
    ) {
      updatedValues.notApplicable = true;
    }

    // Update the director with all changes at once
    const updatedDirectors = formData.directors.map((d) => {
      if (d.id === id) {
        return { ...d, ...updatedValues };
      }
      return d;
    });

    updateFormData("directors", updatedDirectors);
  };

  return {
    handleAddDirector,
    handleRemoveDirector,
    updateDirectorField,
    toggleOfficerTitle,
    isTitleTakenByOther,
  };
};
