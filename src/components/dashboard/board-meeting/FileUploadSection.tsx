import React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";

interface FileUploadSectionProps {
  agendaFile: File | null;
  handleAgendaFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  boardMaterials: File[];
  handleBoardMaterialsChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  formErrors: {
    agenda?: string;
  };
}

const FileUploadSection: React.FC<FileUploadSectionProps> = ({
  agendaFile,
  handleAgendaFileChange,
  boardMaterials,
  handleBoardMaterialsChange,
  formErrors,
}) => {
  const onAgendaFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleAgendaFileChange(e);
      toast({
        title: "Agenda Uploaded",
        description: `${e.target.files[0].name} has been uploaded successfully.`,
      });
    }
  };

  const onBoardMaterialsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleBoardMaterialsChange(e);
      const fileCount = e.target.files.length;
      toast({
        title: "Materials Uploaded",
        description: `${fileCount} ${fileCount === 1 ? "file" : "files"} uploaded successfully.`,
      });
    }
  };

  return (
    <>
      {/* Agenda Upload */}
      <div className="space-y-1.5">
        <Label htmlFor="agenda-upload" className="text-sm font-medium">
          Agenda
        </Label>
        <div className="grid grid-cols-1 gap-1">
          <div className="flex items-center gap-2">
            <Input
              id="agenda-upload"
              type="file"
              className={cn(
                "text-xs h-9 file:mr-2 file:py-1 file:px-2 file:rounded-full file:border-0 file:text-xs file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90",
                formErrors.agenda && "border-red-500"
              )}
              onChange={onAgendaFileChange}
            />
          </div>
          {agendaFile && (
            <p className="text-xs text-gray-500 truncate">{agendaFile.name}</p>
          )}
          <p className="text-xs italic text-gray-500">
            Please upload an agenda and overview of topics proposed to be
            discussed at the meeting.
          </p>
          {formErrors.agenda && (
            <p className="text-red-500 text-xs">{formErrors.agenda}</p>
          )}
        </div>
      </div>

      {/* Board Materials Upload */}
      <div className="space-y-1.5">
        <Label htmlFor="materials-upload" className="text-sm font-medium">
          Board Materials
        </Label>
        <div className="grid grid-cols-1 gap-1">
          <Input
            id="materials-upload"
            type="file"
            multiple
            className="text-xs h-9 file:mr-2 file:py-1 file:px-2 file:rounded-full file:border-0 file:text-xs file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
            onChange={onBoardMaterialsChange}
          />
          {boardMaterials.length > 0 && (
            <div>
              <p className="text-xs font-medium">Uploaded files:</p>
              <ul className="list-disc list-inside text-xs text-gray-600 max-h-[60px] overflow-y-auto">
                {boardMaterials.map((file, index) => (
                  <li key={index} className="truncate">
                    {file.name}
                  </li>
                ))}
              </ul>
            </div>
          )}
          <p className="text-xs italic text-gray-500">
            Please upload any materials to be viewed or considered for the board
            at the proposed meeting.
          </p>
        </div>
      </div>
    </>
  );
};

export default FileUploadSection;
