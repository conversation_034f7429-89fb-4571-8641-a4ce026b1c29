import React from "react";
import Layout from "@/components/Layout";
import { getServiceProviderItems } from "@/components/dashboard/maintenance/ServiceProviderItems";
import MaintenanceItem from "@/components/dashboard/MaintenanceItem";
import { useMaintenanceDialogs } from "@/components/dashboard/maintenance/useMaintenanceDialogs";
import MaintenanceDialogs from "@/components/dashboard/maintenance/MaintenanceDialogs";

const ServiceProviders: React.FC = () => {
  const {
    isAdvisorDialogOpen,
    setIsAdvisorDialogOpen,
    isContractorDialogOpen,
    setIsContractorDialogOpen,
    isEmployeeDialogOpen,
    setIsEmployeeDialogOpen,
    isTerminateDialogOpen,
    setIsTerminateDialogOpen,
    isPromisedGrantsDialogOpen,
    setIsPromisedGrantsDialogOpen,
  } = useMaintenanceDialogs();

  // Mock data for authorized shares - in a real app this would come from an API
  const currentAuthorizedShares = 10000000;

  const serviceProviderItems = getServiceProviderItems({
    onAdvisorClick: () => setIsAdvisorDialogOpen(true),
    onContractorClick: () => setIsContractorDialogOpen(true),
    onEmployeeClick: () => setIsEmployeeDialogOpen(true),
    onTerminateClick: () => setIsTerminateDialogOpen(true),
    onPromisedGrantsClick: () => setIsPromisedGrantsDialogOpen(true),
  });

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
          <h2 className="text-xl font-semibold mb-4">
            Service Provider Management
          </h2>
          <p className="text-gray-600 mb-6">
            Add, update, or terminate service providers for your company.
          </p>

          <div className="space-y-4">
            {serviceProviderItems.map((item, index) => (
              <MaintenanceItem
                key={index}
                icon={item.icon}
                title={item.title}
                description={item.description}
                buttonText={item.buttonText}
                onClick={item.onClick}
                linkTo={item.linkTo}
              />
            ))}
          </div>
        </div>

        <MaintenanceDialogs
          isAdvisorDialogOpen={isAdvisorDialogOpen}
          setIsAdvisorDialogOpen={setIsAdvisorDialogOpen}
          isContractorDialogOpen={isContractorDialogOpen}
          setIsContractorDialogOpen={setIsContractorDialogOpen}
          isEmployeeDialogOpen={isEmployeeDialogOpen}
          setIsEmployeeDialogOpen={setIsEmployeeDialogOpen}
          isTerminateDialogOpen={isTerminateDialogOpen}
          setIsTerminateDialogOpen={setIsTerminateDialogOpen}
          isPromisedGrantsDialogOpen={isPromisedGrantsDialogOpen}
          setIsPromisedGrantsDialogOpen={setIsPromisedGrantsDialogOpen}
          currentAuthorizedShares={currentAuthorizedShares}
          // These dialogs aren't used in this page but are required by the component
          isSharesDialogOpen={false}
          setIsSharesDialogOpen={() => {}}
          isOptionPlanDialogOpen={false}
          setIsOptionPlanDialogOpen={() => {}}
          isBoardMeetingDialogOpen={false}
          setIsBoardMeetingDialogOpen={() => {}}
        />
      </div>
    </Layout>
  );
};

export default ServiceProviders;
