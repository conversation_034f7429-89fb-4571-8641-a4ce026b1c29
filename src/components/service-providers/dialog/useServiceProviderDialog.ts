import { useState } from "react";
import { useServiceProviders } from "@/hooks/serviceProviders";
import { ServiceProviderType } from "@/types/serviceProvider";

export function useServiceProviderDialog(onClose: () => void) {
  const [formData, setFormData] = useState<any>(null);
  const [step, setStep] = useState<"form" | "confirmation" | "success">("form");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addServiceProvider, companyEquity } = useServiceProviders();

  const handleSubmit = (data: any) => {
    setFormData(data);
    setStep("confirmation");
  };

  const handleBack = () => {
    setStep("form");
  };

  const handleFinish = async (): Promise<boolean> => {
    setIsSubmitting(true);
    try {
      const success = await addServiceProvider({
        ...formData,
      });

      if (success) {
        setStep("success");
        return true;
      } else {
        setIsSubmitting(false);
        return false;
      }
    } catch (error) {
      console.error("Error adding service provider:", error);
      setIsSubmitting(false);
      return false;
    }
  };

  const resetAndClose = () => {
    setStep("form");
    setFormData(null);
    setIsSubmitting(false);
    onClose();
  };

  return {
    step,
    formData,
    isSubmitting,
    companyEquity,
    handleSubmit,
    handleFinish,
    handleBack,
    resetAndClose,
  };
}
