import React from "react";
import Layout from "@/components/Layout";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import AuthForm from "@/components/auth/AuthForm";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";

const Login: React.FC = () => {
  const { isAuthenticated, isMfaVerified, loading, user } = useAuth();
  const { isCompanySelected } = useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);
  const location = useLocation();
  if (loading) {
    return null; // Don't render anything while checking auth status
  }

  // If fully authenticated (with MFA), redirect to dashboard or the page they were trying to access
  if (isAuthenticated) {
    const from = permissions.isSigner ? "/signer-dashboard" : "/dashboard";
    return <Navigate to={from} replace />;
  }

  return (
    <Layout>
      <div className="min-h-[calc(100vh-16rem)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <AnimatedTransition className="w-full max-w-md">
          <div className="mb-6 text-center">
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome to Legal Concierge
            </h1>
            <p className="mt-2 text-gray-600">
              Sign in to access your dashboard
            </p>
          </div>
          <AuthForm />
        </AnimatedTransition>
      </div>
    </Layout>
  );
};

export default Login;
