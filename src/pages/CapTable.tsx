import React, { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import CapTableUploader from "@/components/captable/CapTableUploader";
import CapTableHeader from "@/components/captable/CapTableHeader";
import CapTableSelector from "@/components/captable/CapTableSelector";
import CapTableContent from "@/components/captable/CapTableContent";
import CapTableEmptyState from "@/components/captable/CapTableEmptyState";
import CapTableLoading from "@/components/captable/CapTableLoading";
import { useCapTableData } from "@/hooks/useCapTableData";
import { useCapTableFilters } from "@/hooks/useCapTableFilters";
import { useCreateCapTable } from "@/hooks/useCreateCapTable";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

const CapTablePage: React.FC = () => {
  const { toast } = useToast();
  const {
    loading,
    userData,
    currentCapTable,
    proFormaInvestment,
    convertibleNotes,
    sopSummary,
    proFormaVoting,
    saveCapTable,
    deleteCapTable,
    switchCapTable,
  } = useCapTableData();

  const {
    view,
    setView,
    setSearchTerm,
    setRoleFilter,
    setShareClassFilter,
    filteredShareholders,
  } = useCapTableFilters(currentCapTable);

  const { createCapTableFromCompanyDetails, handleCreateNewCapTable } =
    useCreateCapTable(saveCapTable);

  const [showUploader, setShowUploader] = useState(false);

  // Create a cap table from company details if needed
  useEffect(() => {
    if (userData.capTables.length === 0) {
      createCapTableFromCompanyDetails();
    }
  }, [userData.capTables]);

  const handleExport = (format: "pdf" | "csv") => {
    toast({
      title: `Exporting Cap Table as ${format.toUpperCase()}`,
      description:
        "This feature would download your cap table in the requested format.",
      duration: 3000,
    });
  };

  // Handle cap table upload
  const handleCapTableUploaded = async (capTable: any) => {
    await saveCapTable(capTable);
    setShowUploader(false);
  };

  if (loading) {
    return (
      <Layout>
        <div className="container py-8 px-4 md:px-8">
          <CapTableLoading />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <CapTableHeader
          onImportClick={() => setShowUploader(true)}
          onCreateNewClick={handleCreateNewCapTable}
        />

        {showUploader ? (
          <AnimatedTransition>
            <CapTableUploader onDataUploaded={handleCapTableUploaded} />
            <div className="mt-4 flex justify-end">
              <Button variant="outline" onClick={() => setShowUploader(false)}>
                Cancel
              </Button>
            </div>
          </AnimatedTransition>
        ) : (
          <>
            <CapTableSelector
              capTables={userData.capTables}
              currentTableId={currentCapTable?.id}
              onTableChange={switchCapTable}
            />

            {currentCapTable ? (
              <CapTableContent
                currentCapTable={currentCapTable}
                proFormaInvestment={proFormaInvestment}
                convertibleNotes={convertibleNotes}
                sopSummary={sopSummary}
                proFormaVoting={proFormaVoting}
                view={view}
                onSearch={setSearchTerm}
                onFilterRole={setRoleFilter}
                onFilterShareClass={setShareClassFilter}
                onToggleView={setView}
                onExport={handleExport}
                filteredShareholders={filteredShareholders}
              />
            ) : (
              <CapTableEmptyState
                onImportClick={() => setShowUploader(true)}
                onCreateNewClick={handleCreateNewCapTable}
              />
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default CapTablePage;
