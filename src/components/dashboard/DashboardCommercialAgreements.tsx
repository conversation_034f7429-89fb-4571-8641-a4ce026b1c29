import React from "react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import MaintenanceItem from "./MaintenanceItem";
import { Shield, FileText, FileCheck, Lock } from "lucide-react";

const DashboardCommercialAgreements: React.FC = () => {
  const commercialAgreementItems = [
    {
      icon: Shield,
      title: "Non-Disclosure Agreements",
      description:
        "Create and manage confidentiality agreements to protect your intellectual property.",
      buttonText: "Start",
      linkTo: "/agreements/nda",
    },
    {
      icon: FileText,
      title: "SaaS Agreement",
      description:
        "Generate software as a service agreements for your clients and partners.",
      buttonText: "Start",
      linkTo: "/agreements/saas",
    },
    {
      icon: FileCheck,
      title: "Terms of Use",
      description:
        "Create terms of use for your applications, websites, and services.",
      buttonText: "Start",
      linkTo: "/agreements/terms-of-use",
    },
    {
      icon: Lock,
      title: "Privacy Policy",
      description:
        "Generate comprehensive privacy policies compliant with relevant regulations.",
      buttonText: "Start",
      linkTo: "/agreements/privacy-policy",
    },
  ];

  return (
    <AnimatedTransition delay={0.5} className="mb-8">
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold mb-4">Commercial Agreements</h2>
        <p className="text-gray-600">
          Create and manage essential legal agreements for your business.
        </p>
        <div className="mt-6 space-y-4">
          {commercialAgreementItems.map((item, index) => (
            <MaintenanceItem
              key={index}
              icon={item.icon}
              title={item.title}
              description={item.description}
              buttonText={item.buttonText}
              linkTo={item.linkTo}
            />
          ))}
        </div>
      </div>
    </AnimatedTransition>
  );
};

export default DashboardCommercialAgreements;
