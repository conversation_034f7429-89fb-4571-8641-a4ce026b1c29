import React from "react";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CapTableGrid from "./CapTableGrid";
import ProFormaSummary from "./ProFormaSummary";
import ConvertibleTable from "./ConvertibleTable";
import SOPSummary from "./SOPSummary";
import ProFormaCap from "./ProFormaCap";
import ProFormaVoting from "./ProFormaVoting";
import {
  CapTableSummary,
  ProFormaInvestment,
  ConvertibleNote,
  SOPSummary as SOPSummaryType,
  ProFormaVoting as ProFormaVotingType,
} from "@/types/capTable";

interface CapTableTabsProps {
  capTableData: CapTableSummary;
  proFormaInvestment?: ProFormaInvestment;
  convertibleNotes?: ConvertibleNote[];
  sopSummary?: SOPSummaryType;
  proFormaVoting?: ProFormaVotingType;
  currentView: "standard" | "fullyDiluted";
}

const CapTableTabs: React.FC<CapTableTabsProps> = ({
  capTableData,
  proFormaInvestment,
  convertibleNotes,
  sopSummary,
  proFormaVoting,
  currentView,
}) => {
  return (
    <Tabs defaultValue="cap-table" className="w-full">
      <TabsList className="grid grid-cols-6">
        <TabsTrigger value="cap-table">Cap Table</TabsTrigger>
        <TabsTrigger value="pro-forma-summary">Pro Forma Summary</TabsTrigger>
        <TabsTrigger value="convertible">Convertible Notes</TabsTrigger>
        <TabsTrigger value="sop">Option Pool</TabsTrigger>
        <TabsTrigger value="pro-forma-cap">Pro Forma Cap</TabsTrigger>
        <TabsTrigger value="voting">Voting Rights</TabsTrigger>
      </TabsList>

      <TabsContent value="cap-table">
        <CapTableGrid
          shareholders={capTableData.shareholders}
          shareClasses={capTableData.shareClasses}
          currentView={currentView}
          totalShares={capTableData.totalShares}
          totalFullyDiluted={capTableData.totalFullyDiluted}
        />
      </TabsContent>

      <TabsContent value="pro-forma-summary">
        <ProFormaSummary
          capTableData={capTableData}
          proFormaInvestment={proFormaInvestment}
        />
      </TabsContent>

      <TabsContent value="convertible">
        <ConvertibleTable
          convertibleNotes={convertibleNotes || []}
          capTableData={capTableData}
        />
      </TabsContent>

      <TabsContent value="sop">
        <SOPSummary sopData={sopSummary} capTableData={capTableData} />
      </TabsContent>

      <TabsContent value="pro-forma-cap">
        <ProFormaCap
          capTableData={capTableData}
          proFormaInvestment={proFormaInvestment}
        />
      </TabsContent>

      <TabsContent value="voting">
        <ProFormaVoting
          votingData={proFormaVoting}
          capTableData={capTableData}
        />
      </TabsContent>
    </Tabs>
  );
};

export default CapTableTabs;
