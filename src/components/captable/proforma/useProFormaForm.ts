import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { CapTableSummary, ProFormaInvestment } from "@/types/capTable";

export interface ProFormaFormValues {
  investmentAmount: string;
  preMoneyValuation: string;
  postMoneyValuation: string;
  dilutedPoolPercentage: string;
  valuationMode: "pre-money" | "post-money";
}

export const useProFormaForm = (
  capTableData: CapTableSummary,
  initialProForma?: ProFormaInvestment
) => {
  // Use local state only, never persist changes
  const [localProForma, setLocalProForma] = useState<
    ProFormaInvestment | undefined
  >(initialProForma ? { ...initialProForma } : undefined);
  const [valuationMode, setValuationMode] = useState<
    "pre-money" | "post-money"
  >("pre-money");

  const form = useForm<ProFormaFormValues>({
    defaultValues: {
      investmentAmount: initialProForma?.amount
        ? initialProForma.amount.toLocaleString()
        : "0",
      preMoneyValuation: initialProForma?.preMoneyValuation
        ? initialProForma.preMoneyValuation.toLocaleString()
        : "0",
      postMoneyValuation: initialProForma?.postMoneyValuation
        ? initialProForma.postMoneyValuation.toLocaleString()
        : (
            (initialProForma?.preMoneyValuation || 0) +
            (initialProForma?.amount || 0)
          ).toLocaleString(),
      dilutedPoolPercentage:
        initialProForma?.dilutedPoolPercentage?.toString() || "0",
      valuationMode: "pre-money",
    },
  });

  // Update form values when initialProForma changes, but create a deep copy to prevent mutation
  useEffect(() => {
    if (initialProForma) {
      // Create a new copy to ensure we don't mutate the original
      setLocalProForma({ ...initialProForma });
      form.reset({
        investmentAmount: initialProForma.amount?.toLocaleString() || "0",
        preMoneyValuation:
          initialProForma.preMoneyValuation?.toLocaleString() || "0",
        postMoneyValuation:
          initialProForma.postMoneyValuation?.toLocaleString() ||
          (
            initialProForma.preMoneyValuation + initialProForma.amount || 0
          ).toLocaleString(),
        dilutedPoolPercentage:
          initialProForma.dilutedPoolPercentage?.toString() || "0",
        valuationMode: valuationMode,
      });
    }
  }, [initialProForma, form, valuationMode]);

  // Handle valuation mode change - simulation only
  const handleValuationModeChange = (mode: "pre-money" | "post-money") => {
    setValuationMode(mode);
    form.setValue("valuationMode", mode);

    // Recalculate values based on new mode
    if (localProForma) {
      const investmentAmount =
        parseFloat(form.getValues("investmentAmount").replace(/,/g, "")) || 0;

      if (mode === "pre-money") {
        const preMoneyVal =
          parseFloat(form.getValues("preMoneyValuation").replace(/,/g, "")) ||
          0;
        const newPostMoney = preMoneyVal + investmentAmount;
        form.setValue("postMoneyValuation", newPostMoney.toLocaleString());
      } else {
        const postMoneyVal =
          parseFloat(form.getValues("postMoneyValuation").replace(/,/g, "")) ||
          0;
        const newPreMoney = Math.max(0, postMoneyVal - investmentAmount);
        form.setValue("preMoneyValuation", newPreMoney.toLocaleString());
      }
    }
  };

  // Handle form field changes - simulation only
  const handleValuationChange = (
    field: keyof ProFormaFormValues,
    value: string
  ) => {
    const numValue = value === "" ? 0 : parseFloat(value.replace(/,/g, ""));

    if (isNaN(numValue)) return;

    // Create a new copy of the local pro forma to avoid modifying the original
    const updatedProForma = {
      ...(localProForma || {}),
      isUserEdited: true,
    } as ProFormaInvestment;

    if (field === "investmentAmount") {
      updatedProForma.amount = numValue;
      updatedProForma.isAmountEdited = true;

      // Update valuation based on mode
      if (valuationMode === "pre-money") {
        const preMoneyVal =
          parseFloat(form.getValues("preMoneyValuation").replace(/,/g, "")) ||
          0;
        const newPostMoney = preMoneyVal + numValue;
        form.setValue("postMoneyValuation", newPostMoney.toLocaleString());
        updatedProForma.postMoneyValuation = newPostMoney;
      } else {
        const postMoneyVal =
          parseFloat(form.getValues("postMoneyValuation").replace(/,/g, "")) ||
          0;
        const newPreMoney = Math.max(0, postMoneyVal - numValue);
        form.setValue("preMoneyValuation", newPreMoney.toLocaleString());
        updatedProForma.preMoneyValuation = newPreMoney;
      }
    } else if (field === "preMoneyValuation") {
      updatedProForma.preMoneyValuation = numValue;

      // If in pre-money mode, update post-money
      if (valuationMode === "pre-money") {
        const investmentAmount =
          parseFloat(form.getValues("investmentAmount").replace(/,/g, "")) || 0;
        const newPostMoney = numValue + investmentAmount;
        form.setValue("postMoneyValuation", newPostMoney.toLocaleString());
        updatedProForma.postMoneyValuation = newPostMoney;
      }
    } else if (field === "postMoneyValuation") {
      updatedProForma.postMoneyValuation = numValue;

      // If in post-money mode, update pre-money
      if (valuationMode === "post-money") {
        const investmentAmount =
          parseFloat(form.getValues("investmentAmount").replace(/,/g, "")) || 0;
        const newPreMoney = Math.max(0, numValue - investmentAmount);
        form.setValue("preMoneyValuation", newPreMoney.toLocaleString());
        updatedProForma.preMoneyValuation = newPreMoney;
      }
    } else if (field === "dilutedPoolPercentage") {
      updatedProForma.dilutedPoolPercentage = numValue;
    }

    // Update local state only, never persist to backend or global state
    setLocalProForma(updatedProForma);
  };

  return {
    form,
    localProForma,
    valuationMode,
    handleValuationChange,
    handleValuationModeChange,
  };
};
