import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { useServiceProviders } from "@/hooks/serviceProviders";
import { ServiceProvider } from "@/types/serviceProvider";

interface TerminateDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const TerminateDialog: React.FC<TerminateDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { serviceProviders, terminateServiceProvider } = useServiceProviders();
  const [selectedProviderId, setSelectedProviderId] = useState<string>("");
  const [reason, setReason] = useState<string>("");
  const [step, setStep] = useState<"select" | "confirm">("select");

  const activeProviders = serviceProviders.filter(
    (p) => p.status !== "Terminated"
  );

  const selectedProvider = serviceProviders.find(
    (p) => p.id === selectedProviderId
  );

  const handleTerminate = () => {
    if (selectedProviderId) {
      terminateServiceProvider(selectedProviderId);
      resetAndClose();
    }
  };

  const resetAndClose = () => {
    setSelectedProviderId("");
    setReason("");
    setStep("select");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && resetAndClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {step === "select"
              ? "Terminate Service Provider"
              : "Confirm Termination"}
          </DialogTitle>
          <DialogDescription>
            {step === "select"
              ? "Select a service provider to terminate."
              : "Please review and confirm termination details."}
          </DialogDescription>
        </DialogHeader>

        {step === "select" ? (
          <>
            {activeProviders.length > 0 ? (
              <>
                <div className="py-4">
                  <Label className="text-base">Select Service Provider</Label>
                  <RadioGroup
                    value={selectedProviderId}
                    onValueChange={setSelectedProviderId}
                    className="mt-2 space-y-2"
                  >
                    {activeProviders.map((provider) => (
                      <div
                        key={provider.id}
                        className="flex items-center space-x-2 border p-3 rounded-md"
                      >
                        <RadioGroupItem value={provider.id} id={provider.id} />
                        <Label htmlFor={provider.id} className="flex-1">
                          <div className="font-medium">{provider.name}</div>
                          <div className="text-sm text-gray-500">
                            {provider.type} • {provider.email}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                <div className="py-2">
                  <Label htmlFor="reason">
                    Reason for Termination (Optional)
                  </Label>
                  <Textarea
                    id="reason"
                    placeholder="Enter reason for termination"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="mt-1"
                  />
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={resetAndClose}>
                    Cancel
                  </Button>
                  <Button
                    onClick={() => selectedProviderId && setStep("confirm")}
                    disabled={!selectedProviderId}
                  >
                    Continue
                  </Button>
                </DialogFooter>
              </>
            ) : (
              <div className="py-4 text-center">
                <p className="text-gray-500">
                  No active service providers found.
                </p>
                <Button
                  variant="outline"
                  onClick={resetAndClose}
                  className="mt-4"
                >
                  Close
                </Button>
              </div>
            )}
          </>
        ) : (
          <>
            {selectedProvider && (
              <div className="space-y-4 py-2">
                <div>
                  <h3 className="text-sm font-medium">
                    You are about to terminate:
                  </h3>
                  <div className="mt-2 p-3 border rounded-md">
                    <p className="font-medium">{selectedProvider.name}</p>
                    <p className="text-sm text-gray-500">
                      {selectedProvider.type}
                    </p>
                    <p className="text-sm text-gray-500">
                      {selectedProvider.email}
                    </p>
                  </div>
                </div>

                {reason && (
                  <div>
                    <h3 className="text-sm font-medium">
                      Reason for Termination:
                    </h3>
                    <p className="text-sm mt-1">{reason}</p>
                  </div>
                )}

                <div className="text-sm text-amber-600 border-l-4 border-amber-500 pl-3 py-2 bg-amber-50 rounded-r-md">
                  <p className="font-medium">Important</p>
                  <p>
                    This action will terminate the service provider's
                    relationship with your company. It cannot be undone.
                  </p>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setStep("select")}>
                Back
              </Button>
              <Button variant="destructive" onClick={handleTerminate}>
                Terminate
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TerminateDialog;
