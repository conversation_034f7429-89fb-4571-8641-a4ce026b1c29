import { useMemo } from "react";
import { FormStep } from "../types";
import { FORM_STEPS } from "../constants/formSteps";

export const useFormProgress = (currentStep: FormStep) => {
  // Calculate progress percentage based on current step
  const progressPercentage = useMemo(() => {
    if (!currentStep) {
      return 0;
    }

    const currentStepIndex = FORM_STEPS.findIndex(
      (step) => step.id === currentStep
    );

    if (currentStepIndex === -1) {
      return 0;
    }

    return Math.round((currentStepIndex / (FORM_STEPS.length - 1)) * 100);
  }, [currentStep]);

  return { progressPercentage };
};
