import React from "react";
import { WizardStep } from "./types";

interface ProgressStepsProps {
  currentStep: WizardStep;
  steps: { id: WizardStep; label: string }[];
}

const ProgressSteps: React.FC<ProgressStepsProps> = ({
  currentStep,
  steps,
}) => {
  return (
    <div className="flex items-center space-x-4 mb-2">
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === step.id
                  ? "bg-legal-500 text-white"
                  : steps.findIndex((s) => s.id === currentStep) > index
                    ? "bg-legal-200 text-legal-700"
                    : "bg-gray-100 text-gray-500"
              }`}
            >
              {index + 1}
            </div>
            <span className="ml-2 text-sm font-medium hidden sm:inline">
              {step.label}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div className="flex-grow h-0.5 bg-gray-200">
              <div
                className="h-full bg-legal-500 transition-all duration-300"
                style={{
                  width:
                    currentStep === step.id
                      ? "0%"
                      : steps.findIndex((s) => s.id === currentStep) > index
                        ? "100%"
                        : "0%",
                }}
              ></div>
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default ProgressSteps;
