import React from "react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  VestingSchedule,
  AccelerationType,
  QuestionnaireFormData,
} from "../../types";
import { Info, Lock, Calendar, Clock } from "lucide-react";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface VestingTableProps {
  formData: QuestionnaireFormData;
  updateVestingField: (id: string, field: string, value: any) => void;
  calculateShares: (percentage: number) => number;
}

const VestingTable: React.FC<VestingTableProps> = ({
  formData,
  updateVestingField,
  calculateShares,
}) => {
  return (
    <div className="w-full overflow-auto">
      <Table className="min-w-full">
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Director</TableHead>
            <TableHead>Officer Title</TableHead>
            <TableHead>Amount of Shares</TableHead>
            <TableHead>
              <div className="flex items-center">
                Vesting
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button className="ml-1 inline-flex items-center text-gray-500 hover:text-gray-700">
                        <Lock size={14} />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs p-2">
                      <p className="text-sm">
                        Enabling vesting will make shares vest over time
                        according to the selected schedule.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center">
                Vesting Schedule
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button className="ml-1 inline-flex items-center text-gray-500 hover:text-gray-700">
                        <Calendar size={14} />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs p-2">
                      <p className="text-sm">
                        <strong>Standard schedule</strong>: 4-year vesting with
                        a 1-year cliff (25% vests after 1 year, then monthly).
                        <br />
                        <br />
                        <strong>Monthly schedule</strong>: 4-year vesting with
                        no cliff (shares vest monthly from the start).
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center">
                Acceleration
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button className="ml-1 inline-flex items-center text-gray-500 hover:text-gray-700">
                        <Clock size={14} />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs p-2">
                      <p className="text-sm">
                        <strong>Single Trigger</strong>: All shares vest
                        immediately upon company acquisition.
                        <br />
                        <br />
                        <strong>Double Trigger</strong>: Shares vest upon
                        acquisition AND if the founder is terminated without
                        cause.
                        <br />
                        <br />
                        <strong>None</strong>: No acceleration of vesting upon
                        acquisition.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {formData.directors.map((director) => {
            // First try to get from vestingInfo array, then fallback to embedded data in director
            const vestingInfo = formData.vestingInfo.find(
              (v) => v.id === director.id
            ) || {
              id: director.id,
              isVester: director.isVester || false,
              vestingSchedule: director.vestingSchedule || null,
              acceleration: director.acceleration || null,
            };

            return (
              <TableRow key={director.id}>
                <TableCell>{director.name || "Unnamed"}</TableCell>
                <TableCell>{director.isDirector ? "Yes" : "No"}</TableCell>
                <TableCell>{director.officerTitles?.join(", ")}</TableCell>
                <TableCell>
                  {calculateShares(director.stockOwnership).toLocaleString()}
                </TableCell>
                <TableCell>
                  <Checkbox
                    key={`vesting-${director.id}`}
                    id={`vesting-${director.id}`}
                    checked={vestingInfo.isVester}
                    onCheckedChange={(checked) => {
                      console.log(
                        `Checkbox clicked, current value: ${vestingInfo.isVester}, new value: ${checked}`
                      );

                      // Convert the checked value to a boolean
                      const isChecked = checked === true;

                      // The updateVestingField function now handles setting default values
                      // for vestingSchedule and acceleration when isVester is set to true
                      updateVestingField(director.id, "isVester", isChecked);
                    }}
                  />
                </TableCell>
                <TableCell>
                  {vestingInfo.isVester ? (
                    <RadioGroup
                      value={vestingInfo.vestingSchedule || ""}
                      onValueChange={(value) =>
                        updateVestingField(
                          director.id,
                          "vestingSchedule",
                          value
                        )
                      }
                      className="space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="Standard"
                          id={`schedule-standard-${director.id}`}
                        />
                        <Label
                          htmlFor={`schedule-standard-${director.id}`}
                          className="cursor-pointer"
                        >
                          4 years, 1 year cliff
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="Monthly"
                          id={`schedule-monthly-${director.id}`}
                        />
                        <Label
                          htmlFor={`schedule-monthly-${director.id}`}
                          className="cursor-pointer"
                        >
                          4 years, no cliff
                        </Label>
                      </div>
                    </RadioGroup>
                  ) : (
                    <span className="text-gray-400">N/A</span>
                  )}
                </TableCell>
                <TableCell>
                  {vestingInfo.isVester ? (
                    <RadioGroup
                      value={vestingInfo.acceleration || ""}
                      onValueChange={(value) =>
                        updateVestingField(director.id, "acceleration", value)
                      }
                      className="space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="SingleTrigger"
                          id={`accel-single-${director.id}`}
                        />
                        <Label
                          htmlFor={`accel-single-${director.id}`}
                          className="cursor-pointer"
                        >
                          Single Trigger
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="DoubleTrigger"
                          id={`accel-double-${director.id}`}
                        />
                        <Label
                          htmlFor={`accel-double-${director.id}`}
                          className="cursor-pointer"
                        >
                          Double Trigger
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="None"
                          id={`accel-none-${director.id}`}
                        />
                        <Label
                          htmlFor={`accel-none-${director.id}`}
                          className="cursor-pointer"
                        >
                          None
                        </Label>
                      </div>
                    </RadioGroup>
                  ) : (
                    <span className="text-gray-400">N/A</span>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default VestingTable;
