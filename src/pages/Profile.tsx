import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router-dom";
import { useCompanies } from "@/integrations/legal-concierge/hooks/useCompanies";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { User, Building, Settings, Mail, Phone } from "lucide-react";
import Layout from "@/components/Layout";

// Define the form schema
const profileFormSchema = z.object({
  full_name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phone: z.string().min(10, {
    message: "Phone number must be at least 10 digits.",
  }),
  avatar_url: z.string().nullable(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const Profile: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { data: companies, isLoading: isCompaniesLoading } = useCompanies(true);

  // Initialize form with user data
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      full_name: user?.fullName || "",
      email: user?.email || "",
      phone: user?.phoneNumber || "",
      avatar_url: user?.avatarUrl || null,
    },
  });

  // Placeholder for form submission - not used since form is disabled
  const onSubmit = async (data: ProfileFormValues) => {
    console.log("Form submission disabled", data);
  };

  // Generate initials for avatar
  const getInitials = () => {
    const name = user?.fullName || user?.email?.split("@")[0] || "User";
    return name.substring(0, 2).toUpperCase();
  };

  return (
    <Layout>
      <div className="container max-w-6xl py-10">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
            <p className="text-muted-foreground">
              Manage your account settings and view your companies
            </p>
          </div>
          <Separator />

          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full md:w-auto grid-cols-2 md:grid-cols-3">
              <TabsTrigger value="general" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>General</span>
              </TabsTrigger>
              <TabsTrigger
                value="companies"
                className="flex items-center gap-2"
              >
                <Building className="h-4 w-4" />
                <span>Companies</span>
              </TabsTrigger>
              <TabsTrigger value="account" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <span>Account</span>
              </TabsTrigger>
            </TabsList>

            {/* General Tab - Personal Information */}
            <TabsContent value="general">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>
                    Update your personal details and contact information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col md:flex-row gap-8">
                    <div className="flex flex-col items-center space-y-4">
                      <Avatar className="h-24 w-24">
                        {user?.avatarUrl ? (
                          <AvatarImage
                            src={user.avatarUrl}
                            alt={user.fullName}
                          />
                        ) : (
                          <AvatarFallback className="bg-primary text-primary-foreground text-xl">
                            {getInitials()}
                          </AvatarFallback>
                        )}
                      </Avatar>
                      <Button variant="outline" size="sm" disabled>
                        Change Avatar
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        Avatar upload coming soon
                      </p>
                    </div>

                    <div className="flex-1">
                      <Form {...form}>
                        <form
                          onSubmit={form.handleSubmit(onSubmit)}
                          className="space-y-6"
                        >
                          <FormField
                            control={form.control}
                            name="full_name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Full Name</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Your name"
                                    {...field}
                                    disabled
                                  />
                                </FormControl>
                                <FormDescription>
                                  Your profile name as it appears across the
                                  platform
                                </FormDescription>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email</FormLabel>
                                <FormControl>
                                  <div className="flex">
                                    <Mail className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                                    <Input
                                      placeholder="<EMAIL>"
                                      {...field}
                                      disabled
                                    />
                                  </div>
                                </FormControl>
                                <FormDescription>
                                  Your email address is used for login and
                                  notifications
                                </FormDescription>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Phone Number</FormLabel>
                                <FormControl>
                                  <div className="flex">
                                    <Phone className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                                    <Input
                                      placeholder="(*************"
                                      {...field}
                                      disabled
                                    />
                                  </div>
                                </FormControl>
                                <FormDescription>
                                  Your phone number is used for account
                                  verification
                                </FormDescription>
                              </FormItem>
                            )}
                          />

                          <div className="flex items-center gap-4 mt-4">
                            <Button
                              type="button"
                              disabled
                              className="bg-muted text-muted-foreground"
                            >
                              Edit Profile
                            </Button>
                            <p className="text-sm text-muted-foreground">
                              Profile editing is currently disabled
                            </p>
                          </div>
                        </form>
                      </Form>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Companies Tab */}
            <TabsContent value="companies">
              <Card>
                <CardHeader>
                  <CardTitle>Your Companies</CardTitle>
                  <CardDescription>
                    View and manage the companies you're associated with
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isCompaniesLoading ? (
                    <div className="space-y-4">
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-20 w-full" />
                    </div>
                  ) : companies && companies.length > 0 ? (
                    <div className="space-y-4">
                      {companies.map((company) => (
                        <div
                          key={company.companyId}
                          className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                        >
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center text-sm font-bold">
                              {(company.companyName?.[0] || "C").toUpperCase()}
                            </div>
                            <div>
                              <p className="font-medium">
                                {company.companyName || "Unnamed Company"}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                Role: {company.role}
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate("/dashboard")}
                          >
                            View Dashboard
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        You are not associated with any companies yet.
                      </p>
                      <Button
                        variant="outline"
                        className="mt-4"
                        onClick={() => navigate("/dashboard")}
                      >
                        Go to Dashboard
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Account Tab */}
            <TabsContent value="account">
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                  <CardDescription>
                    Manage your account security and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Password</h3>
                    <p className="text-sm text-muted-foreground">
                      Change your password to keep your account secure
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => navigate("/forgot-password")}
                    >
                      Change Password
                    </Button>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Account ID</h3>
                    <p className="text-sm text-muted-foreground">
                      Your unique account identifier
                    </p>
                    <div className="p-2 bg-muted rounded-md">
                      <code className="text-sm">{user?.id}</code>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default Profile;
