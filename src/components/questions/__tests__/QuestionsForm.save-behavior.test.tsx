import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor, act } from "@/test/utils";
import userEvent from "@testing-library/user-event";
import QuestionsForm from "../QuestionsForm";
import { mockLegacyFormData } from "@/test/mocks/companyData";

// Mock the hooks and API calls
vi.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({
    user: { id: "test-user", companyId: "test-company" },
  }),
}));

vi.mock("@/integrations/legal-concierge/hooks/useCompanyInfo", () => ({
  useCompanyById: () => ({
    data: mockLegacyFormData,
    isLoading: false,
  }),
  useUpdateCompanyName: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000)) // Simulate 1 second delay
    ),
  }),
  useUpdateCompanyAddress: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000)) // Simulate 1 second delay
    ),
  }),
  useUpdateAuthorizedShares: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
  useConfirmCompanyForm: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
}));

vi.mock("@/integrations/legal-concierge/hooks/useStockOptionPlan", () => ({
  useUpdateStockOptionPlan: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
}));

vi.mock("@/integrations/legal-concierge/hooks/useVesting", () => ({
  useUpdateVesting: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
}));

vi.mock("@/integrations/legal-concierge/hooks/useRegisteredAgent", () => ({
  useUpdateRegisteredAgent: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
}));

vi.mock("@/integrations/legal-concierge/hooks/useTechnology", () => ({
  useUpdateTechnology: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
}));

vi.mock("@/integrations/legal-concierge/hooks/useOfficerIP", () => ({
  useUpdateOfficerIP: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
}));

vi.mock("@/integrations/legal-concierge/hooks/useOfficers", () => ({
  useAddOfficer: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
  useUpdateOfficer: () => ({
    mutateAsync: vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    ),
  }),
}));

describe("QuestionsForm - Save Behavior", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  it("should maintain company name field value during save operation", async () => {
    const user = userEvent.setup();
    
    render(<QuestionsForm />);

    // Wait for form to load
    await waitFor(() => {
      expect(screen.getByDisplayValue("Test Company Inc.")).toBeInTheDocument();
    });

    // Type a new company name
    const nameInput = screen.getByLabelText(/preferred name of the Company/i);
    await user.clear(nameInput);
    await user.type(nameInput, "New Company Name");

    // Verify the value is displayed
    expect(screen.getByDisplayValue("New Company Name")).toBeInTheDocument();

    // Click Continue button to trigger save
    const continueButton = screen.getByRole("button", { name: /continue/i });
    await user.click(continueButton);

    // Verify the field value remains visible during the save operation
    expect(screen.getByDisplayValue("New Company Name")).toBeInTheDocument();
    
    // Verify loading state is shown
    expect(continueButton).toBeDisabled();

    // Wait for save to complete
    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    }, { timeout: 2000 });

    // Verify the value is still there after save completes
    expect(screen.getByDisplayValue("New Company Name")).toBeInTheDocument();
  });

  it("should maintain address field values during save operation", async () => {
    const user = userEvent.setup();
    
    render(<QuestionsForm />);

    // Navigate to company address step
    const continueButton = screen.getByRole("button", { name: /continue/i });
    await user.click(continueButton);

    // Wait for address step to load
    await waitFor(() => {
      expect(screen.getByLabelText(/street address/i)).toBeInTheDocument();
    });

    // Fill in address fields
    const streetInput = screen.getByLabelText(/^street address$/i);
    const cityInput = screen.getByLabelText(/city/i);
    const zipInput = screen.getByLabelText(/zip code/i);

    await user.clear(streetInput);
    await user.type(streetInput, "123 New Street");
    await user.clear(cityInput);
    await user.type(cityInput, "New City");
    await user.clear(zipInput);
    await user.type(zipInput, "12345");

    // Verify the values are displayed
    expect(screen.getByDisplayValue("123 New Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("New City")).toBeInTheDocument();
    expect(screen.getByDisplayValue("12345")).toBeInTheDocument();

    // Click Continue button to trigger save
    await user.click(continueButton);

    // Verify the field values remain visible during the save operation
    expect(screen.getByDisplayValue("123 New Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("New City")).toBeInTheDocument();
    expect(screen.getByDisplayValue("12345")).toBeInTheDocument();
    
    // Verify loading state is shown
    expect(continueButton).toBeDisabled();

    // Wait for save to complete
    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    }, { timeout: 2000 });

    // Verify the values are still there after save completes
    expect(screen.getByDisplayValue("123 New Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("New City")).toBeInTheDocument();
    expect(screen.getByDisplayValue("12345")).toBeInTheDocument();
  });

  it("should not flicker or lose values when form data updates from server", async () => {
    const user = userEvent.setup();
    
    render(<QuestionsForm />);

    // Wait for form to load
    await waitFor(() => {
      expect(screen.getByDisplayValue("Test Company Inc.")).toBeInTheDocument();
    });

    // Type a new company name
    const nameInput = screen.getByLabelText(/preferred name of the Company/i);
    await user.clear(nameInput);
    await user.type(nameInput, "Updated Company Name");

    // Verify the value is displayed
    expect(screen.getByDisplayValue("Updated Company Name")).toBeInTheDocument();

    // Simulate rapid typing to test that values don't flicker
    await user.type(nameInput, " LLC");
    expect(screen.getByDisplayValue("Updated Company Name LLC")).toBeInTheDocument();

    // Click Continue button
    const continueButton = screen.getByRole("button", { name: /continue/i });
    await user.click(continueButton);

    // During the save operation, the value should never disappear
    // We'll check multiple times during the save process
    const checkValueExists = () => {
      expect(screen.getByDisplayValue("Updated Company Name LLC")).toBeInTheDocument();
    };

    // Check immediately after clicking
    checkValueExists();

    // Check after a short delay
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });
    checkValueExists();

    // Check midway through save
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 500));
    });
    checkValueExists();

    // Wait for save to complete and check final state
    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    }, { timeout: 2000 });
    checkValueExists();
  });

  it("should preserve form state when navigating back to a previous step", async () => {
    const user = userEvent.setup();
    
    render(<QuestionsForm />);

    // Wait for form to load
    await waitFor(() => {
      expect(screen.getByDisplayValue("Test Company Inc.")).toBeInTheDocument();
    });

    // Type a new company name
    const nameInput = screen.getByLabelText(/preferred name of the Company/i);
    await user.clear(nameInput);
    await user.type(nameInput, "Preserved Company Name");

    // Navigate to next step
    const continueButton = screen.getByRole("button", { name: /continue/i });
    await user.click(continueButton);

    // Wait for next step to load
    await waitFor(() => {
      expect(screen.getByLabelText(/street address/i)).toBeInTheDocument();
    });

    // Navigate back
    const backButton = screen.getByRole("button", { name: /back/i });
    await user.click(backButton);

    // Wait for previous step to load
    await waitFor(() => {
      expect(screen.getByLabelText(/preferred name of the Company/i)).toBeInTheDocument();
    });

    // Verify the previously entered value is still there
    expect(screen.getByDisplayValue("Preserved Company Name")).toBeInTheDocument();
  });

  it("should handle slow API responses without losing field values", async () => {
    const user = userEvent.setup();
    
    render(<QuestionsForm />);

    // Wait for form to load
    await waitFor(() => {
      expect(screen.getByDisplayValue("Test Company Inc.")).toBeInTheDocument();
    });

    // Type a new company name
    const nameInput = screen.getByLabelText(/preferred name of the Company/i);
    await user.clear(nameInput);
    await user.type(nameInput, "Slow API Test Company");

    // Verify the value is displayed
    expect(screen.getByDisplayValue("Slow API Test Company")).toBeInTheDocument();

    // Click Continue button to trigger save
    const continueButton = screen.getByRole("button", { name: /continue/i });
    await user.click(continueButton);

    // Verify the field value remains visible throughout the entire save operation
    expect(screen.getByDisplayValue("Slow API Test Company")).toBeInTheDocument();
    
    // Check at multiple intervals during the save
    for (let i = 0; i < 10; i++) {
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });
      expect(screen.getByDisplayValue("Slow API Test Company")).toBeInTheDocument();
    }

    // Wait for save to complete
    await waitFor(() => {
      expect(continueButton).not.toBeDisabled();
    }, { timeout: 2000 });

    // Final verification
    expect(screen.getByDisplayValue("Slow API Test Company")).toBeInTheDocument();
  });
});
