import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import AuthorizeNotesSection from "./convertiblenote/AuthorizeNotesSection";
import CurrentNotesSection from "./convertiblenote/CurrentNotesSection";

const ConvertibleNoteTab: React.FC = () => {
  const [activeSubTab, setActiveSubTab] = useState("authorize-notes");

  return (
    <div className="mt-6">
      <Tabs
        defaultValue="authorize-notes"
        value={activeSubTab}
        onValueChange={setActiveSubTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="authorize-notes">Authorize Notes</TabsTrigger>
          <TabsTrigger value="current-notes">Current Notes</TabsTrigger>
        </TabsList>

        <TabsContent value="authorize-notes">
          <AuthorizeNotesSection />
        </TabsContent>

        <TabsContent value="current-notes">
          <CurrentNotesSection />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ConvertibleNoteTab;
