import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { QuestionnaireFormData } from "../types";
import { Card } from "@/components/common/Card";
import { useAuth } from "@/contexts/AuthContext";
import { useGetRegisteredAgents } from "@/integrations/legal-concierge/hooks/useRegisteredAgent";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

interface RegisteredAgentStepProps {
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: any) => void;
}

// Export the save function so it can be called from the continue button
export const saveRegisteredAgentData = async (
  formData: QuestionnaireFormData,
  companyId: string,
  updateRegisteredAgent: any
): Promise<boolean> => {
  try {
    if (!formData.registeredAgent?.id) {
      console.log("No registered agent selected, skipping save");
      return true; // Don't fail if no agent is selected
    }

    const payload = {
      id: companyId,
      companyId: companyId,
      agentId: formData.registeredAgent.id,
    };

    console.log("Saving registered agent with payload:", payload);

    await updateRegisteredAgent.mutateAsync(payload);

    console.log("Registered agent saved successfully");
    toast.success("Registered agent information saved successfully");
    return true;
  } catch (error) {
    console.error("Error saving registered agent:", error);
    toast.error("Failed to save registered agent information");
    return false;
  }
};

const RegisteredAgentStep: React.FC<RegisteredAgentStepProps> = ({
  formData,
  updateFormData,
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId || "";

  // Fetch registered agents
  const {
    data: registeredAgents = [],
    isLoading,
    error,
  } = useGetRegisteredAgents(companyId);

  const handleAgentSelection = (agentId: string) => {
    // Find the selected agent
    const selectedAgent = registeredAgents.find(
      (agent) => agent.id === agentId
    );
    if (selectedAgent) {
      // Update local form data with the full agent object
      updateFormData("registeredAgent", {
        id: selectedAgent.id,
        name: selectedAgent.name,
        rate: selectedAgent.rate,
        description: selectedAgent.description,
        rateDescription: selectedAgent.rateDescription,
      });
    }
  };

  const handleCardClick = (agentId: string) => {
    // Update only local state
    handleAgentSelection(agentId);
  };

  // NOTE: We're only updating the local state here
  // The parent component will handle saving to the server when the user clicks "Continue"
  // This is done via the formStorage module in the saveStepData function
  // This approach prevents multiple API calls when the user is just exploring options
  console.log({ value: registeredAgents });
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-base font-medium">
            Please confirm the registered agent for your company.
          </h3>
          <p className="text-sm text-gray-600">
            {formData.registerMode == "getting-started"
              ? `A registered agent receives legal documents and official notices on
            behalf of your company.`
              : `A registered agent that received legal documents and official notices on
            behalf of your company.`}
          </p>
        </div>

        {/* Loading state */}
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 text-gray-400 animate-spin" />
            <span className="ml-2 text-gray-500">
              Loading registered agents...
            </span>
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">
              Error loading registered agents. Using default options.
            </p>
          </div>
        )}

        {/* Agents list */}
        <RadioGroup
          value={formData.registeredAgent?.id}
          onValueChange={(value) => handleAgentSelection(value)}
          className="space-y-4"
        >
          {registeredAgents.map((agent) => (
            <div key={agent.id}>
              <Card
                className={`p-4 cursor-pointer ${formData.registeredAgent?.id === agent.id ? "border-2 border-primary" : ""}`}
                onClick={() => handleCardClick(agent.id)}
                hover={true}
              >
                <div className="flex items-start">
                  <RadioGroupItem
                    value={agent.id}
                    id={agent.id}
                    className="mt-1 mr-3"
                  />
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-2">
                      <Label
                        htmlFor={agent.id}
                        className="text-base font-medium cursor-pointer"
                      >
                        {agent.name}
                      </Label>
                      <div className="text-right">
                        <p className="text-sm font-bold">{agent.rate}</p>
                        <p className="text-xs text-gray-500">
                          {agent.rateDescription}
                        </p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{agent.description}</p>
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </RadioGroup>
      </div>
    </div>
  );
};

export default RegisteredAgentStep;
