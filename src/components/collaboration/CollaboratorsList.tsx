import React, { useState } from "react";
import { Plus, Users, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CollaboratorRole, Collaborator as UICollaborator } from "./types";
import { Collaborator as APICollaborator } from "@/integrations/legal-concierge/types/Collaborator";
import CollaboratorCard from "./CollaboratorCard";
import InviteCollaboratorDialog from "./InviteCollaboratorDialog";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import {
  useCompanyCollaborators,
  useSendInvite,
  useUpdateCollaboratorRole,
  useDeleteCollaborator,
} from "@/integrations/legal-concierge/hooks/useCollaborators";

const CollaboratorsList: React.FC = () => {
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const { user } = useAuth();
  const companyId = user?.companyId || "";

  // Fetch collaborators
  const {
    data: collaborators = [],
    isLoading,
    error,
  } = useCompanyCollaborators(companyId);

  // Mutations
  const sendInvite = useSendInvite();
  const updateRole = useUpdateCollaboratorRole();
  const deleteCollaborator = useDeleteCollaborator();

  // Handle remove collaborator
  const handleRemoveCollaborator = async (id: string) => {
    if (!companyId) {
      toast.error("No company selected");
      return;
    }

    try {
      await deleteCollaborator.mutateAsync({
        collaboratorId: id,
        companyId,
      });

      toast.success("Collaborator removed successfully");
    } catch (error) {
      toast.error(`Failed to remove collaborator: ${error.message}`);
    }
  };

  // Handle role change
  const handleChangeRole = async (id: string, newRole: CollaboratorRole) => {
    if (!companyId) {
      toast.error("No company selected");
      return;
    }

    try {
      await updateRole.mutateAsync({
        collaboratorId: id,
        companyId,
        role: newRole,
      });

      toast.success("Role updated successfully");
    } catch (error) {
      toast.error(`Failed to update role: ${error.message}`);
    }
  };

  // Handle invite collaborator
  const handleInviteCollaborator = async (
    email: string,
    role: CollaboratorRole
  ) => {
    if (!companyId) {
      toast.error("No company selected");
      return;
    }

    try {
      // Use the sendInvite API instead of inviteCollaborator
      await sendInvite.mutateAsync({
        email,
        role,
      });

      toast.success(`Invitation sent to ${email}`);
      setShowInviteDialog(false);
    } catch (error) {
      toast.error(`Failed to invite collaborator: ${error.message}`);
    }
  };

  // Convert API collaborator to UI collaborator format
  const convertToUICollaborator = (
    apiCollaborator: APICollaborator
  ): UICollaborator => {
    return {
      id: apiCollaborator.id,
      name: apiCollaborator.name,
      fullName: apiCollaborator.fullName,
      email: apiCollaborator.email,
      role: apiCollaborator.role,
      status: apiCollaborator.status,
      dateAdded: apiCollaborator.dateAdded
        ? new Date(apiCollaborator.dateAdded)
        : undefined,
      lastActive: apiCollaborator.lastActive
        ? new Date(apiCollaborator.lastActive)
        : undefined,
      activity: apiCollaborator.activity,
    };
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Users className="h-5 w-5 text-gray-700" />
          <h2 className="text-lg font-semibold text-gray-900">Collaborators</h2>
        </div>
        {user?.roles?.includes('OWNER') && 
        <Button
        onClick={() => setShowInviteDialog(true)}
        size="sm"
        className="flex items-center space-x-1"
        disabled={!companyId}
      >
        <Plus className="h-4 w-4 mr-1" />
        <span>Invite</span>
      </Button>
        }
        
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 text-gray-400 animate-spin" />
          <span className="ml-2 text-gray-500">Loading collaborators...</span>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2" />
          <div>
            <h3 className="text-red-800 font-medium">
              Error loading collaborators
            </h3>
            <p className="text-red-600 text-sm">{error.message}</p>
          </div>
        </div>
      )}

      {/* Collaborators grid */}
      {!isLoading && !error && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {collaborators.map((collaborator) => (
            <CollaboratorCard
              key={collaborator.id}
              collaborator={convertToUICollaborator(collaborator)}
              onRemove={handleRemoveCollaborator}
              onChangeRole={handleChangeRole}
            />
          ))}

          {collaborators.length === 0 && (
            <div className="col-span-full text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-200">
              <Users className="h-10 w-10 text-gray-400 mx-auto" />
              <h3 className="mt-2 text-gray-900 font-medium">
                No collaborators yet
              </h3>
              <p className="text-gray-500 text-sm">
                Invite team members to collaborate on your incorporation
              </p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setShowInviteDialog(true)}
                disabled={!companyId}
              >
                Invite Your Team
              </Button>
            </div>
          )}
        </div>
      )}

      <InviteCollaboratorDialog
        open={showInviteDialog}
        onOpenChange={setShowInviteDialog}
        onInvite={handleInviteCollaborator}
        userData={user}
      />
    </div>
  );
};

export default CollaboratorsList;
