import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ShareholderRole, ShareClass } from "@/types/capTable";
import {
  Calendar,
  Download,
  Filter,
  Printer,
  Search,
  SlidersHorizontal,
} from "lucide-react";

interface CapTableFiltersProps {
  onSearch: (term: string) => void;
  onFilterRole: (role: ShareholderRole | "All") => void;
  onFilterShareClass: (shareClass: ShareClass | "All") => void;
  onToggleView: (view: "standard" | "fullyDiluted") => void;
  onExport: (format: "pdf" | "csv") => void;
  currentView: "standard" | "fullyDiluted";
}

const CapTableFilters: React.FC<CapTableFiltersProps> = ({
  onSearch,
  onFilterRole,
  onFilterShareClass,
  onToggleView,
  onExport,
  currentView,
}) => {
  return (
    <div className="mb-6 space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search shareholders..."
            className="pl-9"
            onChange={(e) => onSearch(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport("pdf")}
            className="flex items-center gap-1"
          >
            <Download size={16} />
            <span>Export PDF</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport("csv")}
            className="flex items-center gap-1"
          >
            <Download size={16} />
            <span>Export CSV</span>
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 items-center">
        <div className="flex items-center">
          <Filter size={16} className="mr-2 text-gray-500" />
          <span className="text-sm font-medium">Filters:</span>
        </div>

        <Select
          onValueChange={(value) =>
            onFilterRole(value as ShareholderRole | "All")
          }
        >
          <SelectTrigger className="w-[150px] h-8">
            <SelectValue placeholder="Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Roles</SelectItem>
            <SelectItem value="Founder">Founders</SelectItem>
            <SelectItem value="Investor">Investors</SelectItem>
            <SelectItem value="Employee">Employees</SelectItem>
            <SelectItem value="Advisor">Advisors</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>

        <Select
          onValueChange={(value) =>
            onFilterShareClass(value as ShareClass | "All")
          }
        >
          <SelectTrigger className="w-[180px] h-8">
            <SelectValue placeholder="Share Class" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Share Classes</SelectItem>
            <SelectItem value="Common">Common</SelectItem>
            <SelectItem value="Series A Preferred">
              Series A Preferred
            </SelectItem>
            <SelectItem value="Stock Option Pool">Stock Option Pool</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={currentView}
          onValueChange={(value) =>
            onToggleView(value as "standard" | "fullyDiluted")
          }
        >
          <SelectTrigger className="w-[180px] h-8">
            <SelectValue placeholder="View" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="standard">Standard View</SelectItem>
            <SelectItem value="fullyDiluted">Fully Diluted View</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default CapTableFilters;
