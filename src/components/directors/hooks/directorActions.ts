import { Dispatch, SetStateAction } from "react";
import { Director } from "../types";
import { toast } from "@/hooks/use-toast";
import { directorService } from "@/services/directorService";

// Handles the director actions
export const useDirectorActions = (
  directors: Director[],
  setDirectors: Dispatch<SetStateAction<Director[]>>,
  setLoading: Dispatch<SetStateAction<boolean>>,
  companyId: string | null
) => {
  const addDirector = async (
    directorData: Partial<Director>
  ): Promise<void> => {
    if (!companyId) {
      toast({
        title: "Error",
        description: "No company ID found. Please create a company first.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Convert app director to database format
      const appointmentDate = directorData.appointmentDate || new Date();

      const dbDirector = {
        company_id: companyId,
        name: directorData.name || "",
        appointment_date: appointmentDate.toISOString().split("T")[0],
        resigned: directorData.resigned || false,
        resignation_date:
          directorData.resignationDate?.toISOString().split("T")[0] || null,
        stockholder_approval: directorData.stockholderApproval || false,
      };

      // Save to Supabase
      const saved = await directorService.addDirector(dbDirector);

      if (!saved) {
        throw new Error("Failed to add director");
      }

      // Convert saved data back to app format
      const newDirector: Director = {
        id: saved.id || "",
        name: saved.name,
        appointmentDate: new Date(saved.appointment_date),
        resigned: saved.resigned,
        resignationDate: saved.resignation_date
          ? new Date(saved.resignation_date)
          : null,
        stockholderApproval: saved.stockholder_approval,
      };

      // Update local state
      setDirectors((prevDirectors) => [...prevDirectors, newDirector]);
    } catch (error) {
      console.error("Error adding director:", error);
      toast({
        title: "Error",
        description: "Failed to add director",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateDirector = async (id: string, updates: Partial<Director>) => {
    setLoading(true);
    try {
      // Convert app updates to database format
      const dbUpdates: any = {};

      if (updates.name !== undefined) dbUpdates.name = updates.name;
      if (updates.appointmentDate !== undefined)
        dbUpdates.appointment_date = updates.appointmentDate
          .toISOString()
          .split("T")[0];
      if (updates.resigned !== undefined) dbUpdates.resigned = updates.resigned;
      if (updates.resignationDate !== undefined)
        dbUpdates.resignation_date =
          updates.resignationDate?.toISOString().split("T")[0] || null;
      if (updates.stockholderApproval !== undefined)
        dbUpdates.stockholder_approval = updates.stockholderApproval;

      // Save to Supabase
      const updated = await directorService.updateDirector(id, dbUpdates);

      if (!updated) {
        throw new Error("Failed to update director");
      }

      // Update local state
      setDirectors((prevDirectors) =>
        prevDirectors.map((director) =>
          director.id === id ? { ...director, ...updates } : director
        )
      );

      return updated;
    } catch (error) {
      console.error("Error updating director:", error);
      toast({
        title: "Error",
        description: "Failed to update director",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  const uploadResignationLetter = async (id: string, file: File) => {
    setLoading(true);
    try {
      // In a real implementation, we would upload this to Supabase Storage
      // For demo purposes, we'll simulate a successful upload
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update the director with the resignation letter info
      const updated = await updateDirector(id, {
        resigned: true,
        resignationDate: new Date(),
      });

      return !!updated;
    } catch (error) {
      console.error("Error uploading resignation letter:", error);
      toast({
        title: "Error",
        description: "Failed to upload resignation letter",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    addDirector,
    updateDirector,
    uploadResignationLetter,
  };
};
