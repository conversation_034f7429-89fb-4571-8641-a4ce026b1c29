import React from "react";
import { OfficerTitle } from "../../types";
import Button from "@/components/common/Button";
import { PlusCircle } from "lucide-react";
import DirectorForm from "./DirectorForm";
import { Officer } from "../../types/Officer";

interface DirectorsListProps {
  directors: Officer[];
  onAddDirector: () => void;
  onRemoveDirector: (id: string) => void;
  onUpdateDirectorField: (
    id: string,
    field: keyof Officer,
    value: string | number | boolean | OfficerTitle[]
  ) => void;
  onToggleOfficerTitle: (id: string, title: OfficerTitle) => void;
  isTitleTakenByOther: (currentId: string, title: OfficerTitle) => boolean;
}

const DirectorsList: React.FC<DirectorsListProps> = ({
  directors,
  onAddDirector,
  onRemoveDirector,
  onUpdateDirectorField,
  onToggleOfficerTitle,
  isTitleTakenByOther,
}) => {
  if (directors.length === 0) {
    return (
      <div className="text-center p-6 bg-gray-50 rounded-lg">
        <p className="text-gray-500 mb-4">
          No directors/officers/shareholders added yet
        </p>
        <Button onClick={onAddDirector} icon={<PlusCircle size={16} />}>
          Add Person
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {directors.map((director, index) => (
        <DirectorForm
          key={director.id}
          director={director}
          index={index}
          onRemove={onRemoveDirector}
          onUpdate={onUpdateDirectorField}
          onToggleOfficerTitle={onToggleOfficerTitle}
          isTitleTakenByOther={isTitleTakenByOther}
        />
      ))}

      <Button
        onClick={onAddDirector}
        icon={<PlusCircle size={16} />}
        variant="outline"
        className="mt-4"
      >
        Add Another Person
      </Button>
    </div>
  );
};

export default DirectorsList;
