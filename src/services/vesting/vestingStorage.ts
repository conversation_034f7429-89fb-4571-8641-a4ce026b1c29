import { toast } from "sonner";
import { VestingScheduleData } from "./types";
import { Officer } from "@/integrations/legal-concierge/types/Company";
import { VestingInfo } from "@/components/questions/types";
import { APIClient } from "@/integrations/legal-concierge/client";
import {
  VestingRequest,
  VestingUnit,
  VestingSchedule as ApiVestingSchedule,
  VestingAcceleration,
} from "@/integrations/legal-concierge/types/Stocks";

// Map from app vesting schedule to API vesting schedule
const mapVestingSchedule = (schedule: string | null): ApiVestingSchedule => {
  if (schedule === "Standard") return "FOURYEARSONEYEARCLIFF";
  if (schedule === "Monthly") return "FOURYEARSNOCLIFF";
  return "FOURYEARSONEYEARCLIFF"; // Default
};

// Map from app acceleration to API acceleration
const mapAcceleration = (acceleration: string | null): VestingAcceleration => {
  if (acceleration === "SingleTrigger") return "SINGLETRIGGER";
  if (acceleration === "DoubleTrigger") return "DOUBLETRIGGER";
  return "NONE"; // Default
};

// Map from API vesting schedule to app vesting schedule
const mapApiVestingSchedule = (schedule: string): string => {
  if (schedule === "FOURYEARSONEYEARCLIFF") return "Standard";
  if (schedule === "FOURYEARSNOCLIFF") return "Monthly";
  return "Standard"; // Default
};

// Map from API acceleration to app acceleration
const mapApiAcceleration = (acceleration: string): string => {
  if (acceleration === "SINGLETRIGGER") return "SingleTrigger";
  if (acceleration === "DOUBLETRIGGER") return "DoubleTrigger";
  return "None"; // Default
};

/**
 * Saves vesting schedules using the legal-concierge client
 */
export async function saveVestingSchedules(
  directors: Officer[],
  vestingInfo: VestingInfo[],
  issuedShares: number,
  companyId: string,
  isVestingConfirmed: boolean = false
): Promise<boolean> {
  try {
    if (!companyId) {
      console.log("Hello", { companyId });
      toast.error("Company ID is required to save vesting schedules");
      return false;
    }

    const api = new APIClient();

    // Convert vesting info to the format expected by the API
    const vestingUnits: VestingUnit[] = directors.map((director) => {
      const vInfo = vestingInfo.find((v) => v.id === director.id);
      const isVester = vInfo?.isVester || false;

      return {
        officerId: director.id,
        isVester: isVester,
        vestingSchedule: mapVestingSchedule(vInfo?.vestingSchedule),
        acceleration: mapAcceleration(vInfo?.acceleration),
      };
    });

    // Create the vesting request
    const vestingRequest: VestingRequest = {
      companyId,
      vestingUnits,
      isVestingConfirmed: isVestingConfirmed, // Use the actual confirmation status
    };

    // Update vesting info
    await api.updateVesting(companyId, vestingRequest);
    toast.success("Vesting schedules saved successfully");
    return true;
  } catch (error) {
    console.error("Error in saveVestingSchedules:", error);
    toast.error("An error occurred while saving vesting schedules");
    return false;
  }
}

/**
 * Retrieves vesting schedules using the legal-concierge client
 */
export async function getVestingSchedules(): Promise<VestingScheduleData[]> {
  try {
    const api = new APIClient();

    // Get the current user's company ID
    const { data: user } = await api.profile();
    if (!user || !user.companyId) {
      return [];
    }

    // Get company details which include officers with vesting info
    const companyDetails = await api.getCompanyById(user.companyId);
    if (!companyDetails || !companyDetails.officers) {
      return [];
    }

    // Filter officers with vesting enabled
    const officersWithVesting = companyDetails.officers.filter(
      (officer) => officer.isVester
    );

    // Map to VestingScheduleData format
    return officersWithVesting.map((officer) => {
      // Determine cliff based on schedule type
      let cliffMonths = 0;
      if (officer.vestingSchedule === "FOURYEARSONEYEARCLIFF") {
        cliffMonths = 12;
      }

      return {
        id: officer.id,
        director_id: officer.id,
        director_name: officer.name || "Unnamed",
        schedule_type: mapApiVestingSchedule(officer.vestingSchedule),
        vesting_start_date: new Date(), // API doesn't provide this, using current date as fallback
        total_shares: officer.amountOfShares,
        cliff_months: cliffMonths,
        vesting_period_months: 48, // 4 years (48 months) standard
        acceleration: mapApiAcceleration(officer.accleration),
        company_id: officer.companyId,
      };
    });
  } catch (error) {
    console.error("Error in getVestingSchedules:", error);
    return [];
  }
}
