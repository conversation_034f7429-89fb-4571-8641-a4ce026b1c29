const generateRandomString = (length: number): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

export const generateDocumentKey = (): string => {
  return generateRandomString(13);
};

export const createOnlyOfficeConfig = (
  document: {
    fileName: string;
    url: string;
  },
  mode: 'view' | 'edit',
  user: {
    id: string;
    name: string;
  }
) => {
  const config = {
    document: {
      fileType: "docx",
      key: generateDocumentKey(),
      title: document.fileName,
      url: document.url,
      permissions: {
        edit: mode === 'edit',
        download: true,
        forcesave: mode === 'edit',
      },
    },
    documentType: "word",
    editorConfig: {
      mode,
      callbackUrl: "https://apidev.foundersform.com/api/onlyoffice/callback",
      user: {
        id: user.id,
        name: user.name,
      },
      customization: {
        autosave: true,
        compactHeader: true,
        compactToolbar: true,
        hideRulers: true,
        hideStatusBar: true,
        hideRightMenu: true,
        hideLeftMenu: true,
        toolbarHideFileName: true,
        about: false,
        feedback: false,
        help: false,
        plugins: false,
        toolbar: false,
        header: false,
        disableContextMenu: true,
        forcesave: true,
      },
    },
    height: "100%",
    width: "100%",
  };

  return config;
}; 