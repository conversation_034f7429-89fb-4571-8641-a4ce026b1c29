import React, { useState } from "react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { Safe } from "@/types/financing";

// Mock data for demonstration
const mockCurrentSafe: Safe = {
  id: "1",
  dateAuthorized: new Date("2023-02-10"),
  authorizedAmount: 1000000,
  outstandingSafeAmount: 800000,
  valuationCap: 8000000,
  discount: 20,
  mfn: true,
  boardApproved: true,
};

const mockNewSafeTerms: Safe = {
  ...mockCurrentSafe,
  valuationCap: 7000000, // Lower valuation cap
  discount: 25, // Higher discount
};

const MfnSafesSection: React.FC = () => {
  const { toast } = useToast();
  const [notifyDialogOpen, setNotifyDialogOpen] = useState(false);

  const handleNotifyHolders = () => {
    setNotifyDialogOpen(true);
  };

  const handleSendNotifications = () => {
    toast({
      title: "Notifications Sent",
      description:
        "Holders have been notified of their option to amend and restate their SAFEs.",
    });
    setNotifyDialogOpen(false);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">MFN for SAFEs</h3>
          <p className="text-sm text-muted-foreground">
            The Most Favored Nation (MFN) provision in your SAFEs may require
            you to offer updated terms to existing SAFE holders when you issue
            new convertible instruments with more favorable terms.
          </p>
        </div>

        <div className="space-y-8">
          <div>
            <h4 className="text-md font-medium mb-3">Current SAFE Round</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date Authorized</TableHead>
                  <TableHead>Authorized SAFE Amount</TableHead>
                  <TableHead>Outstanding SAFE Amount</TableHead>
                  <TableHead>Valuation Cap</TableHead>
                  <TableHead>Discount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>
                    {mockCurrentSafe.dateAuthorized
                      ? format(mockCurrentSafe.dateAuthorized, "PP")
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    ${mockCurrentSafe.authorizedAmount.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    ${mockCurrentSafe.outstandingSafeAmount.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    ${mockCurrentSafe.valuationCap.toLocaleString()}
                  </TableCell>
                  <TableCell>{mockCurrentSafe.discount}%</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div>
            <h4 className="text-md font-medium mb-3">New SAFE Terms</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date Authorized</TableHead>
                  <TableHead>Authorized SAFE Amount</TableHead>
                  <TableHead>Outstanding SAFE Amount</TableHead>
                  <TableHead>Valuation Cap</TableHead>
                  <TableHead>Discount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>
                    {mockNewSafeTerms.dateAuthorized
                      ? format(mockNewSafeTerms.dateAuthorized, "PP")
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    ${mockNewSafeTerms.authorizedAmount.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    ${mockNewSafeTerms.outstandingSafeAmount.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      ${mockNewSafeTerms.valuationCap.toLocaleString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {mockNewSafeTerms.discount}%
                    </span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleNotifyHolders}>Notify Holders</Button>
          </div>
        </div>

        <Dialog open={notifyDialogOpen} onOpenChange={setNotifyDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Amended and Restated SAFE</DialogTitle>
              <DialogDescription>
                Review the amended and restated SAFE terms before notifying
                holders.
              </DialogDescription>
            </DialogHeader>

            <div className="h-60 border rounded-md p-4 overflow-y-auto bg-gray-50 my-4">
              <h3 className="text-md font-semibold mb-2">
                Amended and Restated SAFE
              </h3>
              <p className="text-sm">
                This document would contain the full text of the amended and
                restated SAFE with updated terms as shown in the "New SAFE
                Terms" table.
              </p>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setNotifyDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleSendNotifications}>
                Send Notifications
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default MfnSafesSection;
