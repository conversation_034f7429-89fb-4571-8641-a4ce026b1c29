import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { Director } from "../types";
import { supabase } from "@/integrations/supabase/client";
import { directorService } from "@/services/directorService";
import { companyService } from "@/services/companyService";

// Handles the core state management for directors
export const useDirectorState = () => {
  const [directors, setDirectors] = useState<Director[]>([]);
  const [boardSize, setBoardSize] = useState(3); // Default board size
  const [loading, setLoading] = useState(false);
  const [companyId, setCompanyId] = useState<string | null>(null);

  // Calculate vacancies
  const vacancies = Math.max(
    0,
    boardSize - directors.filter((d) => !d.resigned).length
  );

  // Load directors data from Supabase
  useEffect(() => {
    const fetchDirectors = async () => {
      setLoading(true);
      try {
        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          console.error("No authenticated user");
          setLoading(false);
          return;
        }

        // Get company details for this user
        const companyDetails = await companyService.getCompanyDetails(user.id);

        if (!companyDetails?.id) {
          console.error("No company found for user");
          setLoading(false);
          return;
        }

        setCompanyId(companyDetails.id);

        // Get directors for this company
        const directorsData = await directorService.getDirectorsForCompany(
          companyDetails.id
        );

        if (directorsData.length > 0) {
          // Map database directors to app directors
          const mappedDirectors: Director[] = directorsData.map((d) => ({
            id: d.id || "",
            name: d.name,
            appointmentDate: new Date(d.appointment_date),
            resigned: d.resigned,
            resignationDate: d.resignation_date
              ? new Date(d.resignation_date)
              : null,
            stockholderApproval: d.stockholder_approval,
          }));

          setDirectors(mappedDirectors);
        } else if (companyDetails) {
          // If no directors but company exists, create default directors
          const defaultDirectors = [
            {
              name: "Jane Smith",
              appointmentDate: new Date(),
              resigned: false,
              resignationDate: null,
              stockholderApproval: false,
            },
            {
              name: "John Doe",
              appointmentDate: new Date(),
              resigned: false,
              resignationDate: null,
              stockholderApproval: false,
            },
          ];

          const savedDirectors = [];

          for (const director of defaultDirectors) {
            const saved = await directorService.addDirector({
              company_id: companyDetails.id,
              name: director.name,
              appointment_date: director.appointmentDate
                .toISOString()
                .split("T")[0],
              resigned: director.resigned,
              resignation_date:
                director.resignationDate?.toISOString().split("T")[0] || null,
              stockholder_approval: director.stockholderApproval,
            });

            if (saved) {
              savedDirectors.push({
                id: saved.id || "",
                name: saved.name,
                appointmentDate: new Date(saved.appointment_date),
                resigned: saved.resigned,
                resignationDate: saved.resignation_date
                  ? new Date(saved.resignation_date)
                  : null,
                stockholderApproval: saved.stockholder_approval,
              });
            }
          }

          setDirectors(savedDirectors);
        }

        // Get board size from company details or use default
        const mockBoardSize = 3;
        setBoardSize(mockBoardSize);
      } catch (error) {
        console.error("Error fetching directors:", error);
        toast({
          title: "Error",
          description: "Failed to load directors data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDirectors();
  }, []);

  return {
    directors,
    setDirectors,
    boardSize,
    loading,
    setLoading,
    vacancies,
    companyId,
  };
};
