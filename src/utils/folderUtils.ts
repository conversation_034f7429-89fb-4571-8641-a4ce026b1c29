export const convertToFolderKey = (path: string): string => {
  return path
    .split('/')
    .map(segment => 
      segment
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-') // Replace any non-alphanumeric chars with hyphen
        .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    )
    .join('/');
};

export const getUploadKey = (folderPath: string, fileName: string): string => {
  const folderKey = convertToFolderKey(folderPath);
  const sanitizedFileName = fileName
    .toLowerCase()
    .replace(/[^a-z0-9.-]/g, '-')
    .replace(/^-+|-+$/g, '');
  return `${folderKey}/${sanitizedFileName}`;
}; 