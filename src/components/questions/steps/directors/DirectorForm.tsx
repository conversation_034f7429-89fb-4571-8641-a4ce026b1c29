import React, { useState, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { OfficerTitle } from "../../types";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import Button from "@/components/common/Button";
import { Trash2, Save, Check } from "lucide-react";
import {
  useDeleteOfficer,
  useUpdateOfficer,
  useAddOfficer,
} from "@/integrations/legal-concierge/hooks";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { Officer } from "../../types/Officer";

interface DirectorFormProps {
  director: Officer;
  index: number;
  onRemove: (id: string) => void;
  onUpdate: (id: string, field: keyof Officer, value: any) => void;
  onToggleOfficerTitle: (id: string, title: OfficerTitle) => void;
  isTitleTakenByOther: (currentId: string, title: OfficerTitle) => boolean;
}

const DirectorForm: React.FC<DirectorFormProps> = ({
  director,
  index,
  onRemove,
  onUpdate,
  onToggleOfficerTitle,
  isTitleTakenByOther,
}) => {
  // Get company info to use for API calls
  const { user } = useAuth();
  const companyId = user?.companyId;

  // Set isDirty to true by default for new officers
  const [isDirty, setIsDirty] = useState(director.isNew || false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Use the delete, update, and add mutations
  const deleteOfficerMutation = useDeleteOfficer();
  const updateOfficerMutation = useUpdateOfficer();
  const addOfficerMutation = useAddOfficer();

  // Check if director has required fields for saving
  const hasRequiredFields = useCallback(() => {
    return (
      director.name.trim() !== "" &&
      director.emailAddress.trim() !== "" &&
      director.contactAddress.trim() !== ""
    );
  }, [director.name, director.emailAddress, director.contactAddress]);

  const onToggleOfficerTitleChange = React.useCallback(
    (id: string, title: OfficerTitle) => {
      setIsDirty(true);
      onToggleOfficerTitle(id, title);
    },
    [onToggleOfficerTitle]
  );

  //setDirty once onChange triggers and then call onUpdate
  const onFieldChange = React.useCallback(
    (id: string, field: keyof Officer, value: any) => {
      setIsDirty(true);
      onUpdate(id, field, value);
    },
    [onUpdate]
  );

  // Handle delete officer
  const handleDeleteOfficer = async () => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      await deleteOfficerMutation.mutateAsync({
        id: director.id,
        companyId,
      });

      // Call the onRemove prop to update the UI
      onRemove(director.id);
      toast.success("Officer deleted successfully");
    } catch (error) {
      console.error("Error deleting officer:", error);
      toast.error("Failed to delete officer");
    }
  };

  // Handle create new officer
  const handleCreateOfficer = async () => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    setIsUpdating(true);

    try {
      // Extract officer titles
      const isCEO = director.isCEO;
      const isPresident = director.isPresident;
      const isSecretary = director.isSecretary;
      const isTreasurer = director.isTreasurer;
      const notApplicable = director.notApplicable;

      await addOfficerMutation.mutateAsync({
        companyId,
        officerId: null,
        name: director.name,
        emailAddress: director.emailAddress,
        contactAddress: director.contactAddress,
        stockOwnership: director.stockOwnership,
        amountOfShares: director.amountOfShares,
        isDirector: director.isDirector,
        isCEO,
        isPresident,
        isSecretary,
        isTreasurer,
        notApplicable,
      });
      // Mark the officer as no longer new after successful creation
      onUpdate(director.id, "isNew" as keyof Officer, false);
      setIsDirty(false);
      toast.success("Officer created successfully");
    } catch (error) {
      console.error("Error creating officer:", error);
      toast.error("Failed to create officer");
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle update officer
  const handleUpdateOfficer = async () => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    setIsUpdating(true);
    try {
      // Extract officer titles
      const isCEO = director.isCEO;
      const isPresident = director.isPresident;
      const isSecretary = director.isSecretary;
      const isTreasurer = director.isTreasurer;
      const notApplicable = director.notApplicable;

      await updateOfficerMutation.mutateAsync({
        id: director.id,
        companyId,
        name: director.name,
        emailAddress: director.emailAddress,
        contactAddress: director.contactAddress,
        stockOwnership: director.stockOwnership,
        amountOfShares: director.amountOfShares,
        isDirector: director.isDirector,
        isCEO,
        isPresident,
        isSecretary,
        isTreasurer,
        notApplicable,
      });

      setIsDirty(false);
      toast.success("Officer updated successfully");
    } catch (error) {
      console.error("Error updating officer:", error);
      toast.error("Failed to update officer");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <h4 className="font-medium">
            {director.name ? director.name : `Person #${index + 1}`}
          </h4>
        </div>
        <div className="flex space-x-2">
          {/* Show Save button for new officers, or Update button for existing officers that have been modified */}
          {(director.isNew || isDirty) && hasRequiredFields() && (
            <Button
              variant="outline"
              size="sm"
              onClick={
                director.isNew ? handleCreateOfficer : handleUpdateOfficer
              }
              icon={<Save size={16} />}
              className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
              loading={isUpdating}
            >
              {director.isNew ? "Save" : "Update"}
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDeleteOfficer}
            icon={<Trash2 size={16} />}
            className="text-red-500 hover:text-red-700 hover:bg-red-50"
          >
            Remove
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor={`name-${director.id}`}>Name</Label>
          <Input
            id={`name-${director.id}`}
            value={director.name}
            onChange={(e) => onFieldChange(director.id, "name", e.target.value)}
            placeholder="Full name"
          />
        </div>

        <div className="space-y-2">
          <Label>Director</Label>
          <RadioGroup
            value={director.isDirector ? "yes" : "no"}
            onValueChange={(value) =>
              onFieldChange(director.id, "isDirector", value === "yes")
            }
            className="flex space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yes" id={`director-yes-${director.id}`} />
              <Label
                htmlFor={`director-yes-${director.id}`}
                className="cursor-pointer"
              >
                Yes
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id={`director-no-${director.id}`} />
              <Label
                htmlFor={`director-no-${director.id}`}
                className="cursor-pointer"
              >
                No
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-2 md:col-span-2">
          <Label>Officer Title</Label>
          <div className="flex flex-wrap gap-4">
            {(
              [
                "CEO",
                "President",
                "Treasurer",
                "Secretary",
                "Not Applicable",
              ] as OfficerTitle[]
            ).map((title) => {
              const isCurrentlyChecked =
                title === "CEO"
                  ? director.isCEO
                  : title === "President"
                    ? director.isPresident
                    : title === "Secretary"
                      ? director.isSecretary
                      : title === "Treasurer"
                        ? director.isTreasurer
                        : title === "Not Applicable"
                          ? director.notApplicable
                          : false;

              const isTakenByOther = isTitleTakenByOther(director.id, title);
              const isDisabled = !isCurrentlyChecked && isTakenByOther;

              return (
                <div key={title} className="flex items-center space-x-2">
                  <Checkbox
                    id={`title-${title}-${director.id}`}
                    checked={isCurrentlyChecked}
                    disabled={isDisabled}
                    onCheckedChange={() =>
                      onToggleOfficerTitleChange(director.id, title)
                    }
                  />
                  <Label
                    htmlFor={`title-${title}-${director.id}`}
                    className={`cursor-pointer ${isDisabled ? "text-gray-400" : ""}`}
                    title={
                      isDisabled
                        ? `${title} is already assigned to another officer`
                        : ""
                    }
                  >
                    {title}
                    {isTakenByOther && !isCurrentlyChecked && (
                      <span className="text-xs text-gray-400 ml-1">
                        (taken)
                      </span>
                    )}
                  </Label>
                </div>
              );
            })}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor={`email-${director.id}`}>Email Address</Label>
          <Input
            id={`email-${director.id}`}
            type="email"
            value={director.emailAddress}
            onChange={(e) =>
              onFieldChange(director.id, "emailAddress", e.target.value)
            }
            placeholder="Email address"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor={`address-${director.id}`}>Contact Address</Label>
          <Input
            id={`address-${director.id}`}
            value={director.contactAddress}
            onChange={(e) =>
              onFieldChange(director.id, "contactAddress", e.target.value)
            }
            placeholder="Address"
          />
        </div>
      </div>
    </div>
  );
};

export default DirectorForm;
