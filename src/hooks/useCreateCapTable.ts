import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";
import {
  CapTableSummary,
  Shareholder,
  ShareholderRole,
} from "@/types/capTable";

export const useCreateCapTable = (
  saveCapTable: (table: CapTableSummary) => Promise<any>
) => {
  const { toast } = useToast();
  const { companyDetails } = useCompanyDetails();

  const createCapTableFromCompanyDetails = async () => {
    if (!companyDetails || !companyDetails.companyName) return null;

    const directors = companyDetails.directors.map((director) => {
      const shares = director.sharesAmount || 0;
      const percentage = director.stockOwnershipPercentage || 0;

      return {
        id: director.id,
        name: director.name,
        role: "Founder" as ShareholderRole,
        holdings: {
          Common: shares,
        },
        totalShares: shares,
        percentage: percentage,
        fullyDilutedPercentage: percentage,
      } as Shareholder;
    });

    const newCapTable: CapTableSummary = {
      id: "company-details-captable",
      name: `${companyDetails.companyName} Cap Table`,
      companyName: companyDetails.companyName,
      totalShares: companyDetails.authorizedShares || 10000000,
      totalFullyDiluted:
        companyDetails.issuedShares ||
        companyDetails.authorizedShares ||
        10000000,
      shareClasses: [{ name: "Common", type: "Common" }],
      shareholders: directors,
      lastUpdated: new Date(),
      snapshots: [{ id: "1", name: "Initial", date: new Date() }],
      isUserProvided: false,
    };

    try {
      await saveCapTable(newCapTable);
      toast({
        title: "Cap table created",
        description:
          "A cap table has been created based on your company details.",
      });
      return newCapTable;
    } catch (error) {
      console.error("Error creating cap table from company details:", error);
      return null;
    }
  };

  const handleCreateNewCapTable = async () => {
    const companyName = companyDetails?.companyName || "New Company";

    const newCapTable: CapTableSummary = {
      name: `${companyName} Cap Table`,
      companyName: companyName,
      totalShares: companyDetails?.authorizedShares || 10000000,
      totalFullyDiluted: companyDetails?.authorizedShares || 10000000,
      shareClasses: [{ name: "Common", type: "Common" }],
      shareholders: [],
      lastUpdated: new Date(),
      snapshots: [{ id: "1", name: "Creation", date: new Date() }],
    };

    await saveCapTable(newCapTable);
  };

  return {
    createCapTableFromCompanyDetails,
    handleCreateNewCapTable,
  };
};
