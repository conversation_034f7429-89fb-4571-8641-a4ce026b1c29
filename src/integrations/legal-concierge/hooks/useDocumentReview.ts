import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { QueryKey } from "../keys";
import {
  DocumentPreparationResponse,
  DocumentSignatureResponse,
  DocumentSignatureSQSResponse,
} from "../types/Document";

const api = new APIClient();

// Type guard functions
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// Hook for preparing documents for signature
export const usePrepareDocumentForSignature = () => {
  const queryClient = useQueryClient();

  return useMutation<DocumentPreparationResponse, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.prepareDocumentForSignature(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate documents queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: [QueryKey().documents, companyId],
      });
    },
  });
};

// Hook for sending documents for signature
export const useSendDocumentForSignature = () => {
  const queryClient = useQueryClient();

  return useMutation<DocumentSignatureResponse, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.sendDocumentForSignature(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate documents queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: [QueryKey().documents, companyId],
      });
    },
  });
};

// Hook for confirming document review
export const useConfirmDocumentReview = () => {
  const queryClient = useQueryClient();

  return useMutation<any, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.confirmReview(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().documents, companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId],
      });
    },
  });
};

// Combined hook for the complete document review workflow
export const useDocumentReviewWorkflow = () => {
  const prepareDocument = usePrepareDocumentForSignature();
  const sendForSignature = useSendDocumentForSignature();
  const confirmReview = useConfirmDocumentReview();

  const executeWorkflow = async (companyId: string) => {
    // Step 1: Prepare documents for signature
    const preparationResult = await prepareDocument.mutateAsync(companyId);

    // Step 2: Send documents for signature
    const signatureResult = await sendForSignature.mutateAsync(companyId);

    // Step 3: Confirm review
    const confirmResult = await confirmReview.mutateAsync(companyId);

    return {
      preparation: preparationResult,
      signature: signatureResult,
      confirmation: confirmResult,
    };
  };

  return {
    executeWorkflow,
    isLoading:
      prepareDocument.isPending ||
      sendForSignature.isPending ||
      confirmReview.isPending,
    error:
      prepareDocument.error || sendForSignature.error || confirmReview.error,
    // Expose individual mutation functions
    prepareDocumentMutation: prepareDocument,
    sendForSignatureMutation: sendForSignature,
    confirmReviewMutation: confirmReview,
    steps: {
      preparation: {
        isLoading: prepareDocument.isPending,
        isSuccess: prepareDocument.isSuccess,
        error: prepareDocument.error,
        data: prepareDocument.data,
      },
      signature: {
        isLoading: sendForSignature.isPending,
        isSuccess: sendForSignature.isSuccess,
        error: sendForSignature.error,
        data: sendForSignature.data,
      },
      confirmation: {
        isLoading: confirmReview.isPending,
        isSuccess: confirmReview.isSuccess,
        error: confirmReview.error,
        data: confirmReview.data,
      },
    },
  };
};

// NEW SQS-based hooks for asynchronous document review workflow

// Hook for sending documents for signature using SQS (asynchronous)
export const useSendDocumentForSignatureSQS = () => {
  const queryClient = useQueryClient();

  return useMutation<DocumentSignatureSQSResponse, Error, string>({
    mutationFn: async (companyId: string) => {
      const response = await api.sendDocumentForSignatureSQS(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (data, companyId) => {
      // Invalidate documents queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: [QueryKey().documents, companyId],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId],
      });
    },
  });
};

// Hook for polling signature status
export const useSignatureStatus = (
  companyId: string,
  enabled: boolean = false,
  refetchInterval: number = 1500 // Poll every 1.5 seconds for more responsive updates
) => {
  return useQuery<number, Error>({
    queryKey: [QueryKey().signatureStatus, companyId],
    queryFn: async () => {
      const timestamp = new Date().toISOString();
      console.log(
        `[useSignatureStatus] ${timestamp} - Polling signature status for company: ${companyId}`
      );

      const response = await api.getSignatureStatus(companyId);
      if (hasError(response)) {
        console.error(`[useSignatureStatus] API error:`, response.error);
        throw new Error(response.error);
      }

      const progress = response.data;
      console.log(
        `[useSignatureStatus] ${timestamp} - Received progress: ${progress}%`
      );

      // Validate progress data
      if (typeof progress !== "number" || progress < 0 || progress > 100) {
        console.warn(
          `[useSignatureStatus] Invalid progress value: ${progress}`
        );
        throw new Error(`Invalid progress value: ${progress}`);
      }

      return response.data;
    },
    enabled,
    refetchInterval: (query) => {
      const currentProgress = query.state.data;
      console.log(
        `[useSignatureStatus] Refetch interval check - progress: ${currentProgress}%, enabled: ${enabled}`
      );

      // Stop polling when progress reaches 100%
      if (currentProgress === 100) {
        console.log(
          `[useSignatureStatus] Progress reached 100%, stopping polling`
        );
        return false;
      }

      // Continue polling if enabled
      return enabled ? refetchInterval : false;
    },
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: true, // Refetch when user returns to tab
    staleTime: 0, // Always consider data stale to ensure fresh polling
    gcTime: 1000 * 60 * 5, // Keep in cache for 5 minutes
    retry: (failureCount, error) => {
      console.log(
        `[useSignatureStatus] Query failed (attempt ${failureCount}):`,
        error
      );
      // Retry up to 3 times for network errors
      return failureCount < 3;
    },
  });
};
