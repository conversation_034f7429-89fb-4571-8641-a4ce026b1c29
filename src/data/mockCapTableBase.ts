import { CapTableSummary } from "@/types/capTable";
import { shareClasses } from "./mockShareClasses";
import { shareholders } from "./mockShareholders";

// Complete cap table data
export const capTableData: CapTableSummary = {
  totalShares: 10000000, // Total outstanding shares
  totalFullyDiluted: 11750000, // Including options
  shareClasses,
  shareholders,
  lastUpdated: new Date("2023-09-15"),
  snapshots: [
    { id: "1", name: "Founding", date: new Date("2022-01-15") },
    { id: "2", name: "Series A", date: new Date("2023-05-10") },
    { id: "3", name: "Current", date: new Date("2023-09-15") },
  ],
};
