import { useState } from "react";

export const useMaintenanceDialogs = () => {
  const [isSharesDialogOpen, setIsSharesDialogOpen] = useState(false);
  const [isOptionPlanDialogOpen, setIsOptionPlanDialogOpen] = useState(false);
  const [isBoardMeetingDialogOpen, setIsBoardMeetingDialogOpen] =
    useState(false);
  const [isAdvisorDialogOpen, setIsAdvisorDialogOpen] = useState(false);
  const [isContractorDialogOpen, setIsContractorDialogOpen] = useState(false);
  const [isEmployeeDialogOpen, setIsEmployeeDialogOpen] = useState(false);
  const [isTerminateDialogOpen, setIsTerminateDialogOpen] = useState(false);
  const [isPromisedGrantsDialogOpen, setIsPromisedGrantsDialogOpen] =
    useState(false);

  return {
    isSharesDialogOpen,
    setIsSharesDialogOpen,
    isOptionPlanDialogOpen,
    setIsOptionPlanDialogOpen,
    isBoardMeetingDialogOpen,
    setIsBoardMeetingDialogOpen,
    isAdvisorDialogOpen,
    setIsAdvisorDialogOpen,
    isContractorDialogOpen,
    setIsContractorDialogOpen,
    isEmployeeDialogOpen,
    setIsEmployeeDialogOpen,
    isTerminateDialogOpen,
    setIsTerminateDialogOpen,
    isPromisedGrantsDialogOpen,
    setIsPromisedGrantsDialogOpen,
  };
};
