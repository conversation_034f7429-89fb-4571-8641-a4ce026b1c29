import React from "react";
import ServiceProviderForm from "../ServiceProviderForm";
import { ServiceProviderType } from "@/types/serviceProvider";

interface DialogFormProps {
  type: ServiceProviderType;
  onSubmit: (data: any) => void;
  hasStockOptionPlan: boolean;
}

const DialogForm: React.FC<DialogFormProps> = ({
  type,
  onSubmit,
  hasStockOptionPlan,
}) => {
  return (
    <ServiceProviderForm
      type={type}
      onSubmit={onSubmit}
      hasStockOptionPlan={hasStockOptionPlan}
    />
  );
};

export default DialogForm;
