"use client";

import { cn } from "@/lib/utils";
import { useEditorStore } from "@/store/use-editor-store";
import {
  AlignCenterIcon,
  AlignJustifyIcon,
  AlignLeftIcon,
  AlignRightIcon,
  MessageSquareIcon,
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

interface AlignmentHoverMenuProps {
  editorRef: React.RefObject<HTMLDivElement>;
}

export const AlignmentHoverMenu = ({ editorRef }: AlignmentHoverMenuProps) => {
  const { editor } = useEditorStore();
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const alignments = [
    { label: "Align Left", value: "left", icon: AlignLeftIcon },
    { label: "Align Center", value: "center", icon: AlignCenterIcon },
    { label: "Align Right", value: "right", icon: AlignRightIcon },
    { label: "Justify", value: "justify", icon: AlignJustifyIcon },
  ];

  const handleAlignment = (value: string) => {
    if (editor) {
      editor.chain().focus().setTextAlign(value).run();
      setIsVisible(false);
    }
  };

  const handleComment = () => {
    if (typeof window !== "undefined" && (window as any).createComment) {
      (window as any).createComment();
      setIsVisible(false);
    }
  };

  const showMenu = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Only show if we have a text selection (not just cursor position)
    if (!editor || !editor.state.selection) return;

    const selection = editor.state.selection;
    if (selection.empty) return; // Don't show for empty selections (just cursor)

    // Only show for text content (paragraphs and headings)
    if (!editor.isActive("paragraph") && !editor.isActive("heading")) {
      return;
    }

    // Get selection coordinates
    const { from, to } = selection;
    const start = editor.view.coordsAtPos(from);
    const end = editor.view.coordsAtPos(to);

    // Calculate position relative to the editor
    const editorElement = editorRef.current;
    if (!editorElement) return;

    const editorRect = editorElement.getBoundingClientRect();
    const x = (start.left + end.left) / 2 - editorRect.left;
    const y = start.top - editorRect.top - 50; // Position above selection

    setPosition({ x, y });
    setIsVisible(true);
  }, [editor, editorRef]);

  const hideMenu = useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, 300); // Small delay to allow moving to menu
  }, []);

  const handleMenuEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleMenuLeave = () => {
    setIsVisible(false);
  };

  useEffect(() => {
    if (!editor) return;

    // Show menu on text selection
    const handleSelectionUpdate = () => {
      showMenu();
    };

    // Hide menu when selection is lost or editor loses focus
    const handleBlur = () => {
      hideMenu();
    };

    editor.on("selectionUpdate", handleSelectionUpdate);
    editor.on("blur", handleBlur);

    return () => {
      editor.off("selectionUpdate", handleSelectionUpdate);
      editor.off("blur", handleBlur);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [editor, showMenu, hideMenu]);

  if (!isVisible || !editor) return null;

  return (
    <div
      ref={menuRef}
      className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-1 flex gap-1"
      style={{
        left: position.x,
        top: position.y,
        transform: "translateX(-50%)",
      }}
      onMouseEnter={handleMenuEnter}
      onMouseLeave={handleMenuLeave}
    >
      {alignments.map(({ label, value, icon: Icon }) => (
        <button
          key={value}
          onClick={() => handleAlignment(value)}
          className={cn(
            "p-2 rounded hover:bg-gray-100 transition-colors",
            editor.isActive({ textAlign: value }) && "bg-gray-200"
          )}
          title={label}
        >
          <Icon className="w-4 h-4" />
        </button>
      ))}

      {/* Separator */}
      <div className="w-px h-6 bg-gray-300 mx-1" />

      {/* Comment Button */}
      <button
        onClick={handleComment}
        className="p-2 rounded hover:bg-gray-100 transition-colors"
        title="Add Comment"
      >
        <MessageSquareIcon className="w-4 h-4" />
      </button>
    </div>
  );
};
