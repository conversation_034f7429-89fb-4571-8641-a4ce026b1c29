import React from "react";
import { cn } from "@/lib/utils";
import { CheckCircle } from "lucide-react";
import { FormStep } from "./types";

interface ProgressIndicatorProps {
  steps: { id: FormStep; label: string }[];
  currentStep: FormStep;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  currentStep,
}) => {
  const currentStepIndex = steps.findIndex((step) => step.id === currentStep);
  return (
    <div className="w-full mb-6">
      <div className="hidden sm:flex items-center justify-between">
        {steps.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isCompleted = index < currentStepIndex;
          const isUpcoming = index > currentStepIndex;

          return (
            <div
              key={step.id}
              className={cn(
                "flex flex-col items-center relative",
                isUpcoming && "opacity-80"
              )}
              style={{ width: `${100 / steps.length}%` }}
            >
              {/* Connecting line */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "absolute h-0.5 top-4 -right-1/2 z-0 transition-colors",
                    isCompleted ? "bg-primary" : "bg-gray-200"
                  )}
                  style={{ width: "100%" }}
                />
              )}
              <div
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold mb-1 z-10",
                  isCompleted
                    ? "bg-primary text-white"
                    : isActive
                      ? "bg-blue-200 text-primary border-2 border-primary"
                      : "bg-gray-100 text-gray-500"
                )}
              >
                {isCompleted ? (
                  <CheckCircle size={16} className="text-white" />
                ) : (
                  index + 1
                )}
              </div>
              <span
                className={cn(
                  "text-xs hidden md:block text-center min-h-8",
                  isActive
                    ? "text-primary font-medium"
                    : isCompleted
                      ? "text-gray-700"
                      : "text-gray-400"
                )}
              >
                {step.label}
              </span>
            </div>
          );
        })}
      </div>

      {/* Mobile progress indicator */}
      <div className="sm:hidden">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">
            Step {currentStepIndex + 1} of {steps.length}
          </span>
          <span className="text-sm text-gray-500">
            {steps[currentStepIndex]?.label ?? currentStep}
          </span>
        </div>
        <div className="mt-2 h-1 w-full bg-gray-200 rounded-full">
          <div
            className="h-1 bg-primary rounded-full"
            style={{
              width: `${((currentStepIndex + 1) / steps.length) * 100}%`,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ProgressIndicator;
