import React from "react";
import MaintenanceItem from "../MaintenanceItem";
import { MaintenanceItemType } from "./types";

interface MaintenanceItemsListProps {
  items: MaintenanceItemType[];
}

const MaintenanceItemsList: React.FC<MaintenanceItemsListProps> = ({
  items,
}) => {
  return (
    <div className="space-y-4">
      {items.map((item, index) => (
        <MaintenanceItem
          key={index}
          icon={item.icon}
          title={item.title}
          description={item.description}
          buttonText={item.buttonText}
          onClick={item.onClick}
          linkTo={item.linkTo}
        />
      ))}
    </div>
  );
};

export default MaintenanceItemsList;
