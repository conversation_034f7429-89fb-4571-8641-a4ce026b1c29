import { v4 as uuidv4 } from "uuid";
import {
  CapTableSummary,
  UserCapTableData,
  CapTableDataService,
} from "@/types/capTable";
import { capTableData } from "@/data/index";

const STORAGE_KEY = "user_cap_table_data";

class LocalStorageCapTableService implements CapTableDataService {
  async loadUserData(): Promise<UserCapTableData> {
    try {
      const storedData = localStorage.getItem(STORAGE_KEY);

      if (storedData) {
        const parsedData = JSON.parse(storedData);

        // Convert date strings back to Date objects
        const processedData = {
          ...parsedData,
          capTables: parsedData.capTables.map((table: any) => ({
            ...table,
            lastUpdated: new Date(table.lastUpdated),
            snapshots: table.snapshots.map((snapshot: any) => ({
              ...snapshot,
              date: new Date(snapshot.date),
            })),
          })),
        };

        return processedData;
      }

      // Initialize with mock data if no user data exists
      const initialData: UserCapTableData = {
        capTables: [
          {
            ...capTableData,
            id: uuidv4(),
            name: "My Cap Table",
            companyName: "Acme Inc.",
            isUserProvided: false,
          },
        ],
        currentCapTableId: undefined,
      };

      await this.saveUserData(initialData);
      return initialData;
    } catch (error) {
      console.error("Error loading user cap table data:", error);
      return { capTables: [] };
    }
  }

  async saveCapTable(capTable: CapTableSummary): Promise<CapTableSummary> {
    try {
      const userData = await this.loadUserData();

      const updatedCapTable = {
        ...capTable,
        id: capTable.id || uuidv4(),
        lastUpdated: new Date(),
        isUserProvided: true,
      };

      const existingIndex = userData.capTables.findIndex(
        (ct) => ct.id === updatedCapTable.id
      );

      if (existingIndex >= 0) {
        userData.capTables[existingIndex] = updatedCapTable;
      } else {
        userData.capTables.push(updatedCapTable);
      }

      userData.currentCapTableId = updatedCapTable.id;
      await this.saveUserData(userData);

      return updatedCapTable;
    } catch (error) {
      console.error("Error saving cap table:", error);
      throw new Error("Failed to save cap table");
    }
  }

  async deleteCapTable(id: string): Promise<boolean> {
    try {
      const userData = await this.loadUserData();

      userData.capTables = userData.capTables.filter((ct) => ct.id !== id);

      if (userData.currentCapTableId === id) {
        userData.currentCapTableId =
          userData.capTables.length > 0 ? userData.capTables[0].id : undefined;
      }

      await this.saveUserData(userData);
      return true;
    } catch (error) {
      console.error("Error deleting cap table:", error);
      return false;
    }
  }

  async getCapTableById(id: string): Promise<CapTableSummary | null> {
    try {
      const userData = await this.loadUserData();
      return userData.capTables.find((ct) => ct.id === id) || null;
    } catch (error) {
      console.error("Error getting cap table by ID:", error);
      return null;
    }
  }

  private async saveUserData(data: UserCapTableData): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error("Error saving user data to localStorage:", error);
      throw error;
    }
  }
}

// Export a singleton instance
export const capTableService = new LocalStorageCapTableService();
