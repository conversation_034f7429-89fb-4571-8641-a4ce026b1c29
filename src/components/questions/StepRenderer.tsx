import React from "react";
import CompanyNameStep from "./steps/CompanyNameStep";
import CompanyAddressStep from "./steps/CompanyAddressStep";
import AuthorizedSharesStep from "./steps/AuthorizedSharesStep";
import DirectorsOfficersSharesStep from "./steps/DirectorsOfficersSharesStep";
import StockPlanStep from "./steps/StockPlanStep";
import VestingStep from "./steps/VestingStep";
import TechStep from "./steps/TechStep";
import RegisteredAgentStep from "./steps/RegisteredAgentStep";
import ReviewStep from "./steps/review/ReviewStep";
import { FormStep, QuestionnaireFormData } from "./types";

interface StepRendererProps {
  currentStep: FormStep;
  formData: QuestionnaireFormData;
  updateFormData: (field: keyof QuestionnaireFormData, value: unknown) => void;
  confirmationChecked?: boolean;
  onConfirmationChange?: (checked: boolean) => void;
  isFormConfirmed?: boolean;
  readOnly?: boolean;
}

const StepRenderer: React.FC<StepRendererProps> = ({
  currentStep,
  formData,
  updateFormData,
  confirmationChecked,
  onConfirmationChange,
  isFormConfirmed = false,
  readOnly = false,
}) => {
  // Create a no-op updateFormData function for read-only mode
  const handleUpdateFormData = readOnly
    ? () => {} // No-op function for read-only mode
    : updateFormData;
  switch (currentStep) {
    case "company-name":
      return (
        <CompanyNameStep
          formData={formData}
          updateFormData={handleUpdateFormData}
        />
      );

    case "company-address":
      return (
        <CompanyAddressStep
          formData={formData}
          updateFormData={handleUpdateFormData}
        />
      );

    case "authorized-shares":
      return (
        <AuthorizedSharesStep
          formData={formData}
          updateFormData={handleUpdateFormData}
        />
      );

    case "directors-and-officers":
      return (
        <DirectorsOfficersSharesStep
          formData={formData}
          updateFormData={handleUpdateFormData}
        />
      );

    case "stock-plan":
      return (
        <StockPlanStep
          formData={formData}
          updateFormData={handleUpdateFormData}
        />
      );

    case "vesting":
      return (
        <VestingStep
          formData={formData}
          updateFormData={handleUpdateFormData}
        />
      );

    case "technology":
      return (
        <TechStep formData={formData} updateFormData={handleUpdateFormData} />
      );

    case "registered-agent":
      return (
        <RegisteredAgentStep
          formData={formData}
          updateFormData={handleUpdateFormData}
        />
      );

    case "review":
      // If the form is already confirmed, show the completion screen
      // if (isFormConfirmed) {
      //   return <FormCompletedStep />;
      // }
      // Otherwise show the regular review step
      return (
        <ReviewStep
          formData={formData}
          confirmationChecked={confirmationChecked}
          onConfirmationChange={readOnly ? () => {} : onConfirmationChange}
        />
      );

    default:
      return <div>Step not found</div>;
  }
};

export default StepRenderer;
