import React from "react";
import { QuestionnaireFormData } from "../../../types";
import { Card, CardContent } from "@/components/common/Card";
import { Check } from "lucide-react";

interface ShareStructureSectionProps {
  formData: QuestionnaireFormData;
}

const ShareStructureSection: React.FC<ShareStructureSectionProps> = ({
  formData,
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-3 flex items-center">
          <Check size={18} className="text-green-500 mr-2" />
          Share Structure
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-500">Authorized Shares</p>
            <p className="font-medium">
              {formData.authorizedShares.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Issued Shares</p>
            <p className="font-medium">
              {formData.issuedShares.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Par Value Per Share</p>
            <p className="font-medium">${formData.parValuePerShare}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShareStructureSection;
