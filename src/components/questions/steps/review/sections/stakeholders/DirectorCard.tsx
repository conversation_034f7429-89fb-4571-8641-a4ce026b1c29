import { Officer } from "@/components/questions/types/Officer";
import React from "react";

interface DirectorCardProps {
  director: Officer;
  index: number;
}

const DirectorCard: React.FC<DirectorCardProps> = ({ director, index }) => {
  return (
    <div className="p-3 border rounded-md">
      <h4 className="font-medium mb-2">
        {director.name || `Person #${index + 1}`}
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <p className="text-sm text-gray-500">Director</p>
          <p>{director.isDirector ? "Yes" : "No"}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Officer Titles</p>
          <p>{director.officerTitles?.join(", ")}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Stock Percentage</p>
          <p>{director.stockOwnership}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Shares</p>
          <p>{director.amountOfShares.toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
};

export default DirectorCard;
