import React from "react";
import { CardFooter } from "@/components/common/Card";
import Button from "@/components/common/Button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { WizardStep } from "./types";

interface WizardFooterProps {
  currentStep: WizardStep;
  loading: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onSubmit: () => void;
}

const WizardFooter: React.FC<WizardFooterProps> = ({
  currentStep,
  loading,
  onPrevious,
  onNext,
  onSubmit,
}) => {
  return (
    <CardFooter className="flex justify-between">
      <Button
        variant="outline"
        onClick={onPrevious}
        disabled={currentStep === "company-name"}
        icon={<ChevronLeft size={16} />}
      >
        Back
      </Button>
      {currentStep === "review" ? (
        <Button onClick={onSubmit} loading={loading}>
          Submit
        </Button>
      ) : (
        <Button
          onClick={onNext}
          icon={<ChevronRight size={16} />}
          iconPosition="right"
        >
          Continue
        </Button>
      )}
    </CardFooter>
  );
};

export default WizardFooter;
