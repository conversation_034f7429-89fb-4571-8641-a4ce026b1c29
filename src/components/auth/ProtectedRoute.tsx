import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Spinner } from "@/components/ui/spinner";
import {
  useUserPermissions,
  UserRole,
} from "@/integrations/legal-concierge/hooks/useUserProfile";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";
import { toast } from "sonner";
import CompanySelectionGuard from "./CompanySelectionGuard";

type ProtectedRouteProps = {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  requiredPermission?: keyof ReturnType<typeof useUserPermissions>;
  redirectTo?: string;
  showAccessDenied?: boolean;
};

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles,
  requiredPermission,
  redirectTo = "/dashboard",
  showAccessDenied = true,
}) => {
  const { isAuthenticated, loading, user, isMfaVerified } = useAuth();
  const location = useLocation();
  const { isCompanySelected, isCompanySelectionLoading } =
    useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);

  // Show loading spinner while checking auth status
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Spinner size="lg" />
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If authenticated but needs MFA, redirect to MFA verification
  if (isAuthenticated && !isMfaVerified) {
    return <Navigate to="/mfa" state={{ from: location }} replace />;
  }

  // If authenticated but company selection is not complete, show company selection guard
  if (isAuthenticated && (!isCompanySelected || isCompanySelectionLoading)) {
    return <CompanySelectionGuard>{children}</CompanySelectionGuard>;
  }

  // Check role-based access if roles are specified
  if (allowedRoles && allowedRoles.length > 0) {
    const hasRequiredRole = allowedRoles.some((role) =>
      user?.roles?.includes(role)
    );
    if (!hasRequiredRole) {
      if (showAccessDenied) {
        toast.error("You don't have permission to access this page");
      }
      return <Navigate to={redirectTo} replace />;
    }
  }

  // Check permission-based access if permission is specified
  if (requiredPermission && !permissions[requiredPermission]) {
    if (showAccessDenied) {
      toast.error("You don't have permission to access this page");
    }
    return <Navigate to={redirectTo} replace />;
  }

  // If authenticated, MFA verified, company selected, and has required permissions, render the protected component
  return <>{children}</>;
};

export default ProtectedRoute;
