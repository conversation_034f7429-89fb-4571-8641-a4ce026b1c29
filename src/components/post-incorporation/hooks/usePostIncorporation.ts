import { useState, useMemo } from "react";
import { Task, StateLink } from "../types";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanyById } from "@/integrations/legal-concierge/hooks/useCompanyInfo";
import {
  useCompleteEIN,
  useCompleteBusinessAccount,
  useCompleteForeignQualification,
  useAddForeignQualificationState,
  useRemoveForeignQualificationState,
  useConfirmPostIncorporation,
} from "@/integrations/legal-concierge/hooks";
import { toast } from "sonner";

// State Secretary of State website links - these would normally come from an API
const STATE_LINKS: Record<string, string> = {
  AL: "https://sos.alabama.gov/business-entities",
  AK: "https://www.commerce.alaska.gov/web/cbpl/Corporations.aspx",
  AZ: "https://azsos.gov/business",
  AR: "https://www.sos.arkansas.gov/business-commercial-services-bcs",
  CA: "https://www.sos.ca.gov/business-programs",
  CO: "https://www.coloradosos.gov/biz/BusinessEntityCriteria.do",
  CT: "https://portal.ct.gov/SOTS/Business-Services/Business-Services",
  DE: "https://corp.delaware.gov/",
  FL: "https://dos.myflorida.com/sunbiz/",
  GA: "https://sos.ga.gov/corporations-division",
  HI: "https://cca.hawaii.gov/breg/",
  ID: "https://sos.idaho.gov/business/",
  IL: "https://www.ilsos.gov/departments/business_services/home.html",
  IN: "https://inbiz.in.gov/BOS/Home/Index",
  IA: "https://sos.iowa.gov/business/index.html",
  KS: "https://sos.ks.gov/business/business.html",
  KY: "https://sos.ky.gov/business/Pages/default.aspx",
  LA: "https://www.sos.la.gov/BusinessServices/Pages/default.aspx",
  ME: "https://www.maine.gov/sos/cec/corp/",
  MD: "https://businessexpress.maryland.gov/",
  MA: "https://www.sec.state.ma.us/cor/coridx.htm",
  MI: "https://www.michigan.gov/sos/0,4670,7-127-1633_1632---,00.html",
  MN: "https://www.sos.state.mn.us/business-liens/",
  MS: "https://www.sos.ms.gov/business-services",
  MO: "https://www.sos.mo.gov/business",
  MT: "https://sosmt.gov/business/",
  NE: "https://www.nebraska.gov/sos/corp/",
  NV: "https://www.nvsos.gov/sos/businesses",
  NH: "https://sos.nh.gov/corporation-division/",
  NJ: "https://www.njportal.com/DOR/BusinessFormation/",
  NM: "https://www.sos.state.nm.us/business-services/",
  NY: "https://dos.ny.gov/business-corporation",
  NC: "https://www.sosnc.gov/divisions/business_registration",
  ND: "https://sos.nd.gov/business",
  OH: "https://www.ohiosos.gov/businesses/",
  OK: "https://www.sos.ok.gov/business/default.aspx",
  OR: "https://sos.oregon.gov/business/Pages/default.aspx",
  PA: "https://www.dos.pa.gov/BusinessCharities/Pages/default.aspx",
  RI: "https://sos.ri.gov/divisions/business-services",
  SC: "https://sos.sc.gov/business-filings",
  SD: "https://sosenterprise.sd.gov/BusinessServices/Business/Default.aspx",
  TN: "https://sos.tn.gov/business-services",
  TX: "https://www.sos.texas.gov/corp/index.shtml",
  UT: "https://corporations.utah.gov/",
  VT: "https://sos.vermont.gov/corporations/",
  VA: "https://scc.virginia.gov/pages/Business-Entity-Resources",
  WA: "https://www.sos.wa.gov/corps/",
  WV: "https://sos.wv.gov/business/Pages/default.aspx",
  WI: "https://www.wdfi.org/corporations/",
  WY: "https://sos.wyo.gov/business/default.aspx",
};

// Initial tasks structure
const INITIAL_TASKS: { [key: string]: Task } = {
  ein: {
    id: "ein",
    title: "Apply for an EIN (Tax ID)",
    description: "Required for taxes, bank accounts, and hiring",
    link: "https://www.irs.gov/businesses/small-businesses-self-employed/apply-for-an-employer-identification-number-ein-online",
    isComplete: false,
    dialogContent:
      "Apply for an EIN in minutes with the IRS via this link. You will need to have this in place in order to open a business bank account and receive payment for stock.",
  },
  bankAccount: {
    id: "bankAccount",
    title: "Open a Business Bank Account",
    description: "Separate your personal and business finances",
    link: "https://www.nerdwallet.com/best/banking/business-bank-accounts",
    isComplete: false,
    dialogContent:
      "Once you have obtained your EIN, you may open a business bank account. Mark as Completed once obtained. You may then have your cofounders pay for their shares to this account.",
  },
  foreignQualification: {
    id: "foreignQualification",
    title: "Request Foreign Qualification to Do Business",
    description: "Register in other states where you conduct business",
    link: "https://www.sba.gov/business-guide/launch-your-business/register-your-business",
    isComplete: false,
    dialogContent:
      "Please select states where you would like to qualify to do business. Note: State registration fees vary based on jurisdiction. Founders Form will inform you of the specific fees applicable to your selected state and appropriate follow up.",
  },
};

export const usePostIncorporation = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  // Fetch company data to get current completion status
  const { data: companyData, isLoading: companyLoading } = useCompanyById(
    companyId || "",
    !!companyId
  );

  // PostIncorporation API hooks
  const completeEIN = useCompleteEIN();
  const completeBusinessAccount = useCompleteBusinessAccount();
  const completeForeignQualification = useCompleteForeignQualification();
  const addForeignQualificationState = useAddForeignQualificationState();
  const removeForeignQualificationState = useRemoveForeignQualificationState();
  const confirmPostIncorporation = useConfirmPostIncorporation();

  // Local state for foreign qualification states (memory only)
  const [selectedStates, setSelectedStates] = useState<StateLink[]>([]);

  // Create tasks object based on company data
  const tasks = useMemo(() => {
    if (!companyData) return INITIAL_TASKS;

    return {
      ein: {
        ...INITIAL_TASKS.ein,
        isComplete: companyData.isEinApplyComplete || false,
      },
      bankAccount: {
        ...INITIAL_TASKS.bankAccount,
        isComplete: companyData.isOpeningBankAccountComplete || false,
      },
      foreignQualification: {
        ...INITIAL_TASKS.foreignQualification,
        isComplete:
          companyData.isForeignQualificationToDoBusinessComplete || false,
      },
    };
  }, [companyData]);

  // Mark a single task as complete using API
  const handleTaskComplete = async (id: string) => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      switch (id) {
        case "ein":
          await completeEIN.mutateAsync(companyId);
          toast.success("EIN task completed successfully!");
          break;
        case "bankAccount":
          await completeBusinessAccount.mutateAsync(companyId);
          toast.success("Business account task completed successfully!");
          break;
        case "foreignQualification":
          await completeForeignQualification.mutateAsync(companyId);
          toast.success("Foreign qualification task completed successfully!");
          break;
        default:
          toast.error("Unknown task");
      }
    } catch (error) {
      toast.error(`Failed to complete ${id} task`);
      console.error(`Error completing ${id}:`, error);
    }
  };

  // Mark all tasks as complete using API
  const handleMarkAllComplete = async () => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      const promises = [];

      if (!companyData?.isEinApplyComplete) {
        promises.push(completeEIN.mutateAsync(companyId));
      }

      if (!companyData?.isOpeningBankAccountComplete) {
        promises.push(completeBusinessAccount.mutateAsync(companyId));
      }

      if (!companyData?.isForeignQualificationToDoBusinessComplete) {
        promises.push(completeForeignQualification.mutateAsync(companyId));
      }

      // Complete all individual tasks first
      await Promise.all(promises);

      // Then confirm the overall post-incorporation completion
      if (!companyData?.isPostIncorporationConfirmed) {
        await confirmPostIncorporation.mutateAsync(companyId);
      }

      toast.success("All post-incorporation tasks completed successfully!");
    } catch (error) {
      toast.error("Failed to complete all tasks");
      console.error("Error completing all tasks:", error);
    }
  };

  // Add a state to the selected states (with API integration)
  const handleAddState = async (stateLink: StateLink) => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      await addForeignQualificationState.mutateAsync({
        companyId,
        state: stateLink.state,
      });
      // Update local state for immediate UI feedback
      setSelectedStates([...selectedStates, stateLink]);
      toast.success(`Added ${stateLink.state} to foreign qualification states`);
    } catch (error) {
      toast.error("Failed to add foreign qualification state");
      console.error("Error adding state:", error);
    }
  };

  // Remove a state from the selected states (with API integration)
  const handleRemoveState = async (stateName: string) => {
    if (!companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      // For API call, we'll use a placeholder ID since we don't have actual state IDs
      const stateId = `state-${stateName}`;

      await removeForeignQualificationState.mutateAsync({
        id: stateId,
        companyId,
      });
      // Update local state for immediate UI feedback
      setSelectedStates(
        selectedStates.filter((item) => item.state !== stateName)
      );
      toast.success(`Removed ${stateName} from foreign qualification states`);
    } catch (error) {
      toast.error("Failed to remove foreign qualification state");
      console.error("Error removing state:", error);
    }
  };

  // Check if all tasks are complete
  const allTasksComplete = useMemo(() => {
    return Object.values(tasks).every((task) => task.isComplete);
  }, [tasks]);

  // Check if any API operations are in progress
  const isLoading =
    completeEIN.isPending ||
    completeBusinessAccount.isPending ||
    completeForeignQualification.isPending ||
    addForeignQualificationState.isPending ||
    removeForeignQualificationState.isPending ||
    confirmPostIncorporation.isPending ||
    companyLoading;

  return {
    tasks,
    selectedStates,
    stateLinks: STATE_LINKS,
    handleTaskComplete,
    handleMarkAllComplete,
    handleAddState,
    handleRemoveState,
    allTasksComplete,
    isLoading,
    companyData,
  };
};
