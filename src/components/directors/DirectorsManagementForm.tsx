import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/common/Card";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { shareholders } from "@/data/mockShareholders";
import { useDirectorsManagement } from "./hooks/useDirectorsManagement";
import BoardSizeDisplay from "./BoardSizeDisplay";
import DirectorsList from "./DirectorsList";
import AddDirectorSection from "./AddDirectorSection";
import { Director } from "@/services/directorService";

const DirectorsManagementForm: React.FC = () => {
  const navigate = useNavigate();

  const {
    directors,
    boardSize,
    loading,
    vacancies,
    addDirector,
    updateDirector,
    generateConsents,
    uploadResignationLetter,
  } = useDirectorsManagement();

  const [isAddingDirector, setIsAddingDirector] = useState(false);
  const [exceedsBoardSize, setExceedsBoardSize] = useState(false);

  const handleSaveChanges = async () => {
    try {
      await generateConsents(exceedsBoardSize);
      toast({
        title: "Changes Saved",
        description: exceedsBoardSize
          ? "Board and stockholder consents have been generated and sent for signatures."
          : "Stockholder consent has been generated and sent for signature.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save changes. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddDirectorClick = () => {
    setIsAddingDirector(true);
    // Check if adding another director would exceed board size
    if (
      directors.length - directors.filter((d) => d.resigned).length >=
      boardSize
    ) {
      setExceedsBoardSize(true);
    } else {
      setExceedsBoardSize(false);
    }
  };

  const handleAddDirector = async (directorData: Partial<Director>) => {
    await addDirector(directorData);
    setIsAddingDirector(false);
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <BoardSizeDisplay
          boardSize={boardSize}
          currentDirectors={directors.filter((d) => !d.resigned).length}
          vacancies={vacancies}
        />

        <h3 className="text-lg font-medium mt-6 mb-3">Current Directors</h3>

        <DirectorsList
          directors={directors}
          onUpdateDirector={updateDirector}
          onUploadResignation={uploadResignationLetter}
        />

        {!isAddingDirector ? (
          <Button onClick={handleAddDirectorClick} className="mt-4">
            Add Director
          </Button>
        ) : (
          <AddDirectorSection
            exceedsBoardSize={exceedsBoardSize}
            onAddDirector={handleAddDirector}
            onCancel={() => setIsAddingDirector(false)}
            vacancies={vacancies}
          />
        )}

        {isAddingDirector && (
          <div className="mt-6 flex justify-end">
            <Button onClick={handleSaveChanges} disabled={loading}>
              Save Changes
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export default DirectorsManagementForm;
