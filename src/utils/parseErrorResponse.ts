export const parseErrorResponse = (errorResponse: any) => {
  console.log(errorResponse);
  if (errorResponse?.data && Array.isArray(errorResponse.data)) {
    const error = errorResponse.data[0];
    if (error.code === "DuplicateUserName") {
      return `The username '${
        error.description.split("'")[1]
      }' is already taken. Please choose a different one.`;
    }
    return error.description || "An error occurred. Please try again.";
  }

  return "An unexpected error occurred. Please try again.";
};
