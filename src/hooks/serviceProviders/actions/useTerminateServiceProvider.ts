import { ServiceProvider } from "@/types/serviceProvider";
import { toast } from "@/hooks/use-toast";
import { serviceProviderService } from "@/services/serviceProviderService";

export function useTerminateServiceProvider(
  serviceProviders: ServiceProvider[],
  setServiceProviders: React.Dispatch<React.SetStateAction<ServiceProvider[]>>
) {
  const terminateServiceProvider = async (id: string): Promise<boolean> => {
    try {
      const success = await serviceProviderService.updateServiceProvider(id, {
        status: "Terminated",
      });

      if (success) {
        setServiceProviders((prev) =>
          prev.map((provider) =>
            provider.id === id
              ? { ...provider, status: "Terminated" }
              : provider
          )
        );

        toast({
          title: "Service Provider Terminated",
          description: "The service provider has been terminated.",
        });
      }

      return !!success;
    } catch (error) {
      console.error("Error terminating service provider:", error);
      toast({
        title: "Error",
        description: "Failed to terminate service provider. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  return terminateServiceProvider;
}
