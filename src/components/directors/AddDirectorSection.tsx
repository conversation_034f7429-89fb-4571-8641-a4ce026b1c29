import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Director } from "@/services/directorService";

interface AddDirectorSectionProps {
  exceedsBoardSize: boolean;
  onAddDirector: (director: Partial<Director>) => Promise<void>;
  onCancel: () => void;
  vacancies: number;
}

const AddDirectorSection: React.FC<AddDirectorSectionProps> = ({
  exceedsBoardSize,
  onAddDirector,
  onCancel,
  vacancies,
}) => {
  const [name, setName] = useState("");
  const [appointmentDate, setAppointmentDate] = useState<Date | undefined>(
    undefined
  );

  const handleSubmit = async () => {
    if (!name || !appointmentDate) {
      alert("Please fill in all fields.");
      return;
    }

    const directorData: Partial<Director> = {
      name: name,
      appointment_date: format(appointmentDate, "yyyy-MM-dd"),
      resigned: false,
      stockholder_approval: !exceedsBoardSize,
    };

    await onAddDirector(directorData);
  };

  return (
    <div className="space-y-4">
      {exceedsBoardSize && (
        <div className="text-red-500">
          Adding this director exceeds the board size. Board and stockholder
          consents are required.
        </div>
      )}
      <div>
        <Label htmlFor="director-name">Director Name</Label>
        <Input
          id="director-name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
      </div>
      <div>
        <Label>Appointment Date</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-[240px] justify-start text-left font-normal",
                !appointmentDate && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {appointmentDate ? (
                format(appointmentDate, "PPP")
              ) : (
                <span>Pick a date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="center">
            <Calendar
              mode="single"
              selected={appointmentDate}
              onSelect={setAppointmentDate}
              disabled={vacancies <= 0}
              initialFocus
            />
          </PopoverContent>
        </Popover>
        {vacancies <= 0 && (
          <p className="text-red-500 text-sm mt-1">
            No board vacancies available.
          </p>
        )}
      </div>
      <div className="flex justify-end space-x-2">
        <Button variant="ghost" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={vacancies <= 0}>
          Add Director
        </Button>
      </div>
    </div>
  );
};

export default AddDirectorSection;
