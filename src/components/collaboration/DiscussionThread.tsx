import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DiscussionMessage } from "./types";
import { formatDistanceToNow } from "date-fns";
import {
  Send,
  MessageSquare,
  Trash,
  Reply,
  Check,
  MoreVertical,
} from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { api } from "@/integrations/legal-concierge/api";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";
import { Badge } from "@/components/ui/badge";

const DiscussionThread: React.FC = () => {
  const [messages, setMessages] = useState<DiscussionMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [replyingTo, setReplyingTo] = useState<DiscussionMessage | null>(null);
  const [loading, setLoading] = useState(true);
  const [showResolved, setShowResolved] = useState(false);

  const { user } = useAuth();
  const { companyDetails } = useCompanyDetails();

  // Add this helper function at the top of the component
  const formatName = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
      .join(' ');
  };

  // Fetch messages from API on component mount
  useEffect(() => {
    const fetchMessages = async () => {
      if (!user?.companyId) return;

      try {
        setLoading(true);
        const response = await api.getDiscussionMessages(user.companyId);

        if (response.error) {
          throw new Error(response.error);
        }

        // Transform the data to match our DiscussionMessage type and sort by timestamp
        const formattedMessages: DiscussionMessage[] = response.data
          .map((message) => {
            const isCurrentUser = message.userId === user.id;
            const userName = isCurrentUser 
              ? (user.fullName ? formatName(user.fullName) : "Anonymous")
              : (message.userName ? formatName(message.userName) : "Anonymous");

            return {
              id: message.id,
              userId: message.userId,
              userName,
              content: message.content || "",
              timestamp: new Date(message.timestamp),
              attachments: message.attachments || [],
              replyTo: message.replyTo,
              resolved: message.resolved,
            };
          })
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()); // Sort by newest first

        setMessages(formattedMessages);
      } catch (error) {
        console.error("Error in fetchMessages:", error);
        toast.error("Failed to load discussion messages");
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [user?.companyId, user?.id, user?.fullName]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user?.companyId) return;

    try {
      const messageData = {
        content: newMessage,
        replyTo: replyingTo ? replyingTo.id : undefined,
        attachments: [], // Currently no UI for attachments, but API supports it
      };

      const response = await api.createDiscussionMessage(user.companyId, messageData);

      if (response.error) {
        throw new Error(response.error);
      }

      // Add the new message to local state at the beginning of the array
      const formattedMessage: DiscussionMessage = {
        id: response.data.id,
        userId: response.data.userId,
        userName: user.fullName ? formatName(user.fullName) : "Anonymous",
        content: response.data.content || "",
        timestamp: new Date(response.data.timestamp),
        attachments: response.data.attachments || [],
        replyTo: response.data.replyTo,
        resolved: response.data.resolved,
      };

      setMessages([formattedMessage, ...messages]); // Add new message at the beginning
      setNewMessage("");
      setReplyingTo(null); // Clear reply state after sending

      toast.success("Message sent successfully");
    } catch (error) {
      console.error("Error in handleSendMessage:", error);
      toast.error("Failed to send message");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleReply = (message: DiscussionMessage) => {
    setReplyingTo(message);
  };

  const handleDelete = async (id: string) => {
    if (!user?.companyId) return;

    try {
      const response = await api.deleteDiscussionMessage(id);

      if (response.error) {
        throw new Error(response.error);
      }

      // Filter out the message with the given id from local state
      setMessages(messages.filter((message) => message.id !== id));
      toast.success("Message deleted successfully");
    } catch (error) {
      console.error("Error in handleDelete:", error);
      toast.error("Failed to delete message");
    }
  };

  const handleResolve = async (id: string) => {
    if (!user?.companyId) return;

    try {
      const message = messages.find(m => m.id === id);
      if (!message) return;

      const response = await api.updateDiscussionMessage(id, {
        content: message.content,
        resolved: true,
        attachments: message.attachments,
      });

      if (response.error) {
        throw new Error(response.error);
      }

      // Mark the message as resolved in local state
      setMessages(
        messages.map((message) => {
          if (message.id === id) {
            return { ...message, resolved: true };
          }
          return message;
        })
      );

      toast.success("Thread resolved");
    } catch (error) {
      console.error("Error in handleResolve:", error);
      toast.error("Failed to resolve thread");
    }
  };

  const handleCancelReply = () => {
    setReplyingTo(null);
  };

  // Group messages by thread (original message + replies)
  const organizeMessagesByThread = () => {
    const threadMap: Record<string, DiscussionMessage[]> = {};

    // First pass - identify root messages (no replyTo) and create threads
    messages.forEach((message) => {
      if (!message.replyTo) {
        // This is a root message
        threadMap[message.id] = [message];
      }
    });

    // Second pass - add replies to their threads
    messages.forEach((message) => {
      if (message.replyTo && threadMap[message.replyTo]) {
        threadMap[message.replyTo].push(message);
      }
    });

    // Filter threads based on showResolved state
    const filteredThreadMap: Record<string, DiscussionMessage[]> = {};
    Object.entries(threadMap).forEach(([key, thread]) => {
      const rootMessage = thread[0];
      if (showResolved || !rootMessage.resolved) {
        filteredThreadMap[key] = thread;
      }
    });

    return filteredThreadMap;
  };

  const threadMap = organizeMessagesByThread();
  const activeThreadCount = Object.values(threadMap)
    .filter(thread => !thread[0].resolved)
    .length;

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            Discussion Thread
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge
              variant="outline"
              className="cursor-pointer hover:bg-gray-100"
              onClick={() => setShowResolved(!showResolved)}
            >
              {showResolved ? "Hide Resolved" : "Show Resolved"}
            </Badge>
            <Badge variant="secondary">{activeThreadCount} Active</Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col max-h-[300px] overflow-y-auto">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-center">
              <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
              <p className="mt-2 text-sm text-gray-500">
                Loading discussions...
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {Object.values(threadMap).length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-8 w-8 text-gray-400 mx-auto" />
                <h3 className="mt-2 text-gray-900 font-medium">
                  No messages yet
                </h3>
                <p className="text-gray-500 text-sm">
                  {showResolved 
                    ? "No messages have been made yet"
                    : "No unresolved messages"}
                </p>
              </div>
            ) : (
              Object.values(threadMap).map((thread) => {
                const rootMessage = thread[0];
                const replies = thread.slice(1);

                return (
                  <div key={rootMessage.id} className="space-y-4">
                    {/* Root message */}
                    <div className="flex">
                      <Avatar className="h-8 w-8 mr-3 flex-shrink-0">
                        <AvatarFallback>
                          {rootMessage.userName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>

                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">
                              {rootMessage.userName}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatDistanceToNow(rootMessage.timestamp, {
                                addSuffix: true,
                              })}
                            </span>
                            {rootMessage.resolved && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                <Check className="w-3 h-3 mr-1" />
                                Resolved
                              </span>
                            )}
                          </div>

                          {user && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 w-7 p-0"
                                >
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => handleReply(rootMessage)}
                                >
                                  <Reply className="mr-2 h-4 w-4" /> Reply
                                </DropdownMenuItem>
                                {!rootMessage.resolved && (
                                  <DropdownMenuItem
                                    onClick={() => handleResolve(rootMessage.id)}
                                  >
                                    <Check className="mr-2 h-4 w-4" /> Resolve
                                  </DropdownMenuItem>
                                )}
                                {rootMessage.userId === user.id && (
                                  <DropdownMenuItem
                                    onClick={() => handleDelete(rootMessage.id)}
                                    className="text-red-600 focus:text-red-600"
                                  >
                                    <Trash className="mr-2 h-4 w-4" /> Delete
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>

                        <p className="text-sm text-gray-800 mt-1">
                          {rootMessage.content}
                          {rootMessage.attachments && rootMessage.attachments.length > 0 && (
                            <div className="mt-2">
                              <div className="text-xs text-gray-500 mb-1">Attachments:</div>
                              <div className="flex flex-wrap gap-2">
                                {rootMessage.attachments.map((attachment, index) => (
                                  <a
                                    key={index}
                                    href={attachment}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-600 hover:text-blue-800 underline"
                                  >
                                    {attachment.split("/").pop()}
                                  </a>
                                ))}
                              </div>
                            </div>
                          )}
                        </p>

                        {user && (
                          <div className="mt-2 flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 text-xs"
                              onClick={() => handleReply(rootMessage)}
                            >
                              <Reply className="h-3 w-3 mr-1" /> Reply
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Replies */}
                    {replies.length > 0 && (
                      <div className="pl-11 space-y-4">
                        {replies.map((reply) => (
                          <div
                            key={reply.id}
                            className="flex border-l-2 border-gray-100 pl-4"
                          >
                            <Avatar className="h-7 w-7 mr-3 flex-shrink-0">
                              <AvatarFallback>
                                {reply.userName
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>

                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium text-sm">
                                    {reply.userName}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {formatDistanceToNow(reply.timestamp, {
                                      addSuffix: true,
                                    })}
                                  </span>
                                </div>

                                {user && (
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-7 w-7 p-0"
                                      >
                                        <MoreVertical className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem
                                        onClick={() => handleReply(rootMessage)}
                                      >
                                        <Reply className="mr-2 h-4 w-4" /> Reply
                                      </DropdownMenuItem>
                                      {reply.userId === user.id && (
                                        <DropdownMenuItem
                                          onClick={() => handleDelete(reply.id)}
                                          className="text-red-600 focus:text-red-600"
                                        >
                                          <Trash className="mr-2 h-4 w-4" />{" "}
                                          Delete
                                        </DropdownMenuItem>
                                      )}
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                )}
                              </div>

                              <p className="text-sm text-gray-800 mt-1">
                                {reply.content}
                                {reply.attachments && reply.attachments.length > 0 && (
                                  <div className="mt-2">
                                    <div className="text-xs text-gray-500 mb-1">Attachments:</div>
                                    <div className="flex flex-wrap gap-2">
                                      {reply.attachments.map((attachment, index) => (
                                        <a
                                          key={index}
                                          href={attachment}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-xs text-blue-600 hover:text-blue-800 underline"
                                        >
                                          {attachment.split("/").pop()}
                                        </a>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-4 pb-4">
        {user ? (
          <div className="w-full space-y-2">
            {replyingTo && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>
                  Replying to <span className="font-medium">{replyingTo.userName}</span>
                </span>
                <span>•</span>
                <button
                  onClick={handleCancelReply}
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Cancel
                </button>
              </div>
            )}
            <div className="flex w-full space-x-2">
              <Input
                placeholder={
                  replyingTo ? "Write your reply..." : "Type your message..."
                }
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1"
              />
              <Button onClick={handleSendMessage} size="icon">
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="w-full text-center py-2 text-gray-500">
            Please log in to participate in discussions
          </div>
        )}
      </CardFooter>
    </Card>
  );
};

export default DiscussionThread;
