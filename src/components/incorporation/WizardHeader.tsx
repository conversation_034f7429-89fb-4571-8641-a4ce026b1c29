import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
} from "@/components/common/Card";
import ProgressSteps from "./ProgressSteps";
import { WizardStep } from "./types";

export const WIZARD_STEPS = [
  { id: "company-name" as <PERSON><PERSON><PERSON>, label: "Company Name" },
  { id: "company-address" as WizardS<PERSON>, label: "Company Address" },
  { id: "review" as WizardStep, label: "Review" },
];

interface WizardHeaderProps {
  currentStep: WizardStep;
}

const WizardHeader: React.FC<WizardHeaderProps> = ({ currentStep }) => {
  const getStepTitle = () => {
    switch (currentStep) {
      case "company-name":
        return "Choose Your Company Name";
      case "company-address":
        return "Enter Company Address";
      case "review":
        return "Review Your Information";
    }
  };

  const getStepDescription = () => {
    switch (currentStep) {
      case "company-name":
        return "Select a unique name for your corporation";
      case "company-address":
        return "Provide the principal address for your business";
      case "review":
        return "Review your company details before proceeding";
    }
  };

  return (
    <CardHeader>
      <ProgressSteps currentStep={currentStep} steps={WIZARD_STEPS} />
      <CardTitle>{getStepTitle()}</CardTitle>
      <CardDescription>{getStepDescription()}</CardDescription>
    </CardHeader>
  );
};

export default WizardHeader;
